'use strict';
module.exports = (sequelize, type) => {
    return sequelize.define('roa_fb_user_data', {
            id: {
                type: type.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            psid: type.STRING,
            full_name: type.STRING,
            phone_no: type.STRING,
            id_number: type.STRING,
            email_address: type.STRING,
            service_request: {
                type: type.STRING,
                allowNull: true,
                defaultValue: null,
            },
            service_request_selected: {
                type: type.STRING,
                allowNull: true,
                defaultValue: null,
            },
            status: type.STRING,
            country: type.STRING,
            country_code: type.STRING
        }, {
            freezeTableName: true,
            timestamps: false
        }
    );
};