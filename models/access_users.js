'use strict';

module.exports = (sequelize, type) => {
    return sequelize.define('access_users', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true

        }, name: type.STRING,

        username: type.STRING,

        password_hash: type.STRING,

        password_reset_token: type.STRING,

        access_type: type.STRING

    },{
        freezeTableName: true,
        timestamps: false
    });
};

