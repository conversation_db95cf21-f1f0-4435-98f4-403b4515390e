'use strict';
const Sequelize = require('sequelize');

module.exports = (sequelize, type) => {
    return sequelize.define('observability_ug', {
        task_id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        platform:type.STRING,
        phone_number: type.STRING,
        country_code:type.STRING,
        user_function: type.STRING,
        time_stamp: {
            type: type.DATE,
            allowNull: true,
            defaultValue: Sequelize.fn('current_timestamp')
          },
    },
    {
        freezeTableName: true,
        timestamps: false
    });
};