'use strict';
module.exports = (sequelize, type) => {
    return sequelize.define('ng_fb_menu', {
            id: {
                type: type.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            response: type.STRING,
            type: type.STRING,
            unique_identifier: type.STRING,
            menu_group: type.STRING,
            has_array: type.BOOLEAN,
            stage_id: type.INTEGER,
            level_id: type.INTEGER,
            require_pin: type.INTEGER,
            trigger_more_services: type.INTEGER,
            product_id: type.INTEGER,
            time_stamp: type.BOOLEAN
        }, {
            freezeTableName: true,
            timestamps: false
        }
    );
};