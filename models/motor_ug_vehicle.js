'use strict';

module.exports = (sequelize, type) => {
    return sequelize.define('MotorUgVehicle',
    {
      id: {
        type: type.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
        ticket_no: {
        type: type.STRING
      },
      make: {
        type: type.STRING,
        allowNull: true,
      },
      model: {
        type: type.STRING,
        allowNull: true,
      },
      registrationNumber: {
        type: type.STRING,
        allowNull: true,
      },
      chassisNumber: {
        type: type.STRING,
        allowNull: true,
      },
      yearOfManufacture: {
        type: type.STRING,
        allowNull: true,
      },
      bodyType: {
        type: type.STRING,
        allowNull: true,
      },
      sittingCapacity: {
        type: type.INTEGER,
        allowNull: true,
      },
      color: {
        type: type.STRING,
        allowNull: true,
      },
      trackerInstalled: {
        type: type.BOOLEAN,
        defaultValue: false,
      },
      alarmInstalled: {
        type: type.BOOLEAN,
        defaultValue: false,
      },
      valuationDone: {
        type: type.BOOLEAN,
        defaultValue: false,
      },
      valueOfCar: {
        type: type.STRING,
        allowNull: true,
      },
        yearsWithLicense: {
        type: type.INTEGER,
        allowNull: true,
      },
      coverStartDate: {
        type: type.STRING,
        allowNull: true,
      },
      proposalDeclinedBefore: {
        type: type.BOOLEAN,
        defaultValue: false,
      },
      specialPremiumBefore: {
        type: type.BOOLEAN,
        defaultValue: false,
      },
      healthIssues: {
        type: type.BOOLEAN,
        defaultValue: false,
      },
      convictionHistory: {
        type: type.BOOLEAN,
        defaultValue: false,
      },
      stickerCollectionPoint: {
        type: type.STRING,
        allowNull: true,
      },
      annualPremium: {
        type: type.DECIMAL(18, 2),
        defaultValue: 0,
      },
      trainingLevies: {
        type: type.DECIMAL(18, 2),
        defaultValue: 0,
      },
      carValue: {
        type: type.DECIMAL(18, 2),
        allowNull: true,
      },
      withinEA: {
        type: type.INTEGER,
        defaultValue: 1,
      },
      excessValue: {
        type: type.DECIMAL,
        allowNull: true,
      },
      carHire: {
        type: type.INTEGER,
        allowNull: true,
      },
      firstApplication: {
        type: type.BOOLEAN,
        allowNull: true,
      },
      carAlarmDiscount: {
        type: type.INTEGER,
        allowNull: true,
      },
      pvt: {
        type: type.INTEGER,
        allowNull: true,
      },
      trackerDiscount: {
        type: type.INTEGER,
        allowNull: true,
      },
      soleOwnership: {
        type: type.BOOLEAN,
        allowNull: true,
      },
      hirePurchaseOrLoanAgreement: {
        type: type.BOOLEAN,
        allowNull: true,
      },
      parkingLocation: {
        type: type.STRING,
        allowNull: true,
      },
      usedForDomesticOrPleasurePurpose: {
        type: type.BOOLEAN,
        allowNull: true,
      },
      usedForBusinessOrProfesionalPurpose: {
        type: type.BOOLEAN,
        allowNull: true,
      },
      usedByEmployeesOrOtherParties: {
        type: type.BOOLEAN,
        allowNull: true,
      },
      usedForCarriage: {
        type: type.BOOLEAN,
        allowNull: true,
      },
      usedForAnyOtherPurpose: {
        type: type.STRING,
        allowNull: true,
      },
      sourceOfFunds: {
        type: type.STRING,
        allowNull: true,
      },
      policyEverCanceled: {
        type: type.BOOLEAN,
        defaultValue: false,
      },
      neededIncreasedPremium :{
        type: type.BOOLEAN,
        defaultValue: false,
      },
      isRareModel: {
        type: type.BOOLEAN,
        defaultValue: false,
      }
    
    }, {
        freezeTableName: true,
        timestamps: false
    });
};