'use strict';
module.exports = (sequelize, type) => {
    return sequelize.define('ng_fb_user_session', {
            id: {
                type: type.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            phone_no: type.STRING,
            psid: type.STRING,
            menu_title: type.STRING,
            fb_name: type.STRING,
            fb_profile_pic_url: type.STRING,
            unique_identifier: type.STRING
        }, {
            freezeTableName: true,
            timestamps: false
        }
    );
};