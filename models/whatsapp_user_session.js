'use strict';

module.exports = (sequelize, type) => {
    return sequelize.define('whatsapp_user_session', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement:true
        },
        phone_no: type.STRING,
        level: type.INTEGER,
        whatsapp_name: type.STRING,
        country: type.STRING,
        country_id: type.INTEGER,
        input: type.STRING,
        unique_identifier: type.STRING,
        expected_input_type: type.STRING,
        target_product: type.STRING,
        path: type.STRING
    }, {
        freezeTableName: true,
        timestamps: false
    });
};