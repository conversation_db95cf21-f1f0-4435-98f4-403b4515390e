'use strict';
module.exports = (sequelize, type) => {
    return sequelize.define('ng_fb_faq', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
            unique_identifier: type.STRING,
            ng_fb_menu_id: type.INTEGER,
            question: type.STRING,
            answer: type.STRING,
            display: type.BOOLEAN,
        }, {
            freezeTableName: true,
            timestamps: false
        }
    );
};