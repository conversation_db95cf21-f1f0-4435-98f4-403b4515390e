'use strict';

module.exports = (sequelize, type) => {
    return sequelize.define('wa_user_session', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        user_id:type.INTEGER,
        phone_no: type.STRING,
        ticket_no:type.STRING,
        level: type.INTEGER,
        input: type.STRING,
        unique_identifier: type.STRING,
        expected_input_type: type.STRING,
        path: type.STRING,
        status:type.STRING
    }, {
        freezeTableName: true,
        timestamps: false
    });
};
