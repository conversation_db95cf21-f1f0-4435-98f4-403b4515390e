let config = require('config');

module.exports = (sequelize, type) => {
    const BotUserModel = sequelize.define('session_level', {
            id: {
                type: type.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            psid: type.STRING,
            fb_name: type.STRING,
            silica_name: type.STRING,
            fb_profile_pic_url: type.STRING,
            phone_no: type.STRING,
            national_id: type.STRING,
            passport: type.STRING,
            postal_address: type.STRING,
            email: type.STRING,
            occupation: type.STRING,
            physical_address: type.STRING,
            funds_source: type.STRING,
            kra_pin: type.STRING,
            tnc_accepted: type.STRING,
            menu_group: type.STRING,
            unique_identifier: type.STRING,
            next_block: type.INTEGER,
            stage: type.INTEGER,
            current_level: type.INTEGER,
            next_level: type.INTEGER,
            is_existing_user: type.BOOLEAN,
            is_onboarding_complete: type.BOOLEAN,
            is_using_temp_pin: type.B<PERSON><PERSON><PERSON>N,
            is_phone_no_verified: type.BOOLEAN,
            has_ongoing_transaction: type.BOOLEAN,
            silica_id: type.INTEGER,
            user_declared_existence: type.BO<PERSON>EAN,
            lead_source: type.STRING,
            onboarding_agent_code: type.STRING,
            client_phone_no: type.STRING,
            otp_id: type.INTEGER,
            user_unique_identifier: type.STRING
        }, {
            freezeTableName: true
        }
    );
    /*  Otp.associate = (models) => {
          Otp.belongsTo(models.author);
      };
  */
    return BotUserModel;
};