'use strict';
module.exports = (sequelize, type) => {
    return sequelize.define('roa_fb_menu_ug', {
            id: {
                type: type.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            response: type.STRING,
            type: type.STRING,
            unique_identifier: type.STRING,
            menu_group: type.STRING,
            has_array: type.BOOLEAN,
            stage_id: type.INTEGER,
            level_id: type.INTEGER,
            require_pin: type.INTEGER,
            trigger_more_services: type.INTEGER,
            product_id: type.INTEGER,
            triggers_kyc: type.BOOLEAN,
        created_at: type.STRING,
        updated_at: type.STRING
        }, {
            freezeTableName: true,
            timestamps: false
        }
    );
};