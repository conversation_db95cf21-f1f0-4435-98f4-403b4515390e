'use strict';

module.exports = (sequelize, type) => {
    return sequelize.define('wa_health_worker', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        key: type.STRING,
        value: type.STRING,
        phone_no: type.STRING,
        status: type.STRING
    }, {
        freezeTableName: true,
        timestamps: false
    });
};
