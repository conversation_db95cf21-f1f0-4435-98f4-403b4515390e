/* jshint indent: 2 */

const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('ng_session', {
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    channel_id: {
      type: DataTypes.TINYINT,
      allowNull: true,
      defaultValue: 0
    },
    path_taken: {
      type: DataTypes.STRING(500),
      allowNull: true,
      defaultValue: "0"
    },
    source_detail: {
      type: DataTypes.STRING(50),
      allowNull: true,
      defaultValue: "0",
      comment: "USSD Code or Aggregator"
    },
    session_id: {
      type: DataTypes.STRING(200),
      allowNull: true,
      defaultValue: "0"
    },
    date_created: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.fn('current_timestamp')
    },
    date_updated: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.fn('current_timestamp')
    }
  }, {
    sequelize,
    tableName: 'ng_session',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
};
