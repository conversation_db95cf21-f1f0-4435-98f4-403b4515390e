'use strict';

module.exports = (sequelize, type) => {
    return sequelize.define('MotorUgCustomer',
    {
      id: {
        type: type.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      ticket_no: {
        type: type.STRING
      },
      fullName: {
        type: type.STRING,
        allowNull: true,
      },
      idNumber: {
        type: type.STRING,
        allowNull: true,
      },
      emailAddress: {
        type: type.STRING,
        allowNull: true,
      },
      gender: {
        type: type.STRING,
        allowNull: true,
      },
      dateOfBirth: {
        type: type.DATE,
        allowNull: true,
      },
      phoneNumber: {
        type: type.STRING,
        allowNull: true,
      },
      nationality: {
        type: type.STRING,
        allowNull: true,
      },
      occupation: {
        type: type.STRING,
        allowNull: true,
      },
      postalAddress: {
        type: type.STRING,
        allowNull: true,
      },
      townOrCity: {
        type: type.STRING,
        allowNull: true,
      },
      maritalStatus: {
        type: type.STRING,
        allowNull: true,
      },
      residentialStatus: {
        type: type.STRING,
        allowNull: true,
      },
      nextOfKinNames: {
        type: type.STRING,
        allowNull: true,
      },
      nextOfKinEmail: {
        type: type.STRING,
        allowNull: true,
      },
      nextOfKinPostalAddress: {
        type: type.STRING,
        allowNull: true,
      },
      nextOfKinRelationship: {
        type: type.STRING,
        allowNull: true,
      },
      industryOrSector: {
        type: type.STRING,
        allowNull: true,
      },
      contractEndDate: {
        type: type.DATE,
        allowNull: true,
      },
      employmentNumber: {
        type: type.STRING,
        allowNull: true,
      },
      salaryRange: {
        type: type.STRING,
        allowNull: true,
      },
      employerName: {
        type: type.STRING,
        allowNull: true,
      },
      schemeName: {
        type: type.STRING,
        allowNull: true,
      },
      onboardingStep: {
        type: type.STRING,
        allowNull: true,
      },
      transactionStatus: {
        type: type.STRING,
        allowNull: true,
      },
      transactionReference: {
        type: type.STRING,
        allowNull: true,
      },
      transactionAmount: {
        type: type.STRING,
        allowNull: true,
      },
      paymentMethod: {
        type: type.STRING,
        allowNull: true,
      },
      paymentNetwork: {
        type: type.STRING,
        allowNull: true,
      },
      paymentNumber:{
        type: type.STRING,
        allowNull: true,
      },
      bank:{
        type: type.STRING,
        allowNull: true,
      },
      created_at: {
            type: type.DATE,
            defaultValue: type.NOW
      },
      updated_at: {
          type: type.DATE,
          defaultValue: type.NOW
      }
    }, {
        freezeTableName: true,
        timestamps: false
    });
};