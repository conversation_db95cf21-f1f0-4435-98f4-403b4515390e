/* jshint indent: 2 */

const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('ng_user', {
    user_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    last_name: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    first_name: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    sur_name: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    full_name: {
      type: DataTypes.STRING(150),
      allowNull: true
    },
    gender: {
      type: DataTypes.ENUM('Male','Female'),
      allowNull: true
    },
    registration_document_type_id: {
      type: DataTypes.TINYINT,
      allowNull: false,
      references: {
        model: 'ng_registration_document_type',
        key: 'id'
      }
    },
    registration_no: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    email: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    registration_status: {
      type: DataTypes.ENUM('PENDING','COMPLETE'),
      allowNull: true
    },
    registration_status_desc: {
      type: DataTypes.STRING(200),
      allowNull: true
    },
    phone_no: {
      type: DataTypes.STRING(20),
      allowNull: true,
      unique: "phone_no"
    },
    existing_user: {
      type: DataTypes.TINYINT,
      allowNull: true
    },
    user_confirmed_details: {
      type: DataTypes.TINYINT,
      allowNull: true
    },
    authentication_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'ng_user_auth',
        key: 'id'
      }
    },
    date_created: {
      type: DataTypes.DATE,
      allowNull: true
    },
    date_updated: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'ng_user',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "user_id" },
        ]
      },
      {
        name: "phone_no",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "phone_no" },
        ]
      },
      {
        name: "registration_document_type_id",
        using: "BTREE",
        fields: [
          { name: "registration_document_type_id" },
        ]
      },
      {
        name: "FK_ng_user_ng_user_auth",
        using: "BTREE",
        fields: [
          { name: "authentication_id" },
        ]
      },
    ]
  });
};
