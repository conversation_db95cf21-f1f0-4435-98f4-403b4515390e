'use strict';

module.exports = (sequelize, type) => {
    return sequelize.define('wa_session_data', {
        session_id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        ticket_no: type.STRING,
        key: type.STRING,
        session_data: type.STRING,
        status: type.STRING,
        phone_no: type.STRING
    }, {
        freezeTableName: true,
        timestamps: false
    });
};
