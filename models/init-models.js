var DataTypes = require("sequelize").DataTypes;
var _access_channel = require("./access_channel");
var _ng_bank = require("./ng_bank");
var _ng_currency = require("./ng_currency");
var _ng_payment_status = require("./ng_payment_status");
var _ng_payment_vendor = require("./ng_payment_vendor");
var _ng_product_transaction = require("./ng_product_transaction");
var _ng_products = require("./ng_products");
var _ng_registration_document_type = require("./ng_registration_document_type");
var _ng_session = require("./ng_session");
var _ng_user = require("./ng_user");
var _ng_user_auth = require("./ng_user_auth");

var _wa_customer_data = require("./wa_customer_data")
var _wa_requests = require("./wa_requests")
var _wa_session_data = require("./wa_session_data")
var _wa_user_files = require("./wa_user_files")
var _wa_user_session = require("./wa_user_session")
var _wa_user = require("./wa_user")
var _whatsapp_user_session = require("./whatsapp_user_session")
var _user_session = require("./user_session")
var _wa_health_worker = require("./wa_health_worker")
var _observability = require("./observability")
var _motor_ug_customer = require("./motor_ug_customer")
var _motor_ug_vehicle = require("./motor_ug_vehicle")

function initModels(sequelize) {
    var access_channel = _access_channel(sequelize, DataTypes);
    var ng_bank = _ng_bank(sequelize, DataTypes);
    var ng_currency = _ng_currency(sequelize, DataTypes);
    var ng_payment_status = _ng_payment_status(sequelize, DataTypes);
    var ng_payment_vendor = _ng_payment_vendor(sequelize, DataTypes);
    var ng_product_transaction = _ng_product_transaction(sequelize, DataTypes);
    var ng_products = _ng_products(sequelize, DataTypes);
    var ng_registration_document_type = _ng_registration_document_type(sequelize, DataTypes);
    var ng_session = _ng_session(sequelize, DataTypes);
    var ng_user = _ng_user(sequelize, DataTypes);
    var ng_user_auth = _ng_user_auth(sequelize, DataTypes);

    var wa_customer_data = _wa_customer_data(sequelize, DataTypes)
    var wa_requests = _wa_requests(sequelize, DataTypes)
    var wa_session_data = _wa_session_data(sequelize, DataTypes)
    var wa_user_files = _wa_user_files(sequelize, DataTypes)
    var wa_user_session = _wa_user_session(sequelize, DataTypes)
    var wa_user = _wa_user(sequelize, DataTypes)
    var whatsapp_user_session = _whatsapp_user_session(sequelize, DataTypes)
    var user_session = _user_session(sequelize, DataTypes)
    var wa_health_worker = _wa_health_worker(sequelize, DataTypes)
    var observability = _observability(sequelize, DataTypes)
    var motor_ug_customer = _motor_ug_customer(sequelize, DataTypes)
    var motor_ug_vehicle = _motor_ug_vehicle(sequelize, DataTypes)

    ng_product_transaction.belongsTo(ng_currency, { foreignKey: "currency_id" });
    ng_currency.hasMany(ng_product_transaction, { foreignKey: "currency_id" });
    ng_product_transaction.belongsTo(ng_bank, { foreignKey: "bank_id" });
    ng_bank.hasMany(ng_product_transaction, { foreignKey: "bank_id" });
    ng_product_transaction.belongsTo(access_channel, { foreignKey: "channel_id" });
    access_channel.hasMany(ng_product_transaction, { foreignKey: "channel_id" });
    ng_product_transaction.belongsTo(ng_user, { foreignKey: "user_id" });
    ng_user.hasMany(ng_product_transaction, { foreignKey: "user_id" });
    ng_product_transaction.belongsTo(ng_payment_status, { foreignKey: "payment_status_id" });
    ng_payment_status.hasMany(ng_product_transaction, { foreignKey: "payment_status_id" });
    ng_product_transaction.belongsTo(ng_products, { foreignKey: "product_id" });
    ng_products.hasMany(ng_product_transaction, { foreignKey: "product_id" });
    ng_product_transaction.belongsTo(ng_payment_vendor, { foreignKey: "payment_vendor_id" });
    ng_payment_vendor.hasMany(ng_product_transaction, { foreignKey: "payment_vendor_id" });
    ng_product_transaction.belongsTo(ng_session, { foreignKey: "session_id" });
    ng_session.hasMany(ng_product_transaction, { foreignKey: "session_id" });
    ng_user.belongsTo(ng_registration_document_type, { foreignKey: "registration_document_type_id" });
    ng_registration_document_type.hasMany(ng_user, { foreignKey: "registration_document_type_id" });
    ng_user.belongsTo(ng_user_auth, { foreignKey: "authentication_id" });
    ng_user_auth.hasMany(ng_user, { foreignKey: "authentication_id" });
    ng_user_auth.belongsTo(ng_user, { foreignKey: "user_id" });
    ng_user.hasMany(ng_user_auth, { foreignKey: "user_id" });

    return {
        access_channel,
        ng_bank,
        ng_currency,
        ng_payment_status,
        ng_payment_vendor,
        ng_product_transaction,
        ng_products,
        ng_registration_document_type,
        ng_session,
        ng_user,
        ng_user_auth,
        wa_customer_data,
        wa_requests,
        wa_session_data,
        wa_user_files,
        wa_user_session,
        wa_user,
        whatsapp_user_session,
        user_session,
        wa_health_worker,
        observability,
        motor_ug_customer,
        motor_ug_vehicle
    };
}
module.exports = initModels;
module.exports.initModels = initModels;
module.exports.default = initModels;