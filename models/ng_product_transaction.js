/* jshint indent: 2 */

const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('ng_product_transaction', {
    transaction_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    reference_no_internal: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: "0"
    },
    reference_no_external: {
      type: DataTypes.STRING(50),
      allowNull: true,
      defaultValue: "0"
    },
    amount: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0
    },
    currency_id: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      defaultValue: 0,
      references: {
        model: 'ng_currency',
        key: 'id'
      }
    },
    paying_phone_number: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: "0"
    },
    bank_id: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      defaultValue: 0,
      references: {
        model: 'ng_bank',
        key: 'id'
      }
    },
    channel_id: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 0,
      references: {
        model: 'access_channel',
        key: 'id'
      }
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      references: {
        model: 'ng_user',
        key: 'user_id'
      }
    },
    payment_status_id: {
      type: DataTypes.TINYINT,
      allowNull: true,
      defaultValue: 0,
      references: {
        model: 'ng_payment_status',
        key: 'id'
      }
    },
    payment_status_desc: {
      type: DataTypes.STRING(50),
      allowNull: true,
      defaultValue: "0"
    },
    product_id: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      defaultValue: 0,
      references: {
        model: 'ng_products',
        key: 'id'
      }
    },
    payment_payload_requested: {
      type: DataTypes.STRING(200),
      allowNull: true,
      defaultValue: "0"
    },
    payment_payload_received: {
      type: DataTypes.STRING(400),
      allowNull: true,
      defaultValue: "0"
    },
    payment_request_time: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: Sequelize.fn('current_timestamp')
    },
    payment_response_time: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: Sequelize.fn('current_timestamp')
    },
    payment_vendor_id: {
      type: DataTypes.SMALLINT,
      allowNull: true,
      defaultValue: 0,
      references: {
        model: 'ng_payment_vendor',
        key: 'id'
      }
    },
    product_option: {
      type: DataTypes.STRING(50),
      allowNull: true,
      defaultValue: "0"
    },
    session_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
      references: {
        model: 'ng_session',
        key: 'id'
      }
    },
    policy_reference_number: {
      type: DataTypes.STRING(50),
      allowNull: true,
      defaultValue: "0"
    },
    policy_details: {
      type: DataTypes.STRING(50),
      allowNull: true,
      defaultValue: "0",
      comment: "Contain additional Policy info e.g. licence number for motor cover"
    },
    pas_sync_status: {
      type: DataTypes.ENUM('PENDING','ON-GOING','SUCCESS','FAIL','TIMEOUT','SENT_VIA_EMAIL'),
      allowNull: true,
      defaultValue: "PENDING"
    },
    pas_sync_status_desc: {
      type: DataTypes.STRING(200),
      allowNull: true,
      defaultValue: "0"
    },
    pas_sync_time: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: Sequelize.fn('current_timestamp')
    },
    date_updated: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.fn('current_timestamp')
    },
    date_created: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.fn('current_timestamp')
    }
  }, {
    sequelize,
    tableName: 'ng_product_transaction',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "transaction_id" },
        ]
      },
      {
        name: "payment_vendor_id",
        using: "BTREE",
        fields: [
          { name: "payment_vendor_id" },
        ]
      },
      {
        name: "product_id",
        using: "BTREE",
        fields: [
          { name: "product_id" },
        ]
      },
      {
        name: "FK_ng_product_transaction_ng_session",
        using: "BTREE",
        fields: [
          { name: "session_id" },
        ]
      },
      {
        name: "FK_ng_product_transaction_access_channel",
        using: "BTREE",
        fields: [
          { name: "channel_id" },
        ]
      },
      {
        name: "FK_ng_product_transaction_ng_bank",
        using: "BTREE",
        fields: [
          { name: "bank_id" },
        ]
      },
      {
        name: "FK_ng_product_transaction_ng_currency",
        using: "BTREE",
        fields: [
          { name: "currency_id" },
        ]
      },
      {
        name: "FK_ng_product_transaction_ng_user",
        using: "BTREE",
        fields: [
          { name: "user_id" },
        ]
      },
      {
        name: "FK_ng_product_transaction_ng_payment_status",
        using: "BTREE",
        fields: [
          { name: "payment_status_id" },
        ]
      },
    ]
  });
};
