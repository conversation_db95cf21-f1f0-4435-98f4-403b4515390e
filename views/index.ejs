
<link href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.1/css/bootstrap.min.css" rel="stylesheet" id="bootstrap-css">
<script src="//maxcdn.bootstrapcdn.com/bootstrap/4.1.1/js/bootstrap.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<!------ Include the above in your HEAD tag ---------->

<!doctype html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Fonts -->
    <link rel="dns-prefetch" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css?family=Raleway:300,400,600" rel="stylesheet" type="text/css">

    <link rel="stylesheet" href="css/style.css">

    <link rel="icon" href="Favicon.png">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css">

    <title>i-INVEST</title>
</head>

<br>
<main class="login-form">
    <div class="container">
        <div class="row justify-content-center">

            <body>
            <% if (message.length > 0) { %>
            <div class="alert alert-danger col-sm-12"><%= message %></div>
            <% } %>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">Login</div>
                    <div class="card-body">
                        <form action="<%= url %>" method="POST">
                            <div class="form-group row">
                                <label for="phone_no" class="col-md-4 col-form-label text-md-right">Phone No</label>
                                <div class="col-md-6">
                                    <input id="login-phone_no" type="text" class="form-control" name="phone_no" value=""
                                           placeholder="Phone No e.g 0722100***" required autofocus>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="password" class="col-md-4 col-form-label text-md-right">Password</label>
                                <div class="col-md-6">
                                    <input id="login-password" type="password" class="form-control" name="password"
                                           placeholder="Enter PIN number" required >
                                    <input id="token" type="hidden" class="form-control" name="token" value=<%= token %>>
                                </div>
                            </div>

                            <br/>
                            <div class="col-md-5 offset-md-4">
                                <!--<button id="btn-login" type="submit" class="btn btn-success btn-lg btn-block">Login</button>-->
                                <button class="btn btn-success btn-lg btn-block" type="submit">Login</button>
                            </div>
                            <%= url %>
                    </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

</main>
</body>
</html>

<!--
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Sample Site</title>
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/3.3.1/css/bootstrap.min.css"/>
    <link href="/css/style.css" rel="stylesheet" type="text/css">

</head>
<body id="clrblk">
<div class="container-fluid">
        <div class="panel panel-info">
            <div class="panel-heading">
                <div class="panel-title">Message</div>
            </div>

            <div style="padding-top:30px" class="panel-body">
                <% if (message.length > 0) { %>
                <div class="alert alert-danger col-sm-12"><%= message %></div>
                <% } %>

                <form id="loginform" class="form-horizontal" role="form" method="post" action="/login/<%= token %>">

                    <div style="margin-bottom: 25px" class="input-group">
                        <span class="input-group-addon"><i class="glyphicon glyphicon-user"></i></span>
                        <input id="login-phone_no" type="text" class="form-control" name="phone_no" value=""
                               placeholder="Phone No e.g +************">
                    </div>

                    <div style="margin-bottom: 25px" class="input-group">
                        <span class="input-group-addon"><i class="glyphicon glyphicon-lock"></i></span>
                        <input id="login-password" type="password" class="form-control" name="password"
                               placeholder="Enter PIN number">
                    </div>

                    <div style="margin-bottom: 25px" class="input-group">
                        <span class="input-group-addon"><i class="glyphicon glyphicon-lock"></i></span>
                        <input id="token" type="text" class="form-control" name="token" value=<%= token %>>
                    </div>

                    <div style="margin-top:10px" class="form-group">
                        &lt;!&ndash; Button &ndash;&gt;

                        <div class="col-sm-12 controls">
                            <button id="btn-login" type="submit" class="btn btn-success">Login</button>


                        </div>
                    </div>

                </form>

            </div>
        </div>

</div>
</body>
</html>
-->
