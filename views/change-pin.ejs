<html>
<head>

    <title>i-INVEST</title>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Fonts -->
    <link rel="dns-prefetch" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css?family=Raleway:300,400,600" rel="stylesheet" type="text/css">

    <link rel="stylesheet" href="css/style.css">

    <link rel="icon" href="Favicon.png">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css">


</head>
<body>

<!------ Include the above in your HEAD tag ---------->

<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.0.8/css/all.css">

<script>
    (function (d, s, id) {
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {
            return;
        }
        js = d.createElement(s);
        js.id = id;
        js.src = "//connect.facebook.net/en_US/messenger.Extensions.js";
        fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'Messenger'));
</script>

<br>
<main class="login-form">
    <div class="container">

        <div class="row justify-content-center">

            <body>
            <% if (message.length > 0) { %>
            <div class="alert alert-danger col-sm-12"><%= message %></div>
            <% } %>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">Change your PIN</div>
                    <div class="card-body">
                        <form action="<%= url %>" method="POST">

                            <div class="form-group row">
                                <label for="phone_no" class="col-md-4 col-form-label text-md-right">Enter current
                                    PIN</label>
                                <div class="col-md-6">
                                    <input id="entered_current_pin" name="entered_current_pin" class="form-control"
                                           placeholder="******"
                                           type="password" required
                                           autofocus>
                                </div>
                            </div>
                            <br/>
                            <div class="form-group row">
                                <label for="phone_no" class="col-md-4 col-form-label text-md-right">New PIN</label>
                                <div class="col-md-6">
                                    <input id="entered_pin" name="entered_pin" class="form-control" placeholder="******"
                                           type="password"
                                           required>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="password" class="col-md-4 col-form-label text-md-right">Confirm PIN</label>
                                <div class="col-md-6">
                                    <input id="confirm_pin" name="confirm_pin" class="form-control" placeholder="******"
                                           type="password"
                                           required>
                                    <input id="token" type="hidden" class="form-control" name="token"
                                           value=<%= token %>
                                </div>
                            </div>

                            <br/>
                            <br/>
                            <div class="col-md-5 offset-md-4">
                                <!--<button id="btn-login" type="submit" class="btn btn-success btn-lg btn-block">Login</button>-->
                                <button class="btn btn-success btn-lg btn-block" type="submit">Confirm</button>
                            </div>

                    </div>
                    </form>
                </div>
            </div>
        </div>


    </div>
</main>
<!--container end.//-->


</body>


<!--
<div class="row justify-content-center">
    <h4 align="center">Enter your new PIN</h4>
    <form role="form" method="post" action="/change-pin/<%= token %>">
        <div class="form-group">
            <div class="form-group">
                <label>Enter new PIN</label>
                <input id="entered_pin" name="entered_pin" class="form-control" placeholder="******"
                       type="password"
                       required>
            </div> &lt;!&ndash; form-group// &ndash;&gt;
            <div class="form-group">
                <label>Confirm PIN</label>
                <input id="confirm_pin" name="confirm_pin" class="form-control" placeholder="******"
                       type="password"
                       required>
            </div>
            <div class="form-group">
                <label>Token</label>
                <input id="token" name="token" class="form-control" value="<%= token %>" type="hidden" required>
            </div> &lt;!&ndash; form-group// &ndash;&gt;
            <br/>
            &lt;!&ndash;<div class="form-group">
                <div class="checkbox">
                    <label> <input type="checkbox"> Save password </label>
                </div> &lt;!&ndash; checkbox .// &ndash;&gt;
            </div> &lt;!&ndash; form-group// &ndash;&gt;&ndash;&gt;
            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-block">Confirm</button>
                <br/>
            </div> &lt;!&ndash; form-group// &ndash;&gt;

            <% if (error != null && !error) { %>
            <div class="alert alert-info" role="alert">
                <%= message %>
            </div>
            <% } else if(error !== null && error){ %>
            <div class="alert alert-danger" role="alert">
                <%= message %>
            </div>
            <% } %>
        </div>
    </form>

</div>-->
