let createError = require('http-errors'),
    express = require('express'),
    path = require('path'),
    cookieParser = require('cookie-parser'),
    logger = require('morgan'),
    helmet = require('helmet');
const session = require('express-session')
const dotenv = require('dotenv')



var indexRouter = require('./routes/index');
var usersRouter = require('./routes/users');
var ussdRouter = require('./routes/ussd');
//var ussdRouterNg = require('./routes/ng/ussd');
var whatsappRouter = require('./routes/whatsapp');
var ngChatbotpRouter = require('./routes/ng/ng-chatbot');
var sadcChatbotRouter = require('./routes/sadc/sadc-chatbot');

dotenv.config();

var app = express();
app.use(helmet());

// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'ejs');

app.use(logger('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));

// app.use('/', indexRouter);
app.use('/users', usersRouter);
app.use('/ussd', ussdRouter);
//app.use('/ng/ussd', ussdRouterNg);
app.use('/whatsapp', whatsappRouter);
app.use('/ng-chatbot', ngChatbotpRouter);
app.use('/sadc-chatbot', sadcChatbotRouter);
var scheduler = require('./services/ug/Scheduler')


/**
 * 5 min session Authentication for Interintel post  login
 * Apis
 */
app.use(session({
        resave: false,
        saveUninitialized: false,
        secret: "ytiuytuiytiuy",
        cookie: {
            maxAge: 1000 * 300,
            secure: false
        }

    }))
    // catch 404 and forward to error handler
app.use(function(req, res, next) {
    res.send('Invalid request');
});

// error handler
app.use(function(err, req, res, next) {
    // set locals, only providing error in development
    res.locals.message = err.message;
    res.locals.error = req.app.get('env') === 'development' ? err : {};

    // render the error page
    res.status(err.status || 500);
    res.render('error');
});


app.listen(process.env['PORT'], () => console.log('Listening on port ' + process.env['PORT']));
module.exports = app;