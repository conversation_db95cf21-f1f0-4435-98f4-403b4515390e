# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
- develop_qa

variables:
  tag: latest

pool:
  name: ado-mom-api-pool
  vmImage: Linux
  
stages:
  - stage: BuildStage
    displayName: 'Build'
    jobs:
      - job: Build
        displayName: 'Build'
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '16.x'
            displayName: 'Install Node.js'

          - script: |
              npm install
            displayName: 'npm install'

          - task: CmdLine@2
            inputs:
              script: |
                echo  '$(System.DefaultWorkingDirectory)'
                dir

          - task: ArchiveFiles@2
            displayName: 'Archive files'
            inputs:
              rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
              includeRootFolder: true 
              archiveType: 'zip' 
              archiveFile: '$(System.DefaultWorkingDirectory)/uganda_whatsapp-$(Build.BuildId).zip' 
              replaceExistingArchive: true 
          - task: PublishPipelineArtifact@0
            inputs:
              targetPath: '$(System.ArtifactsDirectory)'
      

  # - stage: Scan
  #   dependsOn: BuildStage
  #   displayName: Scan
  #   jobs:
  #     - job: Scan
  #       displayName: Scan
  #       steps:
        
  #       - task: CmdLine@2
  #         displayName: 'SonarQube'
  #         continueOnError: true
  #         inputs:
  #           script: |
  #               curl  http://************:8080/artifactory/SupportTools/sonar-scanner/latest/linux.zip -o linux.zip && unzip linux.zip
  #               chmod u+x linux/bin/sonar-scanner
  #               ./linux/bin/sonar-scanner -D sonar.projectKey=OMDigital-MyOldMutual-SocialPlatforms-$(Build.Repository.Name) -D sonar.projectVersion=1.0 -D sonar.sources=. -D sonar.host.url=http://************:9900 -D sonar.login=$(sonartoken)


        # - task: CmdLine@2
        #   displayName: 'NexusIQ'
        #   continueOnError: true
        #   inputs:
        #     script: |
        #         curl  http://************:8080/artifactory/SupportTools/nexus-iq/lastest/nexus-iq-cli.jar -o nexus-iq-cli.jar
        #         chmod u+x nexus-iq-cli.jar
        #         chmod 755 $(System.DefaultWorkingDirectory)/kenya_whatsapp-$(Build.BuildId).zip
        #         java -jar nexus-iq-cli.jar -a YsoEhuE6:j0u782DQVxieX3H476Fck704Hc8MLN1gU1a5BdcglVY4 -i kenya_whatsapp -s http://************:8070 $(System.DefaultWorkingDirectory)/kenya_whatsapp-$(Build.BuildId).zip -t build
  
  - stage: Build
    #dependsOn: Scan
    displayName: Build Docker Image
    jobs:
    - job: Build
      displayName: Build Docker Image
      steps:
      - task: Bash@3
        displayName: 'Login into ECR'
        inputs:
          targetType: inline
          script: aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin 065450289399.dkr.ecr.eu-west-1.amazonaws.com
      - task: Docker@2
        displayName: 'Build and Push to ECR'
        inputs:
          command: buildAndPush
          dockerfile: 'Dockerfile'
          repository: '065450289399.dkr.ecr.eu-west-1.amazonaws.com/uganda_whatsapp'
          tags: |
            $(Build.BuildId)
            $(tag)