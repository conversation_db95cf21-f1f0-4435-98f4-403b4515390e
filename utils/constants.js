exports.errorName = {
    UNAUTHORIZED: 'UNAUTHORIZED',
    INVALID_TOKEN: 'INVALID_TOKEN',
    TOKEN_DOESNT_EXIST: 'TOKEN_DOESNT_EXIST',
    UNABLE_GET_USER: 'UNABLE_GET_USER',
    INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
    USER_SESSION_EXPIRED: 'USER_SESSION_EXPIRED',
    NO_DATA_FOUND: 'NO_DATA_FOUND',
    INVALID_SELECT_FUND: 'INVALID_SELECT_FUND',
    INVALID_OPTION: 'INVALID_OPTION'
}

exports.errorType = {
    UNAUTHORIZED: {
        code: 401,
        message: 'Authentication is needed to get requested response.'

    },
    INVALID_TOKEN: {
        code: 401,
        message: 'The Access Token provided is invalid.'
    },
    TOKEN_DOESNT_EXIST: {
        code: 401,
        message: 'The Bearer Token provided does not exists.'
    },
    UNABLE_GET_USER: {
        code: 500,
        message: 'Error unable to fetch user details.'
    },
    INTERNAL_SERVER_ERROR: {
        code: 500,
        message: 'An error occurred when processing the request.'
    },
    USER_SESSION_EXPIRED: {
        code: 101,
        status_action: 'User should login',
        message: 'The user session has expired kindly login again'
    },
    NO_DATA_FOUND: {
        code: 500,
        message: 'There was no data found'
    },
    INVALID_SELECT_FUND: {
        code: 400,
        message: 'Invalid select fund option selected'
    },
    INVALID_OPTION: {
        code: 400,
        message: 'Invalid option selected'
    }

}
