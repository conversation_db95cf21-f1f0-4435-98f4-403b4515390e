const express = require('express')

const dotenv = require('dotenv');
let db = require('../models');

//function to store user track activity
const store_activity_log  = async (activity_name,origin,payload,phone_no,user_id,callback) =>{

    let session = await db.globalDB.activity_log.create({
        activity_name : activity_name,
        origin:origin,
        payload:payload,
        phone_no:phone_no,
        user_id:user_id,
        created_at:new Date(),
        updated_at:new Date()
    });
    return session.get({plain: true});
};

module.exports = {
    store_activity_log: store_activity_log
}
