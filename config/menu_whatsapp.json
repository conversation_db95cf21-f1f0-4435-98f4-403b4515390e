{"1": {"start": {"title": "Welcome to UAP Old Mutual Services\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n5 Invest With Us\n6 Bank With Us\n7 New Customer Verification\nUpdate My Personal Details\n7 COVID-19 Updates", "next": {"1": "register_claim", "2": "policy_status", "3": "buy_insurance", "4": "make_insurance_payment", "5": "invest_with_us", "6": "bank_with_us", "7": "covid"}}}, "2": {"register_claim": {"title": "Register Claim  For?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance type\n00 Back", "next": {"1": "claim_options", "2": "claim_options", "3": "claim_options", "4": "claim_options", "0": "back_to_main"}}, "policy_status": {"title": "Policy status for?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance Type\n00 Back", "next": {"1": "policy_status_options", "2": "policy_status_options", "3": "policy_status_options", "4": "policy_status_options", "0": "back_to_main"}}, "buy_insurance": {"title": "Buy For?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance Type\n00 Back", "next": {"1": "buy_insurance_options", "2": "buy_insurance_options", "3": "buy_insurance_options", "4": "buy_insurance_options", "0": "back_to_main"}}, "make_insurance_payment": {"title": "Make payment to?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance Type\n00 Back", "next": {"1": "make_insurance_payment_options", "2": "make_insurance_payment_options", "3": "make_insurance_payment_options", "4": "make_insurance_payment_options", "0": "back_to_main"}}, "invest_with_us": {"title": "We Have a Fully Dedicated Investment Platform i-INVEST\nDial *480# to access i-INVEST \n\n00 Back ", "next": {"0": "back_to_main"}}, "bank_with_us": {"title": "Which Faulu Microficance Bank Product Are You Interested In:\n1. Loan\n2. Current Account\n3. Savings Account\n4. Fixed Deposit Account\n00 Back", "next": {"1": "bank_with_us_option", "2": "bank_with_us_option", "3": "bank_with_us_option", "4": "bank_with_us_option", "0": "back_to_main"}}, "covid": {"title": "Preferred Update on:\n1. Keeping Healthy\n2. Feeling Unwell\n3. Working From Home\n4. Studying From Home\n5. Unwinding From Home\n6. Ministry of Health Updates\n00 Back", "next": {"1": "covid_update_message", "2": "covid_update_message", "3": "covid_update_message", "4": "covid_update_message", "5": "covid_update_message", "6": "covid_update_message", "0": "back_to_main"}}}, "3": {"claim_options": {"title": "Select How to Start the Insurance Claim \n1. Provide Claim Details \n2. Request a Call Back\n00 Back \n0 Home", "next": {"1": "provide_details", "2": "request_callback", "0": "register_claim"}}, "policy_status_options": {"title": "Select How To Start Your Policy Status inquiry\n1. Provide Policy Details \n2. Request a Call Back\n00 Back \n0 Home", "next": {"1": "provide_details", "2": "request_callback", "0": "policy_status"}}, "buy_insurance_options": {"title": "Select How to Start Buying Insurance \n1. Provide Claim Details \n2. Request a Call Back \n00 Back \n0 Home", "next": {"1": "provide_details", "2": "request_callback", "0": "buy_insurance"}}, "make_insurance_payment_options": {"title": "Select How to Start the Insurance Payment \n1. Provide Claim Details \n2. Request a Call Back \n00 Back \n0 Home", "next": {"1": "provide_details", "2": "request_callback", "0": "make_insurance_payment"}}}, "4": {"provide_details_car": {"title": "What's Your Car Registration Number? e.g KAA123A\n\n00 Back \n0 Home", "next": {"*\\w+": "provide_details.policy_no", "0": "back_to_main"}}, "provide_details_other": {"title": "What's Your Insurance Policy Number?\n\n00 Back \n0 Home", "next": {"*\\w+": "provide_details.policy_no", "0": "back_to_main"}}, "provide_details_life": {"title": "What's Your Life Insurance Policy Number? e.g. OMK001429206 or National ID Number\n\n00 Back \n0 Home", "next": {"*\\w+": "provide_details.policy_no", "0": "back_to_main"}}, "provide_details_health": {"title": "What's Your Health Insurance Policy Number? eg. UK032344-01 or National ID Number\n\n00 Back \n0 Home", "next": {"*\\w+": "provide_details.policy_no", "0": "back_to_main"}}}, "generic": {"response_options": {"next": {"1": "provide_details", "2": "request_callback", "0": "back_to_main"}}, "provide_details": {"next": {"1": "success_end_message", "0": "back_to_main"}}, "success_end_message": {"title": "Thank You For the Details Provided, One of Our Call Center Agents Will Call You Within 24 Hours.\n\n0 Home", "next": {"0": "back_to_main"}}, "exit": {"title": "Thank You For the Details Provided, One of Our Call Center Agents Will Call You Within 24 Hours."}, "covid_end_message": {"title": "A <PERSON> Has Sent To You Via SMS on"}, "covid_update_message": {"title": "A <PERSON> Has Been Sent To You Via SMS on \n00 Back \n0 Home", "next": {"0": "back_to_main", "11": "exit"}}, "covid_menu": {"title": "A <PERSON> Has Sent To You Via SMS on", "1": "How to Keep Healthy\n00 Back\n0 Home", "2": "What To Do If You Feel Unwell\n00 Back\n0 Home", "3": "How to Work From Home\n00 Back\n0 Home", "4": "How to Study From Home\n00 Back\n0 Home", "5": "How to Unwind From Home\n00 Back\n0 Home", "6": "Ministry of Health Updates\n00 Back\n0 Home", "0": "Back to Menu"}, "insurance_class": ["Health Insurance", "Car Insurance", "Life Insurance", "Other Insurance Insurance"]}}