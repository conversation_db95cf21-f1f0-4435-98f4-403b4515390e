const axios = require('axios');

async function getUser(params, callback) {
    try {
        let token = process.env.NG_VERIFY_ME_TOKEN;
        let config = {
            headers: {
                'Authorization': 'Bearer ' + token
            }
        }
        // const response = await axios.get('/user?ID=12345');

        console.log(params);
        let response = await axios.post('https://vapi.verifyme.ng/v1/verifications/identities/nin/' + params.ref,
            params, config)
        if (response)
            callback(false, response.data)
        else
            callback(true, response.data)
            /*.then(function (response) {
                console.log(response.data.data.fieldMatches);
                console.log(response.data.status);
                callback(false, response)
                /!*if (response.data.status === 'success' && response.data.data.fieldMatches.lastname) {
                    callback(true, response)
                } else {
                    callback(false, response.data.message)
                }*!/
            })*/
                .catch(function (error) {
                    console.log(error.data);
                });

    } catch (error) {
        console.error(error);
        callback(true, error)
    }
}

module.exports = {
    getUser: getUser
}