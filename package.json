{"name": "ussd", "version": "0.0.2", "private": true, "scripts": {"start": "nodemon ./bin/www"}, "dependencies": {"@aws-sdk/client-s3": "^3.279.0", "aws-sdk": "^2.1322.0", "axios": "^0.21.1", "basic-auth": "^2.0.1", "body-parser": "^1.20.2", "config": "^3.2.2", "cookie-parser": "^1.4.5", "country-codes-list": "^1.6.8", "dateformat": "^3.0.3", "debug": "~2.6.9", "dotenv": "^8.2.0", "download": "^8.0.0", "download-file": "^0.1.5", "ejs": "~2.6.1", "exceljs": "^4.2.0", "express": "~4.16.1", "express-mysql-session": "^2.1.4", "express-redis-cache": "^1.1.3", "express-session": "^1.17.1", "file-type": "^14.3.0", "flutterwave-node": "^1.2.0", "flutterwave-node-v3": "^1.0.6", "helmet": "^3.22.0", "http-errors": "~1.6.3", "image-downloader": "^3.5.0", "json2csv": "^6.0.0-alpha.2", "jsonexport": "^3.2.0", "jsonwebtoken": "^8.4.0", "memory-cache": "^0.2.0", "mongodb": "^3.5.6", "mongoose": "^5.9.10", "morgan": "~1.9.1", "mysql": "^2.18.1", "mysql2": "^2.2.5", "node-cron": "^2.0.3", "node-datetime": "^2.1.2", "nodemailer": "^6.4.10", "passport": "^0.4.0", "passport-jwt": "^4.0.0", "path": "^0.12.7", "pdfmake": "^0.1.68", "qs": "^6.5.2", "read-chunk": "^3.2.0", "redis": "^3.1.2", "request": "^2.88.2", "request-promise": "^4.2.6", "sequelize": "^5.22.4", "sequelize-auto": "^0.7.2", "tsscmp": "^1.0.6", "ussd-menu-builder": "^1.2.0", "ussd-router": "^0.1.5", "util": "^0.12.2", "validator": "^13.0.0"}, "devDependencies": {"nodemon": "^2.0.22"}, "main": "app.js", "author": "", "license": "ISC", "description": ""}