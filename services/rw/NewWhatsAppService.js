'use strict';

var whatsAppMessageService = require('./WhatsAppService');
var whatsAppMenuService = require('../WhatsAppMenuService');
var whatsappDBService = require('./WhatsappDBService');
var selfservice = require('./SelfService-API');
var sessionService = require('./SessionService');
var validateService = require('./ValidateInputService');
var mailService = require('./MailService')
const dotenv = require('dotenv')


let utils = require('../Utils');
let source_channel = 2;
let enforceHWSingleRegistration = true
let tnc_link = 'https://www.oldmutual.rw/privacy-policy/'
let site = 'https://www.uaprwanda.rw/'

let salutation_trigger = [
    'hi', 'hello', 'helo', 'halo', 'hallo', 'hey', 'mambo', 'vipi', 'niaje', 'menu', 'help'
];
let salutation_response = ['Hi', 'Hello', 'Hello', 'Hi', 'Hi', 'Hi', 'Hi``', 'Hi', 'Hi', 'Hi', 'Hi'];
let res;
let level, shouldUpdateSession = true;
let menu_data = {}
let menu = '';

async function handleUserText(req, _res, source_country = 'ke') {
    console.log('**Source country:  ' + source_country)
    res = _res;
    let content = req.body.results[0];
    let unique_identifier = content.message.text
    let phone_no = content.from

    console.log('WA | Sender: ' + phone_no + ' | Text: ' + unique_identifier)

    // console.log(getDateTime() + " | " +  content.from + ' used trigger word: ' + unique_identifier)
    let sal_index = salutation_trigger.indexOf(unique_identifier.toLocaleLowerCase());

    console.log('Index: ' + sal_index + ' | Text: ' + content.message.text)
    console.log(`(sal_index > -1) ${(sal_index > -1)}`)
    if (sal_index > -1) {
        unique_identifier = 'start';
        await saveUserDetails(salutation_response[sal_index], content, source_country)
        whatsappDBService.getUserLevel(phone_no, function(err, user_response) {

            if (err) {
                initialWelcome(salutation_response[sal_index], content, source_country)
            } else {
                console.log(utils.getDateTime() + " | SAL USED: " + content.from + ' | SELECTION - input: ' + content.message.text)

                if (user_response.country != null) {
                    //console.log(content.from + ' | country: ' + country)
                    level = 1
                    unique_identifier = user_response.country
                } else if (user_response.country == null) {
                    // console.log('===> Enten')
                    level = 0
                    unique_identifier = 'start'
                    user_response.level = 0
                    updateCountry({ id: user_response.id, phone_no: phone_no, level: 0 })
                }
                console.log(`unique_identifier ${unique_identifier}`)
                menu = whatsAppMenuService.getMenu(level, unique_identifier, user_response)
                let name = ""
                if ("contact" in content && level == 1) {
                    content.contact.name = content.contact.name.replace(/([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
                    if (typeof content.contact.name == 'string') {
                        name = " " + content.contact.name
                        if (source_country === 'tz')
                            menu = 'Hi' + name + ', we\'re happy to have you here 🙂. Welcome to UAP. I will be your virtual assistant.' +
                            ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  https://bit.ly/2XfWN4o\n\n' + menu
                        else
                            menu = 'Hi' + name + ', we\'re happy to have you here 🙂. Welcome to Old Mutual Rwanda. I will be your virtual assistant.' +
                            ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n' + menu
                    }
                }
                if (level == '0' || level == '1') {
                    // console.log('----> WhatsAPP to be created')
                    selfservice.create_wa_user(phone_no, name, user_response.country, function(err, resp) {
                        if (!err)
                            console.log('----> WhatsAPP user created ' + resp)
                    })

                    user_response.path = 'start'
                }
                // console.log(utils.getDateTime() + " | " + content.from + ' | REQ: trigger word used | unique_identifier: ' + unique_identifier + ' level: ' + level)

                whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                    if (err) {
                        console.log(content.from + ' | wa_response: ' + err)
                        res.json(err)

                    } else {
                        // if (shouldUpdateSession)
                        whatsappDBService.updateUserSession({
                            phone_no: content.from,
                            level: 1,
                            input: content.message.text,
                            unique_identifier: unique_identifier,
                            expected_input_type: 'SELECTION',
                            path: user_response.path + '*' + content.message.text
                        }, function(err, session_response) {
                            if (err) {
                                console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                    // res.json(err)
                            } else {
                                try {
                                    res.json(wa_response)
                                } catch (e) {

                                }
                            }
                        })
                    }
                })
            }
        })
    } else {
        //await saveUserDetails(salutation_response[sal_index], content, source_country)
        whatsappDBService.getUserLevel(phone_no, function(err, user_response) {
            if (err) {
                initialWelcome(salutation_response[0], content, source_country)
            } else {

                console.log(utils.getDateTime() + " | REQ: " + content.from + ' Data: ' + JSON.stringify(user_response) + ' | USER INPUT: ' + content.message.text + ' Msg Id: ' + content.messageId)
                console.log(`user_response.expected_input_type ${user_response.expected_input_type}`)
                if (user_response.expected_input_type === 'SELECTION') { // Menu selected
                    if (content.message.text == '0' && user_response.level == '1') {
                        level = 0
                        user_response.level = 0
                        user_response.country = null
                        unique_identifier = 'start'
                        initialWelcome('Hi', content, source_country)
                        return;
                    } else if (content.message.text == '0' || user_response.unique_identifier == 'back_to_main') {
                        level = 1
                        user_response.level = 1
                        unique_identifier = user_response.country
                        user_response.unique_identifier = user_response.country
                        user_response.path = 'start'
                            // console.log(content.from + ' | SELECTION ****: ')
                    }
                    //update country
                    else if (user_response.country == null) {
                        user_response.level = 0
                        let country = getCurrentUniqueIdentifier(content.message.text, user_response)
                        user_response.country = country
                            // console.log('->1 country ' + country)
                        if (country === 'start') { // user entered an option not available.
                            // console.log('----> No country for option ' + content.message.text)
                            level = 0
                            unique_identifier = 'start'
                        } else {
                            level = 1
                            unique_identifier = country
                            user_response.country = country
                            user_response.country_id = content.message.text
                        }

                    } else {
                        level = user_response.level + 1
                        unique_identifier = getCurrentUniqueIdentifier(content.message.text, user_response)
                        console.log(`unique_identifier ${unique_identifier}`)
                        if (typeof unique_identifier == 'undefined') {
                            console.log('----> Unique undefined ' + content.message.text)
                            level = user_response.level
                            unique_identifier = user_response.unique_identifier
                        }
                        if (!shouldUpdateSession)
                            level = user_response.level
                    }

                    // console.log(utils.getDateTime() + " | REQ: " + content.from + ' | SELECTION - input: ' + content.message.text + ' Menu: ' + unique_identifier + ' Msg Id: ' + content.messageId)
                    console.log(utils.getDateTime() + " | " + content.from + ' | SELECTION level: ' + level + ' NEW unique_identifier ' + unique_identifier + ' OLD unique_identifier ' + user_response.unique_identifier + ' input ' + content.message.text + ' expected_input_type ' + user_response.expected_input_type)

                    // Validation
                    let validation_error_msg = '';
                    if (unique_identifier === 'request_callback') {

                        menu_data = getMenu(level, unique_identifier, user_response)
                        unique_identifier = menu_data.unique_identifier
                        let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

                        user_response.path = user_response.path + '*end'
                        selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                            if (err) {} else {
                                menu = getTicketResponseMessage(user_response.whatsapp_name, ticket_no, unique_identifier)
                                sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                endTicketSession(phone_no)
                                formulateRequestCallBack(phone_no, user_response.whatsapp_name)
                            }
                        })

                    } else if (unique_identifier === unique_identifier.includes('success_end_message')) {
                        menu_data = getMenu(level, unique_identifier, user_response)
                        unique_identifier = menu_data.unique_identifier
                        let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

                        user_response.path = user_response.path + '*end'
                        selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                            if (err) {} else {
                                menu = getTicketResponseMessage(user_response.whatsapp_name, ticket_no, unique_identifier)
                                sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                endTicketSession(phone_no)
                            }
                        })
                    } else if (unique_identifier === 'covid_update_message') {
                        selfservice.SMS_Update(phone_no, content.message.text, function(err, _cov_resp) {
                            if (err) {
                                _res.json(false)
                                sendToWhatsApp(source_country, phone_no, "Error processing your request. Please try again.\n0 Main Menu", 'END', unique_identifier, level)
                            } else {
                                let cov_resp = JSON.parse(_cov_resp)
                                sendToWhatsApp(source_country, phone_no, cov_resp.content + '\n0 Main Menu', 'END', unique_identifier, level)
                            }
                        })
                    } else {
                        // console.log('--------------> 3')
                        validationPassed(source_country, level, unique_identifier, user_response, content)
                    }

                } else if (user_response.expected_input_type === 'END') {
                    if (unique_identifier == 'success_end_message') {
                        user_response.unique_identifier = unique_identifier
                    }
                    selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                        if (err) {
                            console.log('*** Unique ID ignored')
                        } else {
                            endTicketSession(phone_no)
                        }

                    })
                    unique_identifier = getCurrentUniqueIdentifier(content.message.text, user_response)
                    console.log(utils.getDateTime() + " | REQ: " + content.from + ' | END level: ' + user_response.level + ' input ' + content.message.text + ' Msg Id: ' + content.messageId)
                    level = 1
                    unique_identifier = user_response.country
                    menu_data = getMenu(level, unique_identifier, user_response)
                    menu = menu_data.menu

                    if (content.message.text == 1) { //Done uploading photos


                    } else {
                        whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                            if (err) {
                                console.error(content.from + ' | wa_response: ' + err)
                            } else {
                                // if (shouldUpdateSession)

                                whatsappDBService.updateUserSession({
                                    phone_no: content.from,
                                    level: 1,
                                    expected_input_type: 'SELECTION',
                                    unique_identifier: unique_identifier,
                                    path: user_response.path + '*end',
                                }, function(err, session_response) {

                                    if (err) {
                                        console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                            // res.json(err)
                                    } else {
                                        try {
                                            res.json(wa_response)
                                        } catch (e) {

                                        }
                                    }
                                })
                            }
                        })
                    }
                } else if (user_response.expected_input_type === 'IMAGE') {
                    if (content.message.text == '0' || user_response.unique_identifier == 'back_to_main') {
                        level = 1
                        user_response.level = 1
                        unique_identifier = user_response.country
                        user_response.unique_identifier = user_response.country
                        user_response.path = 'start'
                        console.log(content.from + ' | IMAGE ****: ')
                    } else {
                        level = user_response.level + 1
                        unique_identifier = getCurrentUniqueIdentifier(1, user_response) // An IMAGE share level always has one NEXT step!
                    }
                    console.log(content.from + ' | REQ: IMAGE level: ' + user_response.level + ' new unique_identifier ' + unique_identifier + ' expected_input_type ' + user_response.expected_input_type + ' Msg Id: ' + content.messageId)

                    menu_data = getMenu(level, unique_identifier, user_response)
                    menu = menu_data.menu
                    unique_identifier = menu_data.unique_identifier
                    let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

                    /*if (unique_identifier.includes('health_worker')) {
                        console.log('*** Health worker')
                        user_response.unique_identifier = unique_identifier
                    }
                    else */

                    if (content.message.text == 1) { //Done uploading photos
                        if (unique_identifier === 'provide_details_health_worker_success_end_message') {
                            // Send photos
                            // Close whatsapp session
                            // Respond back to user.
                            console.log('claim_req health worker:')
                            selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                                if (err) {
                                    _res.json(false)
                                } else {
                                    if (ticket_no > 0) {
                                        selfservice.healthcare_worker_claim_request(ticket_no, function(err, claim_response) {
                                            if (err) {
                                                _res.json(false)
                                            } else {
                                                console.log('claim_response: ' + claim_response)
                                                user_response.path = user_response.path + '*' + content.message.text
                                                sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)

                                                console.log('Ticket to close - HealthWorker: ' + ticket_no)
                                                selfservice.close_user_WhatsApp_Session(phone_no, ticket_no, function(err, close_sess_response) {
                                                    if (err) {

                                                    } else {
                                                        console.log('Session closed for: @close_user_WhatsApp_Session' + phone_no)
                                                    }

                                                })

                                            }
                                        })

                                    }
                                }

                            })

                        } else if (unique_identifier === 'provide_details_photos_end') {
                            console.log('Formulate Email')
                            selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                                if (err) {
                                    console.log(`Error ${err}`)
                                    _res.json(false)
                                } else {
                                    console.log(`ticket_no ${ticket_no}`)
                                    console.log(`ticket_no (ticket_no > 0) ${(ticket_no > 0)}`)

                                    if (ticket_no > 0) {
                                        user_response.path = user_response.path + '*end'
                                        menu = getTicketResponseMessage(user_response.whatsapp_name, ticket_no, user_response.unique_identifier)
                                        sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                        console.log(`user_response.whatsapp_name  ${user_response.whatsapp_name}`)
                                        formulateClaimsData(phone_no, user_response.whatsapp_name)
                                        endTicketSession(phone_no)
                                    } else
                                        _res.json(false)
                                }
                            })

                        }
                    } else if (unique_identifier === 'provide_details_photos_end') {
                        console.log('Formulate Email 2 ' + content.message.text)
                        if (content.message.text == '1') {
                            selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                                if (err) {
                                    _res.json(false)
                                } else {
                                    if (ticket_no > 0) {
                                        user_response.path = user_response.path + '*end'
                                        menu = getTicketResponseMessage(user_response.whatsapp_name, ticket_no, user_response.unique_identifier)
                                        sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                        formulateClaimsData(phone_no, user_response.whatsapp_name)
                                        endTicketSession(phone_no)
                                    } else
                                        _res.json(false)
                                }
                            })

                        } else {
                            let validation_error_msg = 'Invalid input Type 1 to complete your submission \n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg
                            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else
                                    res.json(wa_response)
                            })
                        }
                    } else {
                        if (input_type == 'END') {
                            user_response.unique_identifier = 'success_end_message'
                            user_response.path = user_response.path + '*end'
                        }

                        selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                            if (err) {
                                console.log('*** Unique ID ignored')
                            } else {
                                // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)

                                user_response.path = user_response.path + '*' + content.message.text
                                sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                endTicketSession(phone_no)
                            }

                        })


                    }
                } else {
                    // unique_identifier = user_response.unique_identifier
                    if (content.message.text == '0') {
                        level = 1
                        user_response.level = 1
                        unique_identifier = user_response.country
                        user_response.unique_identifier = user_response.country
                        user_response.path = 'start'
                            // console.log(content.from + ' | SELECTION ****: ')
                    } else {
                        level = user_response.level + 1
                        unique_identifier = getCurrentUniqueIdentifier(1, user_response) // And entry level always has one next step!
                    }

                    console.log(content.from + ' | level: ' + level + ' new unique_identifier ' + unique_identifier + ' OLD ' + user_response.unique_identifier + ' expected_input_type ' + user_response.expected_input_type + ': ' + content.message.text)

                    menu_data = getMenu(level, unique_identifier, user_response)
                    menu = menu_data.menu
                    unique_identifier = menu_data.unique_identifier

                    let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

                    // console.log(utils.getDateTime() + " | REQ: " + content.from + ' | TEXT level: ' + level + ' new unique_identifier ' + unique_identifier + ' | expected_input_type ' + user_response.expected_input_type + ' Msg Id: ' + content.messageId)

                    /**
                     * Add here any unique_identifier that requires ID or Passport verification
                     * */
                    if (user_response.unique_identifier === 'health_worker_national_id' || user_response.unique_identifier === 'verify_id_passport') {
                        let data = validateService.isValidIdOrPassportNo(content.message.text)
                        if (data.error) {
                            level = user_response.level
                            unique_identifier = user_response.unique_identifier
                                // console.log('--------------> Invalid Input')
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = 'Invalid input\n' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                } else
                                    res.json(wa_response)
                            })
                        } else {
                            if (user_response.unique_identifier === 'health_worker_national_id')
                                selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                    if (err) {
                                        console.log('*** Unique ID ignored')
                                    } else {
                                        // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)
                                        user_response.path = user_response.path + '*' + content.message.text
                                        sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)

                                    }

                                })
                            else
                                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                    if (err) {
                                        console.log('*** Unique ID ignored')
                                    } else {
                                        // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)
                                        user_response.path = user_response.path + '*' + content.message.text
                                        sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)

                                    }

                                })
                        }

                    } else if (content.message.text == '00' && user_response.unique_identifier === 'otp_verification') {

                        selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                            if (err) {
                                _res.json(false)
                            } else {

                                sessionService.getWhatsAppSessionData(ticket_no, function(err, resp) {
                                    if (err) {
                                        _res.json(false)
                                    } else {

                                        resp.forEach(element => {

                                            if (element.key === "customer_verification_phone") {
                                                console.log('OTP Resend: ' + JSON.stringify(resp))
                                                selfservice.sendOTP(element.session_data, function(err, otp_resp) {
                                                    if (err) {
                                                        menu_data = getMenu(7, 'signup_phone_no', user_response)
                                                        menu = menu_data.menu
                                                        sendToWhatsApp(source_country, phone_no, menu, 'TEXT', 'signup_phone_no', 7, user_response.path)
                                                    } else {
                                                        menu_data = getMenu(8, 'otp_verification', user_response)
                                                        menu = menu = 'OTP code has been sent\n' + menu_data.menu
                                                        sendToWhatsApp(source_country, phone_no, menu, 'TEXT', 'otp_verification', 8, user_response.path)
                                                            // endTicketSession(phone_no)
                                                    }
                                                })
                                            }


                                        })

                                    }


                                })

                            }
                        })
                    } else if (user_response.unique_identifier === 'enter_tax_id_number') {
                        // validate tax number
                        let valid = validateService.validateTaxIDNumber(content.message.text, source_country)
                        if (!valid) {
                            let validation_error_msg = 'Invalid Taxpayers Identification Number\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {

                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    // console.log('*** Unique ID stored!' + resp)
                                    var currentPath = user_response.path.split("*")
                                    var menu = currentPath[3]
                                    if (menu === "1") {
                                        var level = user_response.level + 1
                                        menu_data = getMenu(level, 'enter_car_make', user_response)
                                        sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'enter_car_make', level, user_response.path)
                                    } else {
                                        endSesssionGetData(phone_no, menu)
                                        var level = user_response.level + 1
                                        menu_data = getMenu(level, 'success_end_message', user_response)
                                        sendToWhatsApp(source_country, phone_no, menu_data.menu, 'END', 'success_end_message', level, user_response.path)


                                    }
                                }

                            })

                        }
                    } else if (user_response.unique_identifier === 'enter_email_address') {
                        let valid = validateService.validateEmail(content.message.text)
                        if (!valid) {
                            let validation_error_msg = 'Invalid email address\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    console.log('*** Session Saved')
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'select_identification_document', user_response)
                                    sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', 'select_identification_document', level, user_response.path)

                                }

                            })
                        }

                    } else if (user_response.unique_identifier === 'enter_car_reg_number') {
                        console.log(`Reg Number ${content.message.text}`)
                        let valid = validateService.validateCarRegistration(content.message.text)
                        if (!valid) {
                            let validation_error_msg = 'Invalid Car Registration No\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(400)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    console.log('*** Session Saved')
                                    endSesssionGetData(phone_no)
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'success_end_message', user_response)
                                    sendToWhatsApp(source_country, phone_no, menu_data.menu, 'END', 'success_end_message', level, user_response.path)


                                }

                            })
                        }

                    } else if (user_response.unique_identifier === 'enter_car_year_make') {
                        console.log(`YOM ${content.message.text}`)
                        let valid = validateService.validateYear(content.message.text)
                        if (!valid) {
                            let validation_error_msg = 'Invalid Car Year of Make\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(400)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'enter_car_reg_number', user_response)
                                    sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'enter_car_reg_number', level, user_response.path)


                                }

                            })
                        }

                    } else if (user_response.unique_identifier === 'enter_national_id') {
                        console.log(`National ID ${content.message.text}`)
                        let valid = validateService.validateIdNumber(content.message.text, source_country)
                        if (!valid) {
                            let validation_error_msg = 'Invalid National ID\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    console.log('*** Session Saved')
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'photo_national_id', user_response)
                                    sendToWhatsApp(source_country, phone_no, menu_data.menu, 'IMAGE', 'photo_national_id', level, user_response.path)

                                }

                            })

                        }
                    }  else if (user_response.unique_identifier === 'enter_first_name') {
                        console.log(`First Name ${content.message.text}`)
                        let valid = validateService.validateString(content.message.text, source_country)
                        if (!valid) {
                            let validation_error_msg = 'Invalid Forename ID\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    console.log('*** Session Saved')
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'enter_sur_name', user_response)
                                    sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'enter_sur_name', level, user_response.path)

                                }

                            })

                        }
                    } else if (user_response.unique_identifier === 'provide_details_car') {
                        console.log(`Reg Number ${content.message.text}`)
                        let valid = validateService.validateCarRegistration(content.message.text)
                        if (!valid) {
                            let validation_error_msg = 'Invalid Car Registration No\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.json(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'provide_details_car_photos', user_response)
                                    sendToWhatsApp(source_country, phone_no, menu_data.menu, 'IMAGE', 'provide_details_car_photos', level, user_response.path)

                                }

                            })
                        }

                    } else if (user_response.unique_identifier === 'enter_sur_name') {
                        console.log(`National ID ${content.message.text}`)
                        let valid = validateService.validateString(content.message.text, source_country)
                        if (!valid) {
                            let validation_error_msg = 'Invalid Surname\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    console.log('*** Session Saved')
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'enter_email_address', user_response)
                                    sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'enter_email_address', level, user_response.path)

                                }

                            })

                        }
                    } else if (unique_identifier.includes('success_end_message')) {
                        let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

                        user_response.path = user_response.path + '*end'
                        selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                            if (err) {} else {
                                selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                    if (err) {
                                        console.log('*** Unique ID ignored')
                                    } else {
                                        // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)
                                        user_response.path = user_response.path + '*' + content.message.text
                                        menu = getTicketResponseMessage(user_response.whatsapp_name, ticket_no)
                                        sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                        endTicketSession(phone_no)

                                    }
                                })
                            }
                        })

                    } else {
                        console.log(`Last IF ${content.message.text}`)
                        if (input_type === 'END') {
                            user_response.unique_identifier = 'success_end_message'
                        }
                        selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                            if (err) {
                                console.log('*** Unique ID ignored')
                            } else {
                                // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)
                                user_response.path = user_response.path + '*' + content.message.text
                                sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)
                                if (input_type === 'END') {
                                    endTicketSession(phone_no)
                                }
                            }

                        })
                    }

                }
            }

        })

    }

}

function validationPassed(source_country, level, unique_identifier, user_response, content) {
    let phone_no = content.from
    menu_data = getMenu(level, unique_identifier, user_response)
    let name = ""

    if ("contact" in content && level == 1) {
        content.contact.name = content.contact.name.replace(/([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        if (typeof content.contact.name == 'string') {
            name = content.contact.name
            if (source_country === 'tz')
                menu = 'Hi ' + name + ', we\'re happy to have you here 🙂. Welcome to UAP. I will be your virtual assistant.' +
                ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n' + menu_data.menu
            else {
                menu = 'Hi ' + name + ', we\'re happy to have you here :-). Welcome to UAP Old Mutual and Faulu Microfinance Bank. I will be your virtual assistant.' +
                    ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n' + menu_data.menu
            }
        } else
            menu = menu_data.menu

    } else
        menu = menu_data.menu


    if (level == '1') {
        // console.log('----> WhatsAPP to be created')
        selfservice.create_wa_user(phone_no, name, user_response.country, function(err, resp) {
            if (!err)
                console.log('----> WhatsAPP user created ' + resp)
        })
    }

    unique_identifier = menu_data.unique_identifier


    let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);
    // console.log(content.from + ' @validationPassed | new level: ' + level + ' old level: ' + user_response.level + ' new unique_identifier ' + unique_identifier + ' OLD ' + user_response.unique_identifier + ' next input type ' + input_type)

    /* if (unique_identifier.includes('health_worker')) {
         console.log('*** Health worker')
         user_response.unique_identifier = unique_identifier
     }
     else */
    if (input_type == 'END') {
        user_response.unique_identifier = 'success_end_message'
    }
    // phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel,
    selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
        if (err) {
            console.log('*** Unique ID ignored' + err)
        } else {
            // console.log('*** Ticket session received!' + resp)
            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                if (err) {
                    console.error(content.from + ' | wa_response: ' + err)
                } else {
                    // let _level = user_response.level + 1

                    console.error(content.from + ' | wa_response: ' + err)

                    let params = {
                            phone_no: content.from,
                            level: level,
                            input: content.message.text,
                            unique_identifier: unique_identifier,
                            country: user_response.country,
                            country_id: user_response.country_id,
                            expected_input_type: input_type,
                            path: user_response.path + '*' + content.message.text
                        }
                        //console.log('### params: ' + JSON.stringify(params))
                        // res.json(wa_response)
                    if (shouldUpdateSession)
                        whatsappDBService.updateUserSession(params, function(err, session_response) {

                            if (err) {
                                console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                    // res.json(err)
                            } else {
                                try {
                                    res.json(wa_response)
                                } catch (e) {

                                }
                            }
                        })
                }
            })
        }

    })
}

function endTicketSession(phone_no) {
    console.log(`endTicketSession ${phone_no}`)
    selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
        if (err) {

        } else {
            selfservice.close_user_WhatsApp_Session(phone_no, ticket_no, function(err, close_sess_response) {
                if (err) {

                } else {
                    console.log('Ticket to closed : ' + ticket_no + ' Message: ' + close_sess_response)
                }

            })
        }

    })
}


function handleUserImages(req, _res, source_country = 'ke') {
    res = _res;
    let content = req.body.results[0];
    let unique_identifier
    let phone_no = content.from

    console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: IMAGE ' + content.messageId)
        // console.log('Attached docs: '+JSON.stringify(req.body))

    whatsappDBService.getUserLevel(phone_no, function(err, user_response) {
        if (err) {
            initialWelcome(salutation_response[0], content, source_country)
        } else {
            unique_identifier = getCurrentUniqueIdentifier(1, user_response)
            console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: IMAGE level: ' + user_response.level + ' new unique_identifier ' + unique_identifier + ' old unique_identifier ' + user_response.unique_identifier + ' input ' + content.message.url + ' expected_input_type ' + user_response.expected_input_type + ' Msg Id: ' + content.messageId)

            if (content.message.text == '0' || unique_identifier == 'back_to_main') {
                level = 1
                unique_identifier = user_response.country
                user_response.path = 'start'
            } else
                level = user_response.level

            let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

            console.log(user_response.unique_identifier)


            console.log("before download")
            console.log(`user_response ${JSON.stringify(user_response)}`)

            selfservice.getSessionTicketNo(phone_no, async function(err, response) {

                await selfservice.download_reg_media(content.message.url, phone_no, response, content.message.caption, source_country, function(err, resp) {
                    if (err) {
                        console.error(utils.getDateTime() + ' | ' + phone_no + 'Error downloading HW registration images ' + err)
                        sendErrorMessage(phone_no, _res)
                    } else {
                        console.log('*** Identification media saved')
                        if (user_response.unique_identifier === 'photo_national_id' || user_response.unique_identifier === 'photo_passport' || user_response.unique_identifier === 'photo_passport_health' || user_response.unique_identifier === 'photo_national_id_health') {
                            if (user_response.unique_identifier === 'photo_national_id' || user_response.unique_identifier === 'photo_passport') {
                            var level = user_response.level + 1
                            menu_data = getMenu(level, 'enter_tax_id_number', user_response)
                            sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'enter_tax_id_number', level, user_response.path)
                            }
                            else {
                            var level = user_response.level + 1
                            menu_data = getMenu(level, 'enter_tax_id_number_health', user_response)
                            sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'enter_tax_id_number_health', level, user_response.path)
                            }
                        } else {
                            res.json(true)
                        }
                    }
                })
            })



            if (input_type == 'END') {
                user_response.unique_identifier = 'success_end_message'
            }

        }
    })

}

function handleUserVideos(req, _res) {
    console.log('*** Unique ID ignored')
    let content = req.body.results[0];
    console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: VIDEO TYPE UPLOADED Msg Id ' + content.messageId)

    let menu = 'Please upload a photo or a document.'
    whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
        if (err) {
            _res.json(false)
        } else {
            _res.json(menu)
        }
    })
}

function handleUserDocuments(req, _res) {
    handleUserImages(req, _res)

}

function handleUserAudio() {

}

function goToMainMenu() {


}

function getMenu(level, unique_identifier, user_response) {

    console.log('--> 1 CHECK MENU  level: ' + level + ' Unique: ' + unique_identifier + ' Prev input: ' + user_response.input)
    console.log('User data: ' + JSON.stringify(user_response))
    let _menu = whatsAppMenuService.getMenu(level, unique_identifier, user_response)
    if (typeof _menu === 'undefined') {
        let _level = level - 1;
        console.log(utils.getDateTime() + " | " + user_response.phone_no + 'MENU: getMenu() not found  level: ' + _level + ' Unique: ' + unique_identifier + ' Prev input: ' + user_response.input)
        shouldUpdateSession = false;
        unique_identifier = whatsAppMenuService.getMenuByIndex(user_response.level, user_response.unique_identifier, user_response.input, user_response)
        console.log('--> Previous uniq %% ' + unique_identifier)
        _menu = whatsAppMenuService.getMenu(level, unique_identifier, user_response)
        return { menu: _menu, unique_identifier: unique_identifier };
    } else {
        console.log('--> Menu Found  level: ' + level + ' Unique: ' + unique_identifier + ' Prev input: ' + user_response.input)
        shouldUpdateSession = true;
        return { menu: _menu, unique_identifier: unique_identifier };
    }
}

function getCurrentUniqueIdentifier(input, user_response) {

    // console.log('Unique User data: ' + JSON.stringify(user_response))
    let _unique_identifier, level;
    _unique_identifier = whatsAppMenuService.getMenuByIndex(user_response.level, user_response.unique_identifier, input, user_response)

    // _unique_identifier = whatsAppMenuService.getMenuByIndex(2, 'register_claim', 1)

    // console.log('User data: ' + _unique_identifier)
    if (typeof _unique_identifier === 'undefined') {
        shouldUpdateSession = false
        console.log(utils.getDateTime() + " | " + user_response.phone_no + 'MENU: getCurrentUniqueIdentifier() unique_identifier not Found  level: ')

        if (user_response.level == 1) {
            // console.log('1 User data: ')
            return whatsAppMenuService.getMenuByIndex(0, 'start', user_response.country_id, user_response)
        } else {
            // console.log('2 User data: ')
            // return whatsAppMenuService.getMenuByIndex(level, user_response.unique_identifier, user_response.input)
            return user_response.unique_identifier
        }
        /*console.log('--> 343 Previous uniq ' + _unique_identifier)
        return whatsAppMenuService.getMenu(level, _unique_identifier)*/
    } else {
        // console.log('--> Unique FOUND ' + _unique_identifier)
        if (user_response.level == 1)
            shouldUpdateSession = true;
        return _unique_identifier;
    }
}
/**
 * Save user details
 */
const saveUserDetails = async(salutation, content, source_country) => {
    console.log('saveUserDetails')
    let name = ""
    if ("contact" in content) {
        content.contact.name = content.contact.name.replace(/([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        if (typeof content.contact.name == 'string') {
            salutation = salutation + " " + content.contact.name
            name = content.contact.name
        }

    }
    whatsappDBService.updateUserSession({
        phone_no: content.from,
        whatsapp_name: name,
        level: 0,
        input: content.message.text,
        unique_identifier: 'start',
        expected_input_type: 'SELECTION',
        country: 'rwanda',
        country_id: 4,
        path: 'start'
    }, function(err, msg) {
        if (err) {
            console.log(`updateUserSession err ${err}`)
        }
        console.log(`updateUserSession ${msg}`)
    })
}

function initialWelcome(salutation, content, source_country) {
    let name = ""
    if ("contact" in content) {
        content.contact.name = content.contact.name.replace(/([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        if (typeof content.contact.name == 'string') {
            salutation = salutation + " " + content.contact.name
            name = content.contact.name
        }

    }
    let salute = ''
    if (source_country === 'tz')
        salute = salutation + ', we\'re happy to have you here 🙂. Welcome to UAP. I will be your virtual assistant.' +
        ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n'
    else
    //
    // salute = salutation + ', we\'re happy to have you here 🙂. Welcome to UAP Old Mutual and Faulu Microfinance Bank. I will be your virtual assistant.' +
    // ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n'

        salute = salutation + ', we\'re happy to have you here 🙂. Welcome to UAP Old Mutual and Faulu Microfinance Bank. I will be your virtual assistant.' +
        ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n'

    /*let salute = salutation + ', we\'re happy to have you here 🙂. Welcome to UAP Old Mutual and Faulu Microfinance Bank. I will be your virtual assistant.' +
        ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n';*/
    // console.log('Salutation ' + salute)

    whatsappDBService.updateUserSession({
        phone_no: content.from,
        whatsapp_name: name,
        level: 0,
        input: content.message.text,
        unique_identifier: 'start',
        expected_input_type: 'SELECTION',
        country: null,
        country_id: null,
        path: 'start'
    }, function(err, msg) {
        if (err) {

        } else {
            salute = salute + '\n' + whatsAppMenuService.getMenu(0, 'start', { country: null })
            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, salute, function(err, wa_response) {
                if (err) {
                    console.error(content.from + ' | wa_response: ' + err)
                } else {
                    // whatsAppSessionService.startSession(content.from,'name','Emmanuel')
                    try {
                        res.json(wa_response)
                    } catch (e) {

                    }
                }

            })

        }


    });

}

function sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, path) {
    whatsAppMessageService.sendWhatsAppMessage(source_country, phone_no, menu, function(err, wa_response) {
        if (err) {
            console.error(phone_no + ' | wa_response: ' + err)
            try {
                res.json(false)
            } catch (e) {

            }
        } else {
            // if (shouldUpdateSession)

            whatsappDBService.updateUserSession({
                phone_no: phone_no,
                expected_input_type: input_type,
                unique_identifier: unique_identifier,
                level: level,
                path: path
            }, function(err, session_response) {

                if (err) {
                    console.error(phone_no + ' | Existing Error updating user session : ' + session_response)
                        // res.json(err)
                } else {
                    // console.log('All done ' + wa_response)
                    try {
                        res.json(true)
                    } catch (e) {

                    }
                }
            })
        }
    })
}

function getTicketResponseMessage(name, ticket_no, unique_identifier = null) {

    if (unique_identifier !== null && unique_identifier == 'bank_with_us_option_success_end_message')
        return 'Hi ' + name + ', Thank you for contacting us. Your ticket number is ' + ticket_no + '. You will be contacted by our call centre team shortly. ' +
            'To view our online portal, click here: https://www.faulukenya.com/ and to view our T&Cs click here: https://bit.ly/3gF1AnI\n\n0. Main Menu'
    else if (unique_identifier !== null && unique_identifier === 'provide_details_car_photos')
        return 'Hi ' + name + ', Thank you for contacting us. Your ticket number is ' + ticket_no + '. You will be contacted by our call centre team shortly. ' +
            'To view our online portal, click here: ' + site + ' \n\n0. Main Menu'

    else
        return 'Hi ' + name + ', Thank you for contacting us. Your ticket number is ' + ticket_no + '. You will be contacted by our call centre team shortly. ' +
            'To view our online portal, click here: ' + site + ' \n\n0. Main Menu'

}


function updateCountry(params) {
    console.log(`params ${params}`)
    whatsappDBService.updateCountry(params, function(err, msg) {
        if (err) {
            console.log('Error updating country')
        } else {
            console.log('Country updated: ' + JSON.stringify(params))
        }
    })
}

function sendErrorMessage(phone_no, _res) {
    whatsAppMessageService.sendWhatsAppMessage(source_country, phone_no, 'Error processing your request. Please try again. \n\n0. Main Menu', function(err, wa_response) {
        if (err) {
            _res.json(false)
        } else {
            _res.json('Error processing your request. Please try again. \n\n0. Main Menu ')
        }
    })
}

function endSesssionGetData(phone_no, menu = '0') {
    endTicketSession(phone_no)
    selfservice.getSessionTicketNo(phone_no, async function(err, response) {
        formulateData(response, menu, phone_no)
    })
}
/**
 * Fetch claims data and send email
 * @param {} phone 
 */
const formulateClaimsData = async(phone_no, username) => {
    console.log(`formulateClaimsData ${username}`)
    selfservice.getSessionTicketNo(phone_no, async function(err, response) {
        fetchClaimsData(response, phone_no, username)
    })
}
const fetchClaimsData = async(ticket_no, phone_no, username) => {
    dotenv.config();
    sessionService.getAllUserFiles(ticket_no, function(err, response) {
        sessionService.getWhatsAppSessionData(ticket_no, function(err, resp) {
            if (err) {
                _res.json(false)
            } else {
                console.log(`SessionData length ${resp}`)
                console.log(`UserFiles length ${response}`)
                var attachment = []
                var count = 1
                var file_path = ''
                response.forEach(element => {
                    console.log(`URL ${process.env['SERVER_IP']}`)
                    console.log(element.dataValues.file_path)
                    file_path = process.env['SERVER_IP'] + '' + element.dataValues.file_path.substring(1);
                    console.log(`element.dataValues.file_path ${element.dataValues.file_path}`)
                    console.log(`file_path ${file_path}`)
                    attachment.push(file_path)
                    count++


                })
                var menuSelected = "Register Claim" + "\n\n"
                var body
                var userData = +"\n" + "Name: " + username + "\n" + "Phone Number: " + phone_no
                userData = userData.substring(1)
                resp.forEach(element => {
                    if (element.key === "provide_details_life") {
                        body = menuSelected + 'Life Insurance: ' + element.session_data + +"\n" + userData
                    } else if (element.key === "provide_details_health") {
                        body = menuSelected + 'Health Insurance: ' + element.session_data + "\n" + userData
                    } else if (element.key === "provide_details_car") {
                        body = menuSelected + 'Motor Insurance: ' + element.session_data + "\n" + userData
                    }
                })

                attachment = JSON.stringify(attachment)
                console.log(attachment)
                console.log(`body ${body}`)
                mailService.sendMail(body, "", "rw", attachment, function(err, resp) {
                    if (err) {
                        console.log(err)
                    }
                    console.log(resp.status)
                })

            }
        })


    })
}

function formulateData(ticket_no, menu, phone_no) {
    sessionService.getUserFiles(ticket_no, function(err, response) {
        sessionService.getWhatsAppSessionData(ticket_no, async function(err, resp) {
            if (err) {
                _res.json(false)
            } else {
                var firstName
                var surName
                var emailAddress
                var nationalId
                var passportNo
                var taxIdNumber
                var carMake
                var carYOM
                var carRegNo
                resp.forEach(element => {
                    if (element.key === "enter_first_name") {
                        firstName = element.session_data
                    } else if (element.key === "enter_sur_name") {
                        surName = element.session_data
                    } else if (element.key === "enter_email_address") {
                        emailAddress = element.session_data
                    } else if (element.key === "enter_national_id") {
                        nationalId = element.session_data
                    } else if (element.key === "enter_tax_id_number") {
                        taxIdNumber = element.session_data
                    } else if (element.key === "enter_passport") {
                        passportNo = element.session_data
                    } else if (element.key === "enter_car_make") {
                        carMake = element.session_data
                    } else if (element.key === "enter_car_year_make") {
                        carYOM = element.session_data
                    } else if (element.key === "enter_car_reg_number") {
                        carRegNo = element.session_data
                    }
                })
                var menuSelected = "Buy Insurance"
                var body = ''
                var identifcation = ''
                if (nationalId == undefined) {
                    identifcation = "Passport Number " + passportNo + "\n"
                } else {
                    identifcation = "National ID " + nationalId + "\n"
                }
                if (menu == "0") {
                    body = menuSelected + "\n" + "FirstName " + firstName + "\n" +
                        "Surname " + surName + "\n" +
                        "Email Address " + emailAddress + "\n" + "Phone Number " + phone_no + "\n" +
                        identifcation + "Tax ID Number " + taxIdNumber + "\n" +
                        "CarMake " + carMake + "\n" +
                        "Car Year Of Make " + carYOM + "\n" +
                        "Car Registration Number " + carRegNo
                } else {
                    body = menuSelected + "\n" + "FirstName " + firstName + "\n" +
                        "Surname " + surName + "\n" +
                        "Email Address " + emailAddress + "\n" + "Phone Number " + phone_no + "\n" +
                        identifcation + "Tax ID Number " + taxIdNumber
                }

                var attachment = []
                var file_path = process.env['SERVER_IP'] + '' + response.file_path.substring(1);
                attachment.push(file_path)
                attachment = JSON.stringify(attachment)
                console.log(attachment)
                mailService.sendMail(body, "", "rw", attachment, function(err, resp) {
                    if (err) {
                        console.log(err)
                    }
                    console.log(resp.status)
                })


            }


        })
    })

}


/**
 * formulateRequestCallBack and send email
 * @param {} phone 
 */
const formulateRequestCallBack = async(phone_no, username) => {
    var menuSelected = "Register Claim" + "\n"
    var task = "Request CallBack" + "\n"
    var body = ""
    body = menuSelected + " " + task + " Name: " + username + "\n" + "" + " PhoneNumber: " + phone_no + "\n" + ""
    console.log(`body ${body}`)
    mailService.sendMail(body, "", "rw", "", function(err, resp) {
        if (err) {
            console.log(err)
        }
        console.log(resp.status)
    })
}

module.exports = {
    handleUserText: handleUserText,
    handleUserImages,
    handleUserDocuments,
    handleUserVideos
}