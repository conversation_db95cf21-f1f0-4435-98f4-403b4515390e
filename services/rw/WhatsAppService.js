'use strict';

const request = require('request');
const util = require('util');
const https = require('https');
const fs = require('fs');
const download = require('image-downloader');
const filepath_details = require('path')
let menuService = require('../WhatsAppMenuService');
let expected_input_type = 'SELECTION';
let ticket_no;
const dotenv = require('dotenv')

var sessionService = require('./SessionService');
let utils = require('../Utils');
var { whatsapp_user_session } = require("./DbService")

async function sendWhatsAppMessage(source_country, phone_no, msg, callback) {
    dotenv.config();
    try {
        // console.log(utils.getDateTime() + '| ' + phone_no + ' | RES: Message to WA')

        console.log('--> Message to send: ' + msg)
        callback(false, msg);
        source_country = source_country.toUpperCase()
        console.log('Source country: ' + source_country)
        let url = process.env[source_country + '_WHATSAPP_BASE_URL'] + "/omni/1/advanced"
        let scenario_key = process.env[source_country + '_WHATSAPP_SCENARIO_KEY']
        console.log('URL:', url)
        console.log('scenario_key:', scenario_key)
        request.post({
            url: url,
            headers: {
                'Authorization': 'App ' + process.env[source_country + '_WHATSAPP_AUTHORIZATION'],
                'Content-Type': 'application/json'
            },
            body: {
                "scenarioKey": process.env[source_country + '_WHATSAPP_SCENARIO_KEY'],
                "destinations": [{
                    "to": {
                        "phoneNumber": phone_no
                    }
                }],
                "whatsApp": {
                    "text": msg
                },
                "sms": {
                    "text": "This is the SMS fail over message"
                }
            },
            json: true
        }, function(error, response, body) {
            if (error) {
                console.error('Error sending WA message: ' + error)
                callback(true, "Error sending WA message:");
                // process.on('uncaughtException', handleErrors);
            } else {
                console.log(utils.getDateTime() + ' | ' + phone_no + ' | RES: Sent status received from WA')
                callback(false, response.body);
            }
        });
    } catch (e) {
        console.log('Error', e);
    }
}

async function getMainMenu(phone_no, text, callback) {
    try {
        sessionService.updateWhatsAppSession(phone_no, 1, 'start', 1, text, expected_input_type)
        callback(false, menuService.getMenu(1, 'start'))

    } catch (e) {

    }
}

async function getSessionLevel(phone_no, text, callback) {
    try {

        // sessionService.updateWhatsAppSession(phone_no, text)
        whatsapp_user_session.findOne({
            where: {
                phone_no
            }
        }).then(result => {
            if (result) {
                console.log('User session -> ' + result.level);
                console.log('input type -> ' + result.unique_identifier);

                let menu_resp;
                let level = 0;
                if (result.expected_input_type == 'END')
                    callback(false, 'Thank you for the details provided, One of our call agent will reach you within 24hrs')
                if (result.expected_input_type == 'SELECTION') {
                    let unique_identifier = menuService.getMenuTags(result.level, result.unique_identifier)[text]
                    console.log('unique_identifier -> ' + unique_identifier);

                    if (unique_identifier == 'Back') {
                        level = result.level - 1;
                    } else if (unique_identifier == undefined)
                        level = result.level
                    else
                        level = result.level + 1;

                    menu_resp = menuService.getMenu(level, unique_identifier)
                    console.log('unique_identifier -> ' + unique_identifier + '\nresult.level -> ' + result.level);

                    if (menu_resp == undefined) {
                        unique_identifier = 'start'
                        menu_resp = menuService.getMenu(1, unique_identifier)
                        level = 1;
                    }
                    console.log('Menu -> ' + menu_resp);
                    if (result.path == '') {
                        result.path = text
                    } else
                        result.path = result.level + '*' + text
                    sessionService.updateWhatsAppSession(phone_no, result.level + 1, unique_identifier, result.path, text, menuService.getMenuBlock(result.level + 1, unique_identifier))

                    console.log('input type -> ' + menuService.getMenuBlock(level, unique_identifier));
                } else if (result.expected_input_type == 'TEXT') {
                    console.log('unique_identifier -> ' + result.unique_identifier + '--s-- result.level -> ' + result.level);
                    let unique_identifier = menuService.getMenuTags(result.level, result.unique_identifier)[result.input]
                    console.log('unique_identifier -> ' + unique_identifier + '--s-- result.level -> ' + result.level);
                    sessionService.updateWhatsAppSession(phone_no, result.level + 1, unique_identifier, result.path + '*' + text, text, menuService.getMenuBlock(result.level + 1, unique_identifier))
                    menu_resp = menuService.getMenu(result.level + 1, unique_identifier)
                } else if (result.expected_input_type == 'IMAGE') {
                    level = result.level
                    sessionService.updateWhatsAppSession(phone_no, level, unique_identifier, result.path + '*' + text, text, menuService.getMenuBlock(level, result.unique_identifier))
                    menu_resp = menuService.getMenu(level, unique_identifier)
                }

                callback(false, menu_resp)
            } else {
                sessionService.updateWhatsAppSession(phone_no, 1, 'start', 1, text, expected_input_type)
                let menu_resp = menuService.getMenu(1, text)
                if (menu_resp == undefined) {
                    menu_resp = menuService.getMenu(1, 'start')
                }
                callback(false, menu_resp)
                console.log('Created Menu -> ' + menu_resp);
            }
        });
    } catch (e) {

    }
}

async function whatsAppReceiver(msg, callback) {
    try {


        let caption;
        let url;
        let longitude;
        let latitude;
        let contact_name;

        let type = Object.values(msg.results);

        console.log(util.inspect(type[0].message, false, null, true /* enable colors */ ));


        let from = type[0].from;
        let to = type[0].to;
        let message_type = type[0].message.type;
        let source_channel = '2';

        get_ticket_no(from, source_channel, message_type, caption);

        console.log("Message type => " + message_type);

        if (message_type === "text") {
            contact_name = type[0].contact.name;

        }

        if (message_type === "LOCATION") {
            longitude = type[0].message.longitude;
            latitude = type[0].message.latitude;
        }

        if (message_type === "IMAGE" || message_type === "VIDEO" || message_type === "DOCUMENT") {
            caption = type[0].message.caption;
            url = type[0].message.url;

            url = "https://www.safaricom.co.ke/templates/safaricommainv2/images/logo-white.png";


        }

        // console.log("Caption => " + caption + "  URL = > " + url);


        callback(false, msg);

    } catch (e) {
        console.log('Error => ', e);
        callback(false, e);
    }
}


module.exports = {
    sendWhatsAppMessage: sendWhatsAppMessage,
    whatsAppReceiver: whatsAppReceiver,
    getSessionLevel: getSessionLevel,
    getMainMenu: getMainMenu
}