'use strict';

var { wa_user, whatsapp_user_session, user_session, wa_session_data, wa_user_session, wa_health_worker, wa_user_files } = require("./DbService")


async function updateSession(args) {
    user_session.upsert({ phone_no: args.phoneNumber, session_id: args.sessionId, path_taken: args.text })
}

async function saveWhatsAppUser(phone_no, whatsapp_name, country, callback) {
    wa_user.upsert({
        phone_no: phone_no,
        whatsapp_name: whatsapp_name,
        country: country
    }).then(result => {
        console.log("RESP : " + result);
        callback(false, result);
    })
}


async function updateWhatsAppUser(phone_no, whatsapp_name, country, callback) {

    // console.log("OUR PHONE No => " + phone_no);
    var values = {
        phone_no: phone_no,
        whatsapp_name: whatsapp_name,
        country: country
    };
    var condition = { where: { phone_no: phone_no } };
    var options = { upsert: false };


    wa_user.update(values, condition, options)
        .then(result => {
            if (result) {
                callback(false, result)
            } else {
                callback(true, 'Update failed');
            }
        })


}


async function checkHealthCareUserExistance(phone_no, callback) {

    /* db.wa_health_worker.findOne({
             where: {
                 phone_no: phone_no
             }
         }
     ).then(result => {
         if (result)
             callback(true, result);
         else
             callback(false, result);
     })*/
}

async function updateWhatsAppSession(phone_no, level, unique_identifier, path, text, expected_input_type) {
    whatsapp_user_session.upsert({
        phone_no: phone_no,
        level: level,
        input: text,
        unique_identifier: unique_identifier,
        expected_input_type: expected_input_type,
        path: path
    }).then(result => {
        console.log('RESP:' + result)
    })
}

async function saveWhatsAppFileSession(phone_no, file_path, ticket_no, caption, file_url, callback) {

    wa_user_files.upsert({
        phone_no: phone_no,
        file_path: file_path,
        ticket_no: ticket_no,
        caption: caption,
        file_url: file_url
    }).then(result => {
        callback(false, result);
    });


}


async function saveWhatsAppSessionData(phone_no, key, ticket_no, session_data, callback) {

    let status = 'Active';
    wa_session_data.upsert({
        phone_no: phone_no,
        key: key,
        ticket_no: ticket_no,
        session_data: session_data,
        status: status
    }).then(result => {
        console.log(result);
        callback(false, result);
    });


}

async function getWhatsAppSessionFiles(ticket_no, callback) {

    wa_user_files.findAll({
        where: { ticket_no: ticket_no }
    }).then(
        result => {
            if (result) {
                callback(false, result)
            }
        });
}

async function saveWhatsAppUserSession(phone_no, ticket_no, level, input, unique_identifier, expected_input_type, path, callback) {

    wa_user_session.upsert({
        phone_no: phone_no,
        ticket_no: ticket_no,
        level: level,
        input: input,
        unique_identifier: unique_identifier,
        expected_input_type: expected_input_type,
        path: path,
    }).then(
        result => {
            if (result) {
                callback(false, result)
            }
        }
    );

}


async function getWhatsAppHealthWorkerData(phone_no, callback) {
    wa_health_worker.findAll({
        where: { phone_no: phone_no, status: 'Active' }
    }).then(
        result => {
            if (result) {
                callback(false, result)
            }
        });
}


async function CompleteWhatsAppHealthWorker(phone_no, callback) {


    // console.log("OUR PHONE No => " + phone_no);
    var values = { status: 'In Active' };
    var condition = { where: { phone_no: phone_no } };
    var options = { upsert: true };

    wa_health_worker.update(values, condition, options)
        .then(result => {
            if (result) {
                callback(false, result)
            } else {
                callback(true, 'Update failed');
            }
        })


}


async function saveWhatsAppHealthWorker(phone_no, key, value, callback) {
    // console.log('Result: ' + phone_no + ' Result: ' + key + ' Result: ' + value)
    wa_health_worker.findOne({
        where: {
            phone_no: phone_no,
            key: key
        }
    }).then(result => {
        if (result) {
            // console.log('Result: ' + result)
            wa_health_worker.update({
                value: value,
                status: 'Active'
            }, {
                where: {
                    id: result.id
                }
            }).then(res => {
                if (res)
                    callback(false, res)
                else
                    callback(false, 'Error saving health worker detail.')
            })
        } else {
            console.log(phone_no + ' | Create Result: ' + result)
            wa_health_worker.create({
                phone_no: phone_no,
                key: key,
                value: value,
                status: 'Active'
            }).then(res => {
                if (res)
                    callback(false, res)
                else
                    callback(false, 'Error saving health worker detail.')
            })
        }

    })

    /* db.wa_health_worker.upsert({
         phone_no: phone_no,
         key: key,
         value: value,

     }).then(
         result => {
             if (result) {
                 callback(false, result)
             }
         }
     );*/

}


async function closeWhatsAppUserSession(params, ticket_no, callback) {

    console.log(ticket_no);
    wa_user_session.findOne({
        where: { ticket_no: ticket_no }
    }).then(
        result => {
            if (result) {
                // console.log("VALIDATE =? " + ticket_no);
                wa_user_session.update({ status: 'COMPLETE' }, {
                    where: { ticket_no: ticket_no }

                }).then(result2 => {
                    //'ACTIVE','INCOMPLETE','COMPLETE'

                    if (result2) {
                        console.log("Success");
                        callback(false, result2);
                    } else {
                        console.log("FAILED ");
                        callback(true, 'Update failed')
                    }
                })
            } else {
                console.log("FAILED ");
                callback(true, 'Record not found');
            }
        });


    // var values = {status: 'COMPLETE'};
    // var condition = {where: {ticket_no: ticket_no}};
    // var options = {upsert: true};
    //
    //
    // db.wa_user_session.update(values, condition, options)
    //     .then(result => {
    //         if (result)
    //             callback(false, result)
    //         else {
    //             callback(true, 'Update failed')
    //         }
    //     })


}


// //
// async function closeWhatsAppUserSession(phone_no, ticket_no, callback) {
//
//
//
// console.log("OUR Ticket No => " + ticket_no);
// console.log("OUR PHONE No => " + phone_no);
// var values = {status: 'COMPLETE'};
// var condition = {where: {phone_no: phone_no, ticket_no: ticket_no}};
// var options = {upsert: true};
//
//
// db.wa_user_session.update(values, condition, options)
//     .then(result => {
//         if (result) {
//             callback(false, result)
//         }
//     })
//
//
// }
//


async function getWhatsAppSessionData(ticket_no, callback) {
    wa_session_data.findAll({
        where: { ticket_no: ticket_no }
    }).then(
        result => {
            if (result) {
                callback(false, result)
            }
        });
}

async function checkActiveWhatsAppTicketNo(phone_no, callback) {
    let status = 'ACTIVE';
    wa_user_session.findAll({
        where: {
            phone_no: phone_no,
            status: status
        }
    }).then(
        result => {
            if (result) {

                callback(false, result)
            }
        });
}


function getWhatsAppCountry(phone_no, callback) {
    whatsapp_user_session.findOne({
        where: {
            phone_no: phone_no
        }
    }).then(
        result => {
            if (result) {

                callback(false, result)
            }
        });
}

function getUserFiles(ticket_no, callback) {
    wa_user_files.findOne({
        where: {
            ticket_no: ticket_no
        }
    }).then(
        result => {
            if (result) {
                callback(false, result)
            }
        });
}

async function getAllUserFiles(ticket_no, callback) {
    wa_user_files.findAll({
        where: {
            ticket_no: ticket_no
        }
    }).then(
        result => {
            if (result) {
                callback(false, result)
            }
        });
}

module.exports = {
    updateSession: updateSession,
    updateWhatsAppSession: updateWhatsAppSession,
    saveWhatsAppFileSession: saveWhatsAppFileSession,
    getWhatsAppSessionFiles: getWhatsAppSessionFiles,
    checkActiveWhatsAppTicketNo: checkActiveWhatsAppTicketNo,
    saveWhatsAppSessionData: saveWhatsAppSessionData,
    saveWhatsAppUser: saveWhatsAppUser,
    saveWhatsAppUserSession: saveWhatsAppUserSession,
    closeWhatsAppUserSession: closeWhatsAppUserSession,
    getWhatsAppSessionData: getWhatsAppSessionData,
    saveWhatsAppHealthWorker: saveWhatsAppHealthWorker,
    getWhatsAppHealthWorkerData: getWhatsAppHealthWorkerData,
    CompleteWhatsAppHealthWorker: CompleteWhatsAppHealthWorker,
    checkHealthCareUserExistance: checkHealthCareUserExistance,
    getWhatsAppCountry: getWhatsAppCountry,
    updateWhatsAppUser: updateWhatsAppUser,
    getUserFiles: getUserFiles,
    getAllUserFiles: getAllUserFiles
}