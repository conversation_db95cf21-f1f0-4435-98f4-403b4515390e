exports.getMenuContent = function() {
    return {
        "0": {
            "start": {
                "title": "Kindly Select your Country of Choice (respond with the corresponding number of your choice)\n1 Kenya\n2 Uganda\n3 Tanzania\n4 Rwanda\n5 South Sudan",
                "next": {
                    "1": "kenya",
                    "2": "uganda",
                    "3": "tanzania",
                    "4": "rwanda",
                    "5": "south_sudan"
                },
                "input_type": "SELECTION"
            }
        },
        "1": {
            "rwanda": {
                "title": "How can we be of service today?\n1. Register Claim\n2. Buy Insurance",
                "next": {
                    "1": "register_claim",
                    "2": "buy_insurance"
                },
                "input_type": "SELECTION"
            }
        },
        "2": {
            "register_claim": {
                "title": "Which insurance class do you want to register claim for?\n1. Motor Insurance\n2. Health Insurance\n3. Life Insurance\n0. Back to Main Menu",
                "next": {
                    "1": "claim_car",
                    "2": "claim_health",
                    "3": "claim_life",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status": {
                "title": "Policy status for? (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Motor Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "policy_status_health",
                    "2": "policy_status_car",
                    "3": "policy_status_life",
                    "4": "policy_status_other",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "buy_insurance": {
                "title": "Which insurance would like to purchase?\n1. Motor Insurance\n2. Health Insurance\n0. Main Menu",
                "next": {
                    "1": "enter_first_name",
                    "2": "enter_first_name_health",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment": {
                "title": "Make payment to? (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Motor Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "make_insurance_payment_health",
                    "2": "make_insurance_payment_car",
                    "3": "make_insurance_payment_life",
                    "4": "make_insurance_payment_other",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "invest_with_us": {
                "title": "We have a fully dedicated investment platform i-INVEST\nClick here https://bit.ly/3bDgln4 to chat with Arifa our online virtual assistant. Type 'hi Arifa' in your Facebook Messenger chat to start investing.\nor\nDial *480#\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "new_customer_verification": {
                "title": "Which Insurance class have you bought: (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Motor Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "verify_enter_name",
                    "2": "verify_enter_name",
                    "3": "verify_enter_name",
                    "4": "verify_enter_name",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "update_personal_details": {
                "title": "To update your personal details kindly click on this link https://bit.ly/2LazEcA#",
                "next": {
                    "1": "end"
                },
                "input_type": "END"
            },
            "bank_with_us": {
                "title": "Which Faulu Micro-finance Bank product are you interested In?  (respond with the corresponding number of your choice)\n1. Loan\n2. Current Account\n3. Savings Account\n4. Fixed Deposit Account\n0 Main Menu",
                "next": {
                    "1": "bank_with_us_option_success_end_message",
                    "2": "bank_with_us_option_success_end_message",
                    "3": "bank_with_us_option_success_end_message",
                    "4": "bank_with_us_option_success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "health_worker": {
                "title": "How can we help you? (respond with the corresponding number of your choice)\n1. New User Registration\n2. Register a Claim \n0 Main Menu",
                "next": {
                    "1": "health_worker_profession",
                    "2": "health_worker_register_claim_type",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "covid": {
                "title": "Which option would you prefer an update on? (respond with the corresponding number of your choice)\n1. Keeping Healthy\n2. Feeling Unwell\n3. Working From Home\n4. Studying From Home\n5. Unwinding From Home\n6. Ministry of Health Updates\n0 Main Menu",
                "next": {
                    "1": "covid_update_message",
                    "2": "covid_update_message",
                    "3": "covid_update_message",
                    "4": "covid_update_message",
                    "5": "covid_update_message",
                    "6": "covid_update_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            }
        },
        "3": {
            "enter_first_name": {
                "title": "Kindly enter your First Name",
                "next": {
                    "1": "enter_sur_name",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "enter_first_name_health": {
                "title": "Kindly enter your First Name",
                "next": {
                    "1": "enter_sur_name_health",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "claim_health": {
                "title": "Select how to start the Insurance claim:\n1. Provide claim details \n2. Request a call back \n0. Back to Main Menu",
                "next": {
                    "1": "provide_details_health",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "claim_car": {
                "title": "Select how to start the Insurance claim:\n1. Provide claim details \n2. Request a call back \n0. Back to Main Menu",
                "next": {
                    "1": "provide_details_car",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "claim_life": {
                "title": "Select how to start the Insurance claim: \n1. Provide claim details \n2. Request a call back \n0. Back to Main Menu",
                "next": {
                    "1": "provide_details_life",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "claim_other": {
                "title": "Select how to start the Insurance claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_other",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_health": {
                "title": "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_health",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_car": {
                "title": "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_car",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_life": {
                "title": "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_life",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_other": {
                "title": "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_other",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "buy_insurance_health_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "buy_insurance_car_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "buy_insurance_life_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "buy_insurance_other_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "make_insurance_payment_health": {
                "title": "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_health",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment_car": {
                "title": "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_car",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment_life": {
                "title": "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_life",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment_other": {
                "title": "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_other",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "verify_enter_name": {
                "title": "Kindly enter your full name?\n0 Main Menu",
                "next": {
                    "1": "verify_id_passport",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "bank_with_us_option_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "covid_update_message": {
                "title": "Dear customer, please check health tips here <>  \n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "health_worker_profession": {
                "title": "Kindly select your profession (respond with the corresponding number of your choice)\n1. Doctor\n2. Nurse\n3. Clinical Officer\n4. Laboratory Technician\n0 Main Menu",
                "next": {
                    "1": "health_worker_name",
                    "2": "health_worker_name",
                    "3": "health_worker_name",
                    "4": "health_worker_name",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "health_worker_register_claim_type": {
                "title": "Kindly select the type of claim (respond with the corresponding number of your choice)\n1. Hospital Benefit \n2. Last Expense Benefit\n0 Main Menu",
                "next": {
                    "1": "health_worker_register_claim_hosp",
                    "2": "health_worker_register_claim_last",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            }
        },
        "4": {
            "enter_sur_name": {
                "title": "Kindly enter your Surname",
                "next": {
                    "1": "enter_email_address",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "enter_sur_name_health": {
                "title": "Kindly enter your Surname",
                "next": {
                    "1": "enter_email_address_health",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "request_callback": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "provide_details_policy_status_health": {
                "title": "What\'s Your Health Insurance Policy Number? eg. UK032344-01 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_policy_status_car": {
                "title": "What\'s Your Car Registration Number? e.g RAA654U\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_policy_status_other": {
                "title": "What\'s Your Insurance Policy Number?\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_policy_status_life": {
                "title": "What\'s your Life Insurance policy number? e.g. OMK001429206 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_payment_health": {
                "title": "What\'s Your Health Insurance Policy Number? eg. UK032344-01 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_payment_car": {
                "title": "What\'s Your Car Registration Number? e.g RAA654U\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_payment_other": {
                "title": "What\'s Your Insurance Policy Number?\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_payment_life": {
                "title": "What\'s Your Life Insurance Policy Number? e.g. OMK001429206 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_car": {
                "title": "Enter your car registration number \n(e.g RAA654U)\n",
                "next": {
                    "1": "provide_details_car_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },

            "provide_details_other": {
                "title": "What\'s Your Insurance policy number?\n \n0 Main Menu",
                "next": {
                    "1": "provide_details_other_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_life": {
                "title": "Enter your Insurance policy number\n (e.g 01020304)\n",
                "next": {
                    "1": "provide_details_life_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health": {
                "title": "Enter your Insurance policy number\n (e.g.UK032344-01)\n",
                "next": {
                    "1": "provide_details_health_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "verify_id_passport": {
                "title": "What\'s your National ID e.g. 1234567 or Passport Number e.g. ********\n\n0 Main Menu",
                "next": {
                    "1": "verify_id_photo",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "health_worker_name": {
                "title": "Kindly enter your  first name and surname\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_national_id",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "health_worker_register_claim_hosp": {
                "title": "Select how to start your Hospital Benefit claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_id",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "health_worker_register_claim_last": {
                "title": "Select how to start the Last Expense Benefit claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_last_claim_id",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
        },
        "5": {
            "enter_email_address": {
                "title": "Kindly enter your email address",
                "next": {
                    "1": "select_identification_document",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "enter_email_address_health": {
                "title": "Kindly enter your email address",
                "next": {
                    "1": "select_identification_document_health",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_photos": {
                "title": "Kindly share photos of your insurance claim supporting documents listed below.\n- Duly filled outpatient/reimbursement claim form \n- Copy of drug prescription form\n- Original receipts\n- Copy of lab request form\n\nType 1 when done uploading.\n0 Main Menu",
                "next": {
                    "1": "provide_details_photos_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_car_photos": {
                "title": "Share photos of your car and any other photos of supporting documents listed below\n- Completed Claim Form \n- Copy of the log book \n- Police Abstract Report \n- Motor Vehicle Inspection report (for third party claims)\n- Copy of the Driver’s Driving licence\n- Notice of intention to prosecute(if any)\n- Applicable excess\n\nType 1 to complete your submission\n0. Main Menu",
                "next": {
                    "1": "provide_details_photos_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_life_photos": {
                "title": "Share a photos of your Insurance Supporting Documents. \n-Policy document or Policy schedule\n\nType 1 to complete your submission\n0. Main Menu",
                "next": {
                    "1": "provide_details_photos_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },

            "provide_details_other_photos": {
                "title": "Share photo of your Insurance supporting Documents? e.g. Medical receipts, Death certificate, Police abstract, Damaged property\n\nType 1 to complete your submission\n0 Main Menu",
                "next": {
                    "1": "provide_details_photos_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },

            "provide_details": {
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "verify_id_photo": {
                "title": "Kindly Take & Upload a Photo of Your National ID or Passport \n\n0 Main Menu",
                "next": {
                    "1": "selfie_passport_photo",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "health_worker_national_id": {
                "title": "Kindly enter your National ID Number e.g. 12345678\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_facility",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_id": {
                "title": "Kindly enter your National ID Number? e.g. 12345678\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_staff_card",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_hosp_claim_id": {
                "title": "Kindly share photo of your National ID Card\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_staff_card",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_last_claim_id": {
                "title": "Kindly share photo of your National ID Card\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_last_claim_id_hw",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "request_callback": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
        },
        "6": {
            "select_identification_document": {
                "title": "Kindly select your identifcation document\n1. National ID number\n2. Valid International Passport",
                "next": {
                    "1": "photo_national_id",
                    "2": "photo_passport"
                },
                "input_type": "SELECTION"
            },
            "select_identification_document_health": {
                "title": "Kindly select your identifcation document\n1. National ID number\n2. Valid International Passport",
                "next": {
                    "1": "photo_national_id_health",
                    "2": "photo_passport_health"
                },
                "input_type": "SELECTION"
            },
            "provide_details_car_additional_photos": {
                "title": "Share a photo of police abstract or any other supportive photos\n\n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_other_additional_photos": {
                "title": "Share a photo of additional supportive documents\n 1. Complete Submission \n0. Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_photos_end": {
                "title": "Thank you for the details provided, one of our Call Center representatives will contact you within 24 Hours.\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "provide_details_life_additional_photos": {
                "title": "Share a photo of additional supportive documents\n 1. Complete Submission \n0. Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "selfie_passport_photo": {
                "title": "Kindly take & upload a Selfie or Passport Photo\n0. Main Menu",
                "next": {
                    "1": "signup_phone_no",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "health_worker_facility": {
                "title": "Kindly enter your primary health facility of operation?\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_reg_no",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_supporting_docs": {
                "title": "Kindly share photos of your insurance claim supporting documents e.g. Medical Receipts, Hospital Admission Form or Death Certificate\n\nMaximum 10 attachments.\n\nType 1 when done.\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_hosp_claim_staff_card": {
                "title": "Kindly share your a photo of staff card of employing health facility\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_discharge",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_last_claim_id_hw": {
                "title": "Kindly share a photo of the Health Worker\'s National ID Card\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_last_facility",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
        },
        "7": {
            /*"enter_national_id": {
                "title": "Kindly enter your National ID Number(eg 01020304)?",
                "next": {
                    "1": "photo_national_id",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "enter_passport": {
                "title": "Kindly enter your Valid Passport Number?",
                "next": {
                    "1": "photo_passport",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },*/
            "photo_national_id": {
                "title": "Kindly take and upload a image of your National ID",
                "next": {
                    "1": "enter_tax_id_number",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "photo_passport": {
                "title": "Kindly take and upload a image of your Passport",
                "next": {
                    "1": "enter_tax_id_number",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "photo_national_id_health": {
                "title": "Kindly take and upload a image of your National ID",
                "next": {
                    "1": "enter_tax_id_number_health",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "photo_passport_health": {
                "title": "Kindly take and upload a image of your Passport",
                "next": {
                    "1": "enter_tax_id_number_health",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "signup_phone_no": {
                "title": "Kindly enter the phone number you used to sign-up for the new product? e.g. 0721******\n0. Main Menu",
                "next": {
                    "1": "otp_verification",
                    "2": "end"
                },
                "input_type": "TEXT"
            },
            "health_worker_reg_no": {
                "title": "Kindly enter your professional registration / licence number\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_beneficiary_name",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_end": {
                "title": "We have successfully received your claim notification.\n One of our call center representatives will contact you within 24 Hours.\n\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "provide_details_health_worker_hosp_claim_discharge": {
                "title": "Kindly share your hospitalization discharge summary indicating dates of admission and discharge.\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_covid_report",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_last_facility": {
                "title": "Kindly share a photo staff ID card of the facility where the Healthcare Worker worked.\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_last_death_letter",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            }
        },
        "8": {
            /*"photo_national_id": {
                "title": "Kindly take and upload a image of your National ID",
                "next": {
                    "1": "enter_tax_id_number",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "photo_passport": {
                "title": "Kindly take and upload a image of your Passport",
                "next": {
                    "1": "enter_tax_id_number",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },*/
            "enter_tax_id_number": {
                "title": "Kindly enter your Tax Identification Number(Tin)e.g *********",
                "next": {
                    "1": "enter_car_make",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "enter_tax_id_number_health": {
                "title": "Kindly enter your Tax Identification Number(Tin)e.g *********",
                "next": {
                    "1": "end",
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "end": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uaprwanda.rw/",
                "input_type": "END"
            },
            "back_to_main": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uaprwanda.rw/",
                "input_type": "END"
            },
            "otp_verification": {
                "title": "Kindly Insert the 4 Digit Code Sent To You Via SMS\n0 Main Menu\n00. Resend OTP",
                "next": {
                    "1": "customer_verification_success_end_message",
                    "0": "back_to_main",
                    "00": "otp_verification",
                },
                "input_type": "TEXT"
            },
            "health_worker_beneficiary_name": {
                "title": "Beneficiary Information\nKindly enter your beneficiary\'s name (first name & surname)\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_beneficiary_phone_no",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_hosp_claim_covid_report": {
                "title": "Kindly share your positive COVID-19 diagnosis report\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_last_death_letter": {
                "title": "Kindly share a photo of Police or Hospital Death Notification Letter indicating details of death\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            }
        },
        "9": {
            /*"enter_tax_id_number": {
                "title": "Kindly enter your Tax Identification Number(Tin)e.g *********",
                "next": {
                    "1": "enter_car_make",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },*/
            "enter_car_make": {
                "title": "Kindly enter your Car Make e.g. Honda CRV",
                "next": {
                    "1": "enter_car_year_make",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "customer_verification_success_end_message": {
                "title": "We have successfully received your details. Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uaprwanda.rw/",
                "input_type": "END"
            },
            "end": {
                "title": "Thank you for your details. One of our consultants will call you shortly.",
                "input_type": "END"
            },
            "health_worker_beneficiary_phone_no": {
                "title": "Kindly enter your beneficiary\'s phone number\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_registration_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_success_end_message": {
                "title": "Kindly respond with \n1. To complete Claim Submission\n\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            }
        },
        "10": {
            /*"enter_car_make": {
                "title": "Kindly enter your Car Make e.g. Honda CRV",
                "next": {
                    "1": "enter_car_year_make",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },*/
            "enter_car_year_make": {
                "title": "Kindly enter your Car Year of Manufacture.",
                "next": {
                    "1": "enter_car_reg_number",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "end": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uaprwanda.rw/",
                "input_type": "END"
            },
            "health_worker_registration_end_message": {
                "title": "We have successfully received your details.\nTo view our terms and conditions, click here: https://www.uaprwanda.rw/privacy-policy and to view our online portal, click here: https://www.uaprwanda.rw/ \n\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "success_end_message": {
                "title": "Thank you for your details. One of our consultants will call you shortly.",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            }
        },
        "11": {
            /*"enter_car_year_make": {
                "title": "Kindly enter your Car Year of Manufacture.",
                "next": {
                    "1": "enter_car_reg_number",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            }*/
            "enter_car_reg_number": {
                "title": "Enter your car registration number. eg RAA 654U",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            }
        },
        "12": {
            /*"enter_car_reg_number": {
                "title": "Enter your car registration number. eg RAA 654U",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },*/
            "success_end_message": {
                "title": "Thank you for your details. One of our consultants will call you shortly.",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            }
        },
        /*"13": {
            "success_end_message": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uaprwanda.rw/",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            }
        },*/

        "generic": {
            "response_options": {
                "next": {
                    "1": "provide_details",
                    "2": "request_callback",
                    "0": "back_to_main"
                }
            },
            "provide_details": {
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                }
            },
            "success_end_message": {
                "title": "Thank you for your details. One of our consultants will call you shortly.",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "exit": {
                "title": "Thank You For the Details Provided, One of Our Call Center Agents Will Call You Within 24 Hours."
            },
            "covid_end_message": {
                "title": "A Link Has Sent To You Via SMS on"
            },
            "covid_update_message": {
                "title": "A Link Has Been Sent To You Via SMS on  \n0 Main Menu",
                "next": {
                    "0": "back_to_main",
                    "11": "exit"
                }
            },
            "covid_menu": {
                "title": "A Link Has Sent To You Via SMS on",
                "1": "How to Keep Healthy\n0 Main Menu",
                "2": "What To Do If You Feel Unwell\n0 Main Menu",
                "3": "How to Work From Home\n0 Main Menu",
                "4": "How to Study From Home\n0 Main Menu",
                "5": "How to Unwind From Home\n0 Main Menu",
                "6": "Ministry of Health Updates\n0 Main Menu",
                "0": "Back to Menu"
            },
            "insurance_class": [
                "Health Insurance",
                "Motor Insurance",
                "Life Insurance",
                "Other Insurance Insurance"
            ],
            "end": {
                "title": "Thank you for your details. One of our consultants will call you shortly.",
                "input_type": "END"
            }
        }
    }
}