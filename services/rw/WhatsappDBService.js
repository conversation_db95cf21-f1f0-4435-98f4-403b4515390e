'use strict';
let db = require('../../models');
let utils = require('../Utils');
var { wa_user, whatsapp_user_session, wa_requests } = require("./DbService")


async function updateUserSession(params, callback) {
    whatsapp_user_session.upsert(params).then(result => {
        callback(false, result)
            /*if (result)
                callback(false, result)
            else {
                callback(true,'Update failed')
            }*/
    });
}

async function updateCountry(params, callback) {
    whatsapp_user_session.update(params, { where: { id: params.id } }).then(result => {
        callback(false, result)
            /*if (result)
                callback(false, result)
            else {
                callback(true,'Update failed')
            }*/
    });
}

async function isRequestExisting(params, callback) {
    wa_requests.findOne({ where: { phone_no: params.phone_no, message_id: params.message_id } })
        .then(result => {
            if (result) {
                params.request_count = result.request_count + 1
                console.log(utils.getDateTime() + " | DUPLICATE REQ: " + JSON.stringify(params))
                    // console.log('To update: ' + JSON.stringify(params))
                wa_requests.update(params, { where: { id: result.id } }).then(r => {
                    callback(true)
                })
            } else {
                wa_requests.create(params).then(result => {
                    // console.log('WA request created? ' + result)
                    callback(false)
                })
            }

        })
}

async function updateWaUser(params, callback) {
    wa_user.upsert(params).then(result => {
        callback(false, result)
    })
}


async function getUserLevel(phone_no, callback) {
    whatsapp_user_session.findOne({
        where: { phone_no: phone_no }
    }).then(
        result => {
            if (result) {
                callback(false, result)
            } else {
                callback(true, 'User does not exit');
            }
        });
}

module.exports = {
    updateUserSession: updateUserSession,
    isRequestExisting: isRequestExisting,
    updateCountry: updateCountry,
    getUserLevel: getUserLevel
}