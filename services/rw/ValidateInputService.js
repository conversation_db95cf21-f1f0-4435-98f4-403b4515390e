let validator = require('validator');

exports.validatePhoneNo = function(received_message) {
    return validator.isMobilePhone(received_message);
};

exports.validateEmail = function(received_message) {
    return validator.isEmail(received_message);
};
exports.validateIdNumber = function(received_message, country_code = null) {
    if (country_code != null && country_code === 'na')
        return (!isNaN(received_message * 1) && received_message.length > 10 && received_message.length < 14) || received_message.length > 5; // 11 to 13
    else if (country_code != null && country_code === 'mw')
        return received_message.length > 6 && received_message.length < 14; // 7 to 13
    else if (country_code != null && country_code === 'bw')
        return (!isNaN(received_message * 1) && received_message.length > 7 && received_message.length < 11) || received_message.length > 5; // 8 to 10
    else if (country_code != null && country_code === 'sz')
        return (!isNaN(received_message * 1) && received_message.length === 13) || received_message.length > 5; // 13
    else if (country_code != null && country_code === 'ug')
        return (validator.isInt(received_message)) && (received_message.length >= 6 && received_message.length <= 9)
    else if (country_code != null && country_code === 'rw')
        return (validator.isInt(received_message)) && (received_message.length === 16)
    else
        return (received_message.length > 5 && received_message.length < 11);
};
exports.isValidIdOrPassportNo = function(received_message) {
    let body_part;
    if ((received_message.charAt(0).toLowerCase() != received_message.charAt(0).toUpperCase()) && received_message.length > 7 && !isNaN((received_message.substr(1, received_message.length - 1))) * 1) {

        body_part = {
            value: received_message.toUpperCase(),
            type: 'passport',
            error: null
        };
        console.log("==> TYPE: passport");
    } else if (!isNaN(received_message * 1) && received_message.length > 5 && received_message.length < 11) {
        body_part = {
            value: received_message,
            type: 'national_id',
            error: null
        };
        console.log("==> TYPE: National ID");
    } else {
        body_part = {
            value: received_message,
            error: 'Please enter a valid National ID No or Passport No'
        };
    }
    return body_part;
};

exports.validateCarRegistration = function(received_message) {
    // Car registration number should be 7 to 8 characters
    // The first three characters and the last character being
    //  alphanumeric while the rest being numeric.
    //UAK 117R
    var secOne = received_message.substring(0, 3)
    var secTwo = received_message.substring(4, received_message.length - 1)
    var secThree = received_message.charAt(received_message.length - 1)

    return (received_message.length >= 7 && received_message.length <= 8) && (validator.isAlphanumeric(secOne)) && (validator.isInt(secTwo)) && validator.isAlphanumeric(secThree)

};

exports.validateTaxIDNumber = function(received_message, country_code = null) {
    //The TIN number should be 10 numerical characters
    if (country_code != null && country_code === 'ug') {
        return (validator.isInt(received_message)) && (received_message.length === 10)
    } else if (country_code != null && country_code === 'rw') {
        return (validator.isInt(received_message)) && (received_message.length === 9)
    }

};

exports.validateString = function(word) {
    if ((!/[^a-zA-Z]/.test(word) && word.length > 1)) {
        return true
    } else {
        return false

    }
};



exports.validateYear = function(year) {
    return validator.isInt(year) && year.length == 4
};