'use strict';
var Sequelize = require("sequelize")
var env = process.env.NODE_ENV || 'development';
var config = require(__dirname + '/../../config/config_rw.json')[env];
let initModels = require('../../models/init-models').initModels;
var sequelize = new Sequelize(config.database, config.username, config.password, config.options);
sequelize.options.logging = false
var models = initModels(sequelize);
var wa_user = models.wa_user;
var whatsapp_user_session = models.whatsapp_user_session
var user_session = models.user_session
var wa_session_data = models.wa_session_data
var wa_user_session = models.wa_user_session
var wa_health_worker = models.wa_health_worker
var wa_user_files = models.wa_user_files
var wa_requests = models.wa_requests



module.exports = {
    wa_user: wa_user,
    whatsapp_user_session: whatsapp_user_session,
    user_session: user_session,
    wa_session_data: wa_session_data,
    wa_user_session: wa_user_session,
    wa_health_worker: wa_health_worker,
    wa_user_files: wa_user_files,
    wa_requests: wa_requests
}