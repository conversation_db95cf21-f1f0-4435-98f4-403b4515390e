"use strict";
const request = require('request');
var axios = require('axios');
var qs = require('qs');

async function sendMail(body, cc, country_code, attachment, callback) {
    var data = qs.stringify({
        'id': ' ',
        'link': body,
        'path': attachment,
        'recepient': getReceipientEmailAddress(country_code),
        'from': '<EMAIL>',
        'bcc': '<EMAIL>',
        'subject': 'WhatsApp Sale & Service Request'
    });
    var config = {
        method: 'post',
        url: 'https://uapom.uapoldmutual.com/mailer/mail_service',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: data
    };

    axios(config)
        .then(function(response) {
            if (response.status === 200) {
                callback(false, response)
            } else {
                console.log("Error sending mail error code: " + response.status);
                callback(true, error)
            }
        })
        .catch(function(error) {
            console.log(error);
            callback(true, error)
        });

}

function getReceipientEmailAddress(country_code) {
    if (country_code === 'na') //
        return '<EMAIL>'
    else if (country_code === 'sz')
        return '<EMAIL>'
    else if (country_code === 'bw')
        return '<EMAIL>'
    else if (country_code === 'rw')
        return '<EMAIL>'//'<EMAIL>' //'<EMAIL>' //
    else if (country_code === 'mw')
        return '<EMAIL>'
    else
        return '<EMAIL>'

}

module.exports = {
    sendMail: sendMail,
}