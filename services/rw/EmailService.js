"use strict";
const nodemailer = require("nodemailer");

function sendEmail(opts, callback){
    // to be refactored to omo server mailer
    let transporter = nodemailer.createTransport({
        service:'gmail',
        auth: {
            user:'<EMAIL>',
            pass:'dkmbfafrxohiqhod'
        }
    });
    transporter.sendMail(opts, callback);



}

function getReceipientEmailAddress(country_code) {
    return '<EMAIL>'
    if (country_code === 'na')
        return '<EMAIL>'
    else if (country_code === 'sz')
        return '<EMAIL>'
    else if (country_code === 'bw')
        return '<EMAIL>'
    else if (country_code === 'mw')
        return '<EMAIL>'
    else
        return '<EMAIL>'

}
// test
// sendEmail({
//
//     to: '<EMAIL>',
//     subject: 'Sending Email test',
//     text: 'is a test!',
//     attachments: [{   // utf-8 string as an attachment
//         path:'https://i.picsum.photos/id/100/367/267.jpg?hmac=mNdSdA1Zh6w4qessdp5n207IFw3q_8FbbQ1gIr0jYBs'
//     }]
// }, function(error, info){
//     if (error) {
//         console.log(error);
//     } else {
//         console.log('Email sent : ' + info.response);
//     }
// })
module.exports = {
    sendMail: sendMail
};