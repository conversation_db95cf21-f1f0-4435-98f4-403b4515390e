const request = require('request'),
    config = require('config'),
    _this = this;

function sendResponse(responseData,sender_psid,country_code) {

    // console.log("==> Messenger service: responseData: " + responseData.type + " responseMsg: Sent. PSID: " + sender_psid);
    if (responseData.type === 'postback') {
        callSendAPI(sender_psid, responseData.message,country_code);
    } else if (responseData.type === 'message') {
        this.sendTextMessage(sender_psid, responseData.message,country_code);
    } else if (responseData.type === 'end') {
        this.sendTextMessage(sender_psid, responseData.message,country_code);
    }
};

function callSendAPI(sender_psid, response,country_code, cb = null) {
    // Construct the message body
    let request_body = {
        recipient: {
            id: sender_psid
        },
        message: response
    };

    // Send the HTTP request to the Messenger Platform
    request({
        "uri": "https://graph.facebook.com/v5.0/me/messages",
        "qs": {"access_token": getAccessToken(country_code)},
        "method": "POST",
        "json": request_body
    }, (err, res, body) => {
        if (!err) {
            if (cb) {
                cb();
            }
        } else {
            console.error("Unable to send message: - Error: Suspect postback formation e.g. type Error: " + err);
        }
    });
}

function callSendAPI2(messageData,country_code) {
    request({
        uri: 'https://graph.facebook.com/v5.0/me/messages',
        qs: {access_token: getAccessToken(country_code)},
        method: 'POST',
        json: messageData

    }, function (error, response, body) {
        if (!error && response.statusCode === 200) {
        } else {
            console.error("Unable to send message.");
            console.error("Error: Suspect message formation e.g. type");
        }
    });
}


function sendTextMessage(recipientId, messageText,country_code) {
    var messageData = {
        recipient: {
            id: recipientId
        },
        message: {
            text: messageText
        }
    };
    // call the send API
    callSendAPI2(messageData,country_code);
};

function sendTextMessageFunc (recipientId, messageText,country_code) {
    var messageData = {
        recipient: {
            id: recipientId
        },
        message: {
            text: messageText
        }
    };
    // call the send API
    callSendAPI2(messageData,country_code);
};

function getFBDetails(psid, country_code,callback){
    try {

        request.get({
            url: 'https://graph.facebook.com/' + psid + '?fields=first_name,last_name,profile_pic&access_token=' + getAccessToken(country_code),
            json: true
        }, function (error, response, body) {
            if (!error && response.statusCode === 200) {
              callback(false,body);
            }
            else
                callback(true,error);
        });
    } catch (e) {
        console.log('Error: ' + e);
        callback(true,error);
    }
}

function getAccessToken(country_code){
    if(country_code==='na')
        return 'EAAQh0fIdZAw8BAFmzs5cvp7ZAD0r3GFQHSufKD2iNflqdj05F5TGXkA8188OtP5pfIgAubOHm7akCOhLxDv189oOZBXPwqDpEdioxLtGqW1uVpruqrjZB4hHeczLDtsubpjc4Y6DFznLJGJR936wVfWcOTANUJZAm1OdZCDu1FpAZDZD'
    else if(country_code==='sz')
        return 'EAAIKLZCskcZBYBANYg8yEOfy9TfhQHR7VCFdp1MUNqscmGVwQlPTxVipmCmGxi7Ep2CKNfLPZAEmbakOFpHRaU8UufXcauPYZC8qHpDnnmnqTISmbVUvNO5STORd0ZCUnOutSCzFqDuVOszWFJSApJY2zQL6klBMUlq8WQzJEXgZDZD'
    else if(country_code==='bw')
        return 'EAAmsftZAHcKYBAIqzq22tJZCdKLmd8gZBimQgakvwZCnYFQ863bFaqeQFF6jST00ZBZCd4VKyGZBC0fWw8vIPLXgW59FZAqpuCCLFgWDjA9ChswV6KgzwzDOew7jl8Dalh1mrlg3k163yyiAWbsO6PGTkPlGJe53ZCq0cDYZAvgiIOfQZDZD'
    else if(country_code==='mw')
        return 'EAAIgurZAJ2gcBAAhIuJRU29PfBHZAaGB9UWJBeWGZAYyTnBgZCz4zCtPpT7k99eZCZCxZA3IP0OYTu0se9xquUirg3dzAZCCUuwUspI2yalpYAmfAbnWgzPTho3AbXO1YCVCAKRZBp0SF3ZAeZCDyiYnMXefblr3ZBYtclLYr1al8U1vwAZDZD'
    else // Test page
        return 'EAAhAPHAsUUYBADsrnXORwON01ZAZAhVZCHgCt7kNAAJ2kY73vfp0L1zynQCtUJxv3frP1skWPAPLjys7W7Jod0H2kX0RVFNrzajeVqoW79vjbmh2ZBPYdL8zVbcxb9PnL5yQhGlUeBf4j5s0JanSVk1DmDrIeySjMg77e4jWtwZDZD'
}


module.exports = {
    callSendAPI:callSendAPI,
    sendTextMessageFunc:sendTextMessageFunc,
    sendTextMessage:sendTextMessage,
    sendResponse:sendResponse,
    getUserFBDetails:getFBDetails
};