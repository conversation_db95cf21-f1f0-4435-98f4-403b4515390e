let messengerService = require('./MessengerService.js')
var dbService = require('./DbService');
let mailService = require('../MailService');
let validateInputService = require('../ValidateInputService');

let salutation_trigger = [
    'hi chatbot', 'hello chatbot', 'helo chatbot', 'halo chatbot', 'hallo chatbot', 'hey chatbot', 'hi', 'menu', 'help', 'hello', 'hallo', 'hey'];
let salutation_response = ['Hi', 'Hello', 'Hello', 'Hey', 'Hey', 'Hey', 'Hi', 'Hi', 'Hi', 'Hello', 'Hi', 'Hey'];

exports.webhookMessages = function (request, res, country_code) {
    let req = request;
    let body = req.body;

    if (body.object === 'page') {

        // Iterates over each entry - there may be multiple if batched
        body.entry.forEach(function (entry) {
            // Gets the message. entry.messaging is an array, but
            // will only ever contain one message, so we get index 0
            try {
                if (entry.hasOwnProperty('messaging')) {
                    let webhook_event = entry.messaging[0];

                    let sender_psid = webhook_event.sender.id;
                    let user_data = {psid: sender_psid, country_code: country_code}
                    let valid_request = true;
                    let invalid_msg = '';
                    // Check if the event is a message or postback and
                    // pass the event to the appropriate handler function
                    if (webhook_event.message) {
                        if (webhook_event.message.quick_reply) {
                            console.log('Sender: ' + sender_psid + ' | Postback: ' + webhook_event.message.quick_reply.payload + ' | Country code: ' + country_code)
                            let unique_identifier = webhook_event.message.quick_reply.payload;
                            // console.log('Incoming ---> : ' + JSON.stringify(webhook_event))
                            switch (unique_identifier) {
                                case 'GET_STARTED':
                                    user_data.service_request_selected = null;
                                    user_data.service_request = null;
                                    unique_identifier = 'GET_STARTED'
                                    break;
                                case 'SUBMIT_CLAIM':
                                    user_data.service_request = unique_identifier;
                                    user_data.service_request_selected = webhook_event.message.text;
                                    unique_identifier = 'INSURANCE_CLAIM_TYPE'
                                    break
                                case "WITHDRAWAL":
                                case "APPLY_FUNERAL_COVER":
                                case "APPLY_LOAN":
                                case "APPLY_CAR_HOUSEHOLD_COVER":
                                case "APPLY_LIFE_COVER":
                                case "APPLY_DISABILITY_COVER":
                                case 'FUNERAL_CLAIM':
                                case 'LIFE_COVER_CLAIM':
                                case 'CAR_HOUSEHOLD_CLAIM':
                                case 'DISABILITY_SEVERE_ILLNESS_CLAIM':
                                case 'CREDIT_LIFE_CLAIM':
                                case 'RETIREMENT_ANNUITY':
                                case 'OTHER_CLAIM':
                                case 'SAVE_INVEST':
                                case 'CHANGE_BENEFICIARY':
                                case 'SALES_ENQUIRY':
                                case 'POLICY_STATUS':
                                case 'TAX_CERTIFICATE':
                                case 'CONFIRM_POLICY_VALUE':
                                case 'UPDATE_PROFILE':
                                    user_data.service_request = unique_identifier;
                                    user_data.service_request_selected = webhook_event.message.text;
                                    unique_identifier = 'FULL_NAME'
                                    break;
                                case 'FULL_NAME':
                                    unique_identifier = 'ID_NUMBER'
                                    break;
                                case 'ID_NUMBER':
                                    unique_identifier = 'PHONE_NO'
                                    break;
                                case 'PHONE_NO':
                                    unique_identifier = 'EMAIL'
                                    break;
                                case 'EMAIL':
                                    unique_identifier = 'SUCCESS_END_MSG'
                                    break;
                                case 'SKIP_EMAIL':
                                    unique_identifier = 'SUCCESS_END_MSG'
                                    break;
                                default:
                                    user_data.service_request = unique_identifier;
                                    user_data.service_request_selected = webhook_event.message.text;
                                    break;


                            }
                            console.log('User data: ' + JSON.stringify(user_data))
                            dbService.updateUserData(user_data, function (err, update_response) {
                                if (err) {
                                    console.log('Error @updateUserData: ' + update_response)
                                } else {
                                    // console.log('No Error @updateUserData: ' + update_response)
                                    getMenu(unique_identifier, update_response, sender_psid, country_code)
                                }
                            })


                        } else if (webhook_event.message.text) {

                            let unique_identifier = webhook_event.message.text

                            let sal_index = salutation_trigger.indexOf(unique_identifier.toLocaleLowerCase());

                            // console.log('Index: ' + sal_index + ' | Text: ' + webhook_event.message.text)
                            if (sal_index > -1) {
                                // console.log(sender_psid + ' used trigger word')
                                unique_identifier = 'GET_STARTED';
                                dbService.getUserLevel(sender_psid, function (err, user_response) {
                                    if (err) {
                                        initialWelcome(sender_psid, salutation_response[sal_index], country_code)
                                    }
                                    else {
                                        console.log('Data: ' + JSON.stringify(user_response))

                                        let user_data = {
                                            psid: sender_psid,
                                            country_code: country_code,
                                            service_request_selected: null,
                                            service_request: null
                                        }

                                        messengerService.sendTextMessage(sender_psid,'Welcome back '+ user_response.fb_name, country_code)
                                        dbService.updateUserData(user_data, function (err, update_response) {
                                            if (err) {
                                            } else {
                                                setTimeout(() => {
                                                    getMenu(unique_identifier, update_response, sender_psid, country_code)
                                                }, 1500);
                                            }
                                        })
                                    }
                                })
                            }
                            else {
                                let user_input = webhook_event.message.text
                                // console.log(sender_psid + ' no trigger word')
                                dbService.getUserLevel(sender_psid, function (err, user_response) {
                                    if (err) {
                                        /*let index = salutation_trigger.indexOf(webhook_event.message.text.toLocaleLowerCase());
                                        initialWelcome(sender_psid,salutation_response[index])*/
                                        console.log(user_response + ' - ' + sender_psid + ' and has not used trigger keyword')
                                    } else {
                                        console.log('Sender: ' + sender_psid + ' | RESPONSE TO: ' + user_response.unique_identifier + ' | Text: ' + webhook_event.message.text)
                                        switch (user_response.unique_identifier) {
                                            case 'GET_STARTED':
                                                user_data.service_request = null;
                                                user_data.service_request_selected = null;
                                                user_response.unique_identifier = 'GET_STARTED'
                                                break;
                                            case 'SUBMIT_CLAIM':
                                                user_data.service_request = unique_identifier;
                                                user_data.service_request_selected = webhook_event.message.text;
                                                user_response.unique_identifier = 'INSURANCE_CLAIM_TYPE'
                                                break;
                                            case "WITHDRAWAL":
                                            case "APPLY_FUNERAL_COVER":
                                            case 'INSURANCE_CLAIM_TYPE':
                                                user_data.service_request = unique_identifier;
                                                user_data.service_request_selected = webhook_event.message.text;
                                                user_response.unique_identifier = 'FULL_NAME'
                                                break;
                                            case 'FULL_NAME':
                                                user_data.full_name = unique_identifier;
                                                user_response.unique_identifier = 'ID_NUMBER'
                                                break;
                                            case 'ID_NUMBER':
                                                if (!validateInputService.validateIdNumber(user_input, country_code)) {
                                                    valid_request = false
                                                    invalid_msg = 'Invalid ID number input'
                                                } else {
                                                    user_data.id_number = unique_identifier;
                                                    user_response.unique_identifier = 'PHONE_NO'
                                                }
                                                break;
                                            case 'PHONE_NO':
                                                if (!validateInputService.validatePhoneNo(user_input)) {
                                                    valid_request = false
                                                    invalid_msg = 'Invalid phone number input'
                                                } else {
                                                    user_data.phone_no = user_input;
                                                    user_response.unique_identifier = 'EMAIL'
                                                }
                                                break;
                                            case 'EMAIL':
                                                if (!validateInputService.validateEmail(user_input)) {
                                                    valid_request = false
                                                    invalid_msg = 'Invalid email input'
                                                } else {
                                                    user_data.email_address = user_input;
                                                    user_response.unique_identifier = 'SUCCESS_END_MSG'
                                                    break;
                                                }

                                        }
                                        // console.log('Valid? : ' + valid_request)
                                        /**
                                         * Check if the user input was valid then proceed with processing
                                         * */
                                        if (valid_request) {
                                            dbService.updateUserData(user_data, function (err, update_response) {
                                                if (err) {
                                                    console.log('Error Response: ' + update_response)
                                                } else {
                                                    // console.log('Response: ' + JSON.stringify(update_response))
                                                    dbService.getPostbackMenu(user_response.unique_identifier, country_code, function (err, menu_resp) {
                                                        if (err) {

                                                        }
                                                        else {

                                                            if (user_response.unique_identifier === 'SUCCESS_END_MSG') { // Process the request as the end of the data collection hence send email.

                                                                initiateSendEmail(menu_resp, update_response, country_code)
                                                            } else { // Process the request as a normal request
                                                                messengerService.sendResponse(JSON.parse(menu_resp.response), sender_psid, country_code)
                                                                dbService.updateUserSession({
                                                                    psid: sender_psid,
                                                                    unique_identifier: menu_resp.unique_identifier
                                                                }, function (err, msg) {

                                                                });
                                                            }


                                                        }
                                                    })
                                                }
                                            })
                                        }
                                        else { // User entered an Invalid input
                                            dbService.getPostbackMenu(user_response.unique_identifier, country_code, function (err, menu_resp) {
                                                if (err) {

                                                }
                                                else { // Process the request as a normal request
                                                    /* let _menu_resp = JSON.stringify(JSON.parse(menu_resp.response))
                                                     let message = _menu_resp.response.message.text
                                                     _menu_resp.response.message.text = invalid_msg + '\n' + message*/
                                                    let parsed = JSON.parse(menu_resp.response)
                                                    parsed.message.text = invalid_msg + '\n' + parsed.message.text
                                                    console.log('Menu data: ' + JSON.stringify(parsed))

                                                    messengerService.sendResponse(parsed, sender_psid, country_code)
                                                    dbService.updateUserSession({
                                                        psid: sender_psid,
                                                        unique_identifier: menu_resp.unique_identifier
                                                    }, function (err, msg) {

                                                    });
                                                }
                                            })
                                        }
                                    }
                                })


                            }

                        }
                        else if (webhook_event.postback) {

                        }
                    }
                }
            } catch (e) {
                console.error('Webhook parse: ' + e)
            }

        });

        // Returns a '200 OK' response to all requests
        res.status(200).send('EVENT_RECEIVED');
    } else {
        // Returns a '404 Not Found' if event is not from a page subscription
        res.sendStatus(404);
    }


};

function getMenu(unique_identifier, update_response, sender_psid, country_code) {
    dbService.getPostbackMenu(unique_identifier, country_code, function (err, menu_resp) {
            if (err) {

            }
            else {
                // console.log('---> menu_resp: ' + JSON.parse(menu_resp))
                if (unique_identifier === 'SUCCESS_END_MSG') {
                    initiateSendEmail(menu_resp, update_response, country_code)
                } else {

                    messengerService.sendResponse(JSON.parse(menu_resp.response), sender_psid, country_code)
                    dbService.updateUserSession({
                        psid: sender_psid,
                        unique_identifier: menu_resp.unique_identifier
                    }, function (err, msg) {
                        if (err) {

                        }
                    })
                }

            }
        }
    )
}

function initialWelcome(sender_psid, salutation, country_code) {
    let salute = salutation + ' %s1\nThank you for connecting with us on Facebook.';
    messengerService.getUserFBDetails(sender_psid, country_code, function (err, response) {
        if (err) {
            console.log(err);
            console.error('Error getting FB details: ' + err)

        } else {
            dbService.updateUserSession({
                psid: sender_psid,
                fb_name: response.first_name + " " + response.last_name,
                fb_profile_pic_url: response.profile_pic,
                country_code: country_code,
                unique_identifier: 'GET_STARTED'
            }, function (err, msg) {
                if (err) {
                }
                else {
                    messengerService.sendTextMessage(sender_psid, parameterizedString(salute, response.first_name), country_code)
                    setTimeout(() => {
                        getMenu('GET_STARTED', null, sender_psid, country_code);
                    }, 2000);
                }
            })

        }


    });

}

function initiateSendEmail(menu_resp, update_response, country_code) {
    let cc_email = update_response.email_address;
    if (update_response.email_address == null) {
        update_response.email_address = "Not provided"
        cc_email = ""

    }
    let email_body = '\nName: ' + update_response.full_name + '\n' +
        'Phone Number: ' + update_response.phone_no + '\n' +
        'ID Number: ' + update_response.id_number + '\n' +
        'Email: ' + update_response.email_address + '\n' +
        'Service Requested: ' + update_response.service_request_selected + '\n' +
        'Service Tags: ' + update_response.service_request + '\n' +
        'Source Channel: Facebook Messenger' + '\n\n'
    console.log(update_response.psid + ' | Sending Email')
    mailService.sendMail(email_body, cc_email, country_code, function (email_err, email_response) {
        if (email_err) {
            console.log(update_response.psid + ' | Error Sending Email: ' + email_response)
            messengerService.sendResponse(JSON.parse(menu_resp.response), update_response.psid, country_code)
            dbService.updateUserData({
                psid: update_response.psid,
                status: 'ERROR_SENDING_MAIL'
            }, function (err, resp) {
                if (err)
                    console.error(update_response.psid + ' |  Failed | Error updating email status: ' + resp)
                else
                    console.log(update_response.psid + ' |  Failed | Email sending failed and status updated!')
            })
            dbService.updateUserSession({
                psid: update_response.psid,
                unique_identifier: menu_resp.unique_identifier
            }, function (err, msg) {

            });
            // todo Queue this failure to be retried again.
        } else {
            messengerService.sendResponse(JSON.parse(menu_resp.response), update_response.psid, country_code)
            dbService.updateUserData({
                psid: update_response.psid,
                status: 'SENT'
            }, function (err, resp) {
                if (err)
                    console.error(update_response.psid + ' |  Sent | Error updating email status: ' + resp)
                else
                    console.log(update_response.psid + ' | Sent | Email status updated!')
            })
            dbService.updateUserSession({
                psid: update_response.psid,
                unique_identifier: menu_resp.unique_identifier
            }, function (err, msg) {

            });
        }

    })
}

function getOtherServices(title, go_back, sender_psid, country_code) {
    dbService.getPostbackMenu('OTHER_SERVICES', country_code, function (err, menu_resp) {
        if (err) {

        }
        else {
            let reply = parameterizedString(menu_resp.response, title, go_back)
            // console.log('---> getOtherServices: ' + reply)
            messengerService.sendResponse(JSON.parse(reply), sender_psid, country_code)
        }
    })
}

/***
 * @example parameterizedString("my name is %s1 and surname is %s2", "John", "Doe");
 * @return "my name is John and surname is Doe"
 *
 * @firstArgument {String} like "my name is %s1 and surname is %s2"
 * @otherArguments {String | Number}
 * @returns {String}
 */
const parameterizedString = (...args) => {
    const str = args[0];
    const params = args.filter((arg, index) => index !== 0);
    if (!str) return "";
    return str.replace(/%s[0-9]+/g, matchedStr => {
        const variableIndex = matchedStr.replace("%s", "") - 1;
        return params[variableIndex];
    });
}