"use strict";
const nodemailer = require("nodemailer");

// async..await is not allowed in global scope, must use a wrapper
async function main() {
    // Generate test SMTP service account from ethereal.email
    // Only needed if you don't have a real mail account for testing
    let testAccount = await nodemailer.createTestAccount();

    // create reusable transporter object using the default SMTP transport
    let transporter = nodemailer.createTransport({
        host: "*************",
        port: 25,
        secure: false, // true for 465, false for other ports
       /* auth: {
            user: testAccount.user, // generated ethereal user
            pass: testAccount.pass, // generated ethereal password
        },*/
    });

    /*$config['protocol'] = 'smtp';
$config['smtp_host'] = '*************';
$config['smtp_port'] = '25';
$ci->email->from('<EMAIL>', 'UAP Old Mutual');*/

    // send mail with defined transport object
    let info = await transporter.sendMail({
        from: '"UAP Old Mutual" <<EMAIL>>', // sender address
        to: "<EMAIL>, <EMAIL>", // list of receivers
        subject: "Hello ✔", // Subject line
        text: "Hello world?", // plain text body
        html: "<b>Hello world?</b>", // html body
    });

    console.log("Message sent: %s", info.messageId);
    // Message sent: <<EMAIL>>

    // Preview only available when sending through an Ethereal account
    console.log("Preview URL: %s", nodemailer.getTestMessageUrl(info));
    // Preview URL: https://ethereal.email/message/WaQKMgKddxQDoou...
}

// main().catch(console.error);
