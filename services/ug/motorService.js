var {motor_ug_customer, motor_ug_vehicle} = require("./DbService")

// create a new record with phone_no & ticket_no  
async function createCustomer(phone_no, ticket_no, details, callback) {
    console.log("* +++ * CREATING client DB : ", details)

    motor_ug_customer.upsert({
        phoneNumber: phone_no,
        ticket_no: ticket_no,
        fullName: details.fullName,
    }).then(result => {
        console.log(result);
        callback(false, result);
    }).catch(error => {
        console.error("Error saving customer details:", error);
        callback(false, error);
    });
}

// update any column of record
async function updateCustomer(ticket_no, details, callback) {
    console.log("* +++ * UPDATING client DB : ", details)
    motor_ug_customer.update(details, {
        where: { 
            ticket_no: ticket_no
        }
    }).then(result => {
        console.log(result);
        callback(false, result);
    }).catch(error => {
        console.error("Error updating customer details:", error);
        callback(false, error);
    });
}

// Get customer details by session ticket
async function getCustomerBySession(ticket_no, callback) {
    motor_ug_customer.findOne({
        where: { 
            ticket_no: ticket_no
        }
    }).then(result => {
        if (result) {
            callback(false, result);
        } else {
            callback(true, "Customer not found");
        }
    });
}

// create vehicle record
const createVehicle = async (ticket_no, details, callback) => {
    console.log("* +++ * CREATING vehicle DB : ", details)
    motor_ug_vehicle.upsert({
        ticket_no: ticket_no,
        make: details.make,
    }).then(result => {
        console.log(result);
        callback(false, result);
    }).catch(error => {
        console.error("Error saving vehicle details:", error);
        callback(false, error);
    });
}

// update vehicle record
const updateVehicle = async (ticket_no, details, callback) => {
    console.log("* +++ * UPDATING vehicle DB : ", details)
    motor_ug_vehicle.update(details, {
        where: { 
            ticket_no: ticket_no
        }
    }).then(result => {
        console.log(result);
        callback(false, result);
    }).catch(error => {
        console.error("Error updating vehicle details:", error);
        callback(false, error);
    });
}

// get vehicle by session ticket
const getVehicleBySession = async (ticket_no, callback) => {
    motor_ug_vehicle.findOne({
        where: { 
            ticket_no: ticket_no
        }
    }).then(result => {
        if (result) {
            callback(false, result);
        } else {
            callback(true, "Vehicle not found");
        }
    });
}

// calculate motor premium
const calculateMotorUgPremium = async (ticket_no) => {
  console.log("* +++ * Calculating motor premium : ")
  const vehicle = await motor_ug_vehicle.findOne({
        where: { 
            ticket_no: ticket_no
        }})
  const args = vehicle.dataValues;
  console.log("====> Vehicles details:",args)
  const result = {};

  const sumInsured = parseInt(args.valueOfCar) || 0;

  const premiumFactor = 0.04;

  let totalPremium = 0;

  let basePremium = 0;

  let totalPremiumITL = 0;

  const basePremiumITL = 0.005;

  let totalPremiumVat = 0;

  const baseVatRate = 0.18;

  const stickerFee = 6000;

  const carHireFactor = args && args.carHire ? args.carHire : 0;

  const alternativeAccomodationFactor = args && args.alternativeAccomodation ? args.alternativeAccomodation : 0;

  const firstApplication = args && args.firstApplication ? args.firstApplication : false;

  let stampDutyRate = 0;

  if (!(firstApplication)) {
    stampDutyRate = 35000;
  }

  let alternativeAccomodationPrice = 0;

  let carHirePrice = 0;

  let ugBenefits = [];

  const alternativeAccomodationRate = 0.1;

  const alternativeAccomodationAmount = 300000;

  const alternativeAccomodationVAT = 0.18;

  const alternativeTrainingLevy = 0.005;

  const carHireRate = 0.1;

  const carHireAmount = 100000;

  const carHireVAT = 0.18;

  const carHireLevy = 0.005;

  const alarmDiscountRate = args && args.carAlarmDiscount ? -0.05 : 0;

  const trackerDiscountRate = args && args.trackerDiscount ? -0.15 : 0;

  const excessAmount = args && args.excessValue ? args.excessValue : 0;

  let excessDiscount = 0;

  const pvtRate = args && args.pvt ? 0.1 : 0;

  let pvtFee = 0;

  let alarmDiscountFee = 0;

  let trackerDiscountFee = 0;

  const regionBounds = args && args.withinEA ? args.withinEA : 1;

  const withEAFeeRate = 0.2;

  const outsideEAFeeRate = 0.3;

  let withEAFee = 0;

  let outsideEAFee = 0;

  if (parseInt(args.valueOfCar) < 5000000) {
    console.log("Expected integer got " + typeof args.valueOfCar);

    result.premium = 0;
    result.alternativeAccomodationPrice = alternativeAccomodationPrice;
    result.carHirePrice = carHirePrice;
    result.basePremium = 0;
    result.benefits = [];

    result.status = 400;
    result.message = "Failed to fetch car premium";
    return result;
  }

  try {
    // ugBenefits = await quotationsDB.models.motorUgandaBenefits.findAll({
    //   attributes: ["name", "value"],
    //   where: {
    //     minAmount: { [Op.lte]: parseInt(args.valueOfCar) },
    //     maxAmount: { [Op.gte]: parseInt(args.valueOfCar) },
    //   },
    //   raw: true,
    // });

    basePremium = sumInsured * premiumFactor;

    pvtFee = basePremium * pvtRate;

    if (excessAmount >= 1000000 && excessAmount <= 3000000) {
      excessDiscount = basePremium * -0.1;
    } else if (excessAmount >= 3000001 && excessAmount <= 4000000) {
      excessDiscount = basePremium * -0.15;
    } else if (excessAmount >= 4000001 && excessAmount <= 5000000) {
      excessDiscount = basePremium * -0.25;
    }

    if (regionBounds === 2) {
      withEAFee = basePremium * withEAFeeRate;
    } else if (regionBounds === 3) {
      outsideEAFee = basePremium * outsideEAFeeRate;
    }

    if (alternativeAccomodationFactor >= 1) {
      alternativeAccomodationPrice =
        alternativeAccomodationAmount *
        alternativeAccomodationFactor *
        alternativeAccomodationRate;
      const trainingFee =
        alternativeAccomodationPrice * alternativeTrainingLevy;
      const vatImposed =
        (alternativeAccomodationPrice + trainingFee) *
        alternativeAccomodationVAT;
      alternativeAccomodationPrice =
        alternativeAccomodationPrice + trainingFee + vatImposed;
    }

    if (carHireFactor >= 1) {
      carHirePrice = carHireAmount * carHireFactor * carHireRate;
      const trainingFee = carHirePrice * carHireLevy;
      const vatImposed = (carHirePrice + trainingFee) * carHireVAT;
      carHirePrice = carHirePrice + trainingFee + vatImposed;
    }

    alarmDiscountFee = basePremium * alarmDiscountRate;

    trackerDiscountFee = basePremium * trackerDiscountRate;

    basePremium =
      basePremium +
      alarmDiscountFee +
      trackerDiscountFee +
      withEAFee +
      outsideEAFee +
      pvtFee +
      alternativeAccomodationPrice +
      carHirePrice +
      excessDiscount;

    totalPremiumITL = basePremium * basePremiumITL;

    totalPremiumVat =
      (basePremium + totalPremiumITL + stickerFee) * baseVatRate;

    totalPremium =
      stampDutyRate +
      basePremium +
      totalPremiumITL +
      totalPremiumVat +
      stickerFee;

    result.premium = Math.round(totalPremium);
    result.alternativeAccomodationPrice = alternativeAccomodationPrice;
    result.carHirePrice = carHirePrice;
    result.withinEAFee = withEAFee;
    result.outsideEAFee = outsideEAFee;
    result.basePremium = Math.round(basePremium);
    result.trainingLevy = totalPremiumITL;
    result.vat = totalPremiumVat;
    result.alarmDiscount = alarmDiscountFee;
    result.trackerDiscount = trackerDiscountFee;
    result.pvtFee = pvtFee;
    result.stickerFee = stickerFee;
    result.stampDuty = stampDutyRate;
    result.excessDiscount = excessDiscount;
    result.sumInsured = parseInt(args.valueOfCar);
    // result.benefits = ugBenefits;
    result.status = 200;
    result.message = "Successfully generated motor premium";
    console.log("Premium calculation result:", result);
    return result;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// calculateMotorUgPremium("2025062422513201", (err, result) => {
//   if (err) {
//     console.error("Error calculating premium:", result);
//   } else {
//     console.log("Premium calculation result:", result);
//   }
// });
module.exports = {
    createCustomer: createCustomer,
    updateCustomer: updateCustomer,
    getCustomerBySession: getCustomerBySession,
    createVehicle: createVehicle,
    updateVehicle: updateVehicle,
    getVehicleBySession: getVehicleBySession
}