var {motor_ug_customer, motor_ug_vehicle} = require("./DbService")

// create a new record with phone_no & ticket_no  
async function createCustomer(phone_no, ticket_no, details, callback) {
    console.log("* +++ * CREATING client DB : ", details)

    motor_ug_customer.upsert({
        phoneNumber: phone_no,
        ticket_no: ticket_no,
        fullName: details.fullName,
    }).then(result => {
        console.log(result);
        callback(false, result);
    }).catch(error => {
        console.error("Error saving customer details:", error);
        callback(false, error);
    });
}

// update any column of record
async function updateCustomer(ticket_no, details, callback) {
    console.log("* +++ * UPDATING client DB : ", details)
    motor_ug_customer.update(details, {
        where: { 
            ticket_no: ticket_no
        }
    }).then(result => {
        console.log(result);
        callback(false, result);
    }).catch(error => {
        console.error("Error updating customer details:", error);
        callback(false, error);
    });
}

// Get customer details by phone and ticket
async function getCustomerBySession(ticket_no, callback) {
    motor_ug_customer.findOne({
        where: { 
            ticket_no: ticket_no
        }
    }).then(result => {
        if (result) {
            callback(false, result);
        } else {
            callback(true, "Customer not found");
        }
    });
}

const createVehicle = async (ticket_no, details, callback) => {
    console.log("* +++ * CREATING vehicle DB : ", details)
    motor_ug_vehicle.upsert({
        ticket_no: ticket_no,
        make: details.make,
    }).then(result => {
        console.log(result);
        callback(false, result);
    }).catch(error => {
        console.error("Error saving vehicle details:", error);
        callback(false, error);
    });
}

const updateVehicle = async (ticket_no, details, callback) => {
    console.log("* +++ * UPDATING vehicle DB : ", details)
    motor_ug_vehicle.update(details, {
        where: { 
            ticket_no: ticket_no
        }
    }).then(result => {
        console.log(result);
        callback(false, result);
    }).catch(error => {
        console.error("Error updating vehicle details:", error);
        callback(false, error);
    });
}

const getVehicleBySession = async (ticket_no, callback) => {
    motor_ug_vehicle.findOne({
        where: { 
            ticket_no: ticket_no
        }
    }).then(result => {
        if (result) {
            callback(false, result);
        } else {
            callback(true, "Vehicle not found");
        }
    });
}

module.exports = {
    createCustomer: createCustomer,
    updateCustomer: updateCustomer,
    getCustomerBySession: getCustomerBySession,
    createVehicle: createVehicle,
    updateVehicle: updateVehicle,
    getVehicleBySession: getVehicleBySession
}