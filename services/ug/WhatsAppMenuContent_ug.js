exports.

getMenuContent = function() {
    return {
        "0": {
            "start": {
                "title": "Kindly Select your Country of Choice (respond with the corresponding number of your choice)\n1 Kenya\n2 Uganda\n3 Tanzania\n4 Rwanda\n5 South Sudan",
                "next": {
                    "1": "kenya",
                    "2": "uganda",
                    "3": "tanzania",
                    "4": "rwanda",
                    "5": "south_sudan"
                },
                "input_type": "SELECTION"
            }
        },
        "1": {
            "uganda": {
                "title": "Choose an option\n(e.g. send 1 to Make a claim)\n\n1. Make a claim\n2. Buy insurance\n\nOr View your portfolio and more on the Old Mutual app. Terms of Use https://www.uapoldmutual.co.ug/privacy-policy/\n\nType *Q* to quit the chat at any point in time.",
                "next": {
                    "1": "register_claim",
                    "2": "buy_insurance",
                },
                "input_type": "SELECTION"
            }
        },
        "2": {
            "register_claim": {
                "title": "Which *type of insurance claim* would you like to make?",
                "next": {
                    "1": "claim_car",
                    "2": "claim_health",
                    "3": "claim_life",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status": {
                "title": "Policy status for? (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Motor Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "policy_status_health",
                    "2": "policy_status_car",
                    "3": "policy_status_life",
                    "4": "policy_status_other",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "buy_insurance": {
                "title": "Which *type of insurance* would you like?",
                "next": {
                    "1": "motor_insurance",
                    "2": "health_insurance",
                    "3": "life_insurance",
                    "4": "travel_insurance",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment": {
                "title": "Make payment to? (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Motor Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "make_insurance_payment_health",
                    "2": "make_insurance_payment_car",
                    "3": "make_insurance_payment_life",
                    "4": "make_insurance_payment_other",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "invest_with_us": {
                "title": "We have a fully dedicated investment platform i-INVEST\nClick here https://bit.ly/3bDgln4 to chat with Arifa our online virtual assistant. Type 'hi Arifa' in your Facebook Messenger chat to start investing.\nor\nDial *480#\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "new_customer_verification": {
                "title": "Which Insurance class have you bought: (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Motor Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "verify_enter_name",
                    "2": "verify_enter_name",
                    "3": "verify_enter_name",
                    "4": "verify_enter_name",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "update_personal_details": {
                "title": "To update your personal details kindly click on this link https://bit.ly/2LazEcA#",
                "next": {
                    "1": "end"
                },
                "input_type": "END"
            },
            "bank_with_us": {
                "title": "Which Faulu Micro-finance Bank product are you interested In?  (respond with the corresponding number of your choice)\n1. Loan\n2. Current Account\n3. Savings Account\n4. Fixed Deposit Account\n0 Main Menu",
                "next": {
                    "1": "bank_with_us_option_success_end_message",
                    "2": "bank_with_us_option_success_end_message",
                    "3": "bank_with_us_option_success_end_message",
                    "4": "bank_with_us_option_success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "health_worker": {
                "title": "How can we help you? (respond with the corresponding number of your choice)\n1. New User Registration\n2. Register a Claim \n0 Main Menu",
                "next": {
                    "1": "health_worker_profession",
                    "2": "health_worker_register_claim_type",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "covid": {
                "title": "Which option would you prefer an update on? (respond with the corresponding number of your choice)\n1. Keeping Healthy\n2. Feeling Unwell\n3. Working From Home\n4. Studying From Home\n5. Unwinding From Home\n6. Ministry of Health Updates\n0 Main Menu",
                "next": {
                    "1": "covid_update_message",
                    "2": "covid_update_message",
                    "3": "covid_update_message",
                    "4": "covid_update_message",
                    "5": "covid_update_message",
                    "6": "covid_update_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            }
        },
        "3": {
            "motor_insurance": {
                "title": "Great choice. Our Motor insurance covers *accidents* and *medical expenses* for the driver and the passengers of the vehicle.\n\nWould you like us to *call you back*? " ,
                "next": {
                    "1": "enter_name_surname",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "travel_insurance": {
                "title": "What would you like to do?\n\nChoose an option (e.g. send 1 to Buy Travel insurance)\n\n1. Buy Travel insurance\n2. Learn more about Travel insurance\n\nType and send Q to quit the flow at any point.\nType and send N/A to answer questions that don't apply.\n\nBy continuing to the next step, you agree to our terms and conditions found in this link: https://www.oldmutual.rw/individual/Travel/Travel-insurance/",
                "next": {
                    "1": "travel_enter_name_surname",
                    "2": "travel_insurance_info",
                    "0": "back_to_main"
                    },
                    "input_type": "SELECTION"
             },
            "health_insurance": {
                "title": "Awesome. We offer *budget-friendly health insurance* solutions for you and your family.\n\nWould you like us to *call you back?*",
                "next": {
                    "1": "enter_name_surname",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "life_insurance": {
                "title": "Awesome. Our life insurance protects you in the event of your *passing* or permanent *disability*.\n\nWould you like us to *call you back*? " ,
                "next": {
                    "1": "enter_name_surname",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "claim_health": {
                "title": "Okay, we’ll assist you. *How* would you like to make your claim?",
                "next": {
                    "1": "provide_details_health",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "claim_car": {
                "title": "Okay, we’ll assist you. *How* would you like to make your claim?",
                "next": {
                    "1": "provide_details_car",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "claim_life": {
                "title": "Okay, we’ll assist you. *How* would you like to make your claim?",
                "next": {
                    "1": "provide_details_life",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "claim_other": {
                "title": "Select how to start the Insurance claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_other",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_health": {
                "title": "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_health",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_car": {
                "title": "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_car",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_life": {
                "title": "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_life",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_other": {
                "title": "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_other",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "buy_insurance_health_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "buy_insurance_car_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "buy_insurance_life_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "buy_insurance_other_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "make_insurance_payment_health": {
                "title": "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_health",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment_car": {
                "title": "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_car",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment_life": {
                "title": "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_life",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment_other": {
                "title": "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_other",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "verify_enter_name": {
                "title": "Kindly enter your full name?\n0 Main Menu",
                "next": {
                    "1": "verify_id_passport",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "bank_with_us_option_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "covid_update_message": {
                "title": "Dear customer, please check health tips here <>  \n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "health_worker_profession": {
                "title": "Kindly select your profession (respond with the corresponding number of your choice)\n1. Doctor\n2. Nurse\n3. Clinical Officer\n4. Laboratory Technician\n0 Main Menu",
                "next": {
                    "1": "health_worker_name",
                    "2": "health_worker_name",
                    "3": "health_worker_name",
                    "4": "health_worker_name",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "health_worker_register_claim_type": {
                "title": "Kindly select the type of claim (respond with the corresponding number of your choice)\n1. Hospital Benefit \n2. Last Expense Benefit\n0 Main Menu",
                "next": {
                    "1": "health_worker_register_claim_hosp",
                    "2": "health_worker_register_claim_last",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            }
        },
        "4": {
            "enter_name_surname": {
                "title": "What is your full name, in order of first, middle and last name?\n\n(E.g. Achen Mbabazi)\n\nType *B* to go back to the main screen",
                "next": {
                    "1": "enter_middle_name",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "travel_enter_name_surname":{
                "title": "What is your *name* and *surname*?\n\n(E.g. Achen Mbabazi)\n\nType *B* to go back to the main screen",
                "next": {
                    "1": "travel_enter_middle_name",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "travel_insurance_info":{
                "title": "What is your *name* and *surname*?\n\n(E.g. Achen Mbabazi)\n\nType *B* to go back to the main screen",
                "next": {
                    "1": "travel_enter_middle_name",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "request_callback": {
                "title": "What is your *name* and *surname*?\n\n(E.g. Achen Mbabazi)\n\nType *B* to go back to the main screen",
                "next": {
                    "1": "enter_mobile_number",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "travel_insurance_info": {
                "title": "Our Travel insurance helps cover *accidents*, *emergencies*, and *medical expenses* while traveling abroad or locally. It’s great for peace of mind.\n\nWould you like to go back to the previous menu?",
                "next": {
                    "1": "travel_insurance",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "travel_insurance_first_name": {
                "title": "What is your first name?",
                "next": {
                    "1": "travel_insurance_middle_name",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "travel_insurance_middle_name": {
                "title": "What is your middle name?\n\n*Note:* If not applicable, respond with N/A",
                "next": {
                    "1": "travel_insurance_last_name",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
           
            "travel_insurance_phone_confirm": {
                "title": "To make the process seamless, we’ll use this WhatsApp number (*+256 7XX XXX XXX*) as our primary way to reach you. This allows us to share your quotes, policy updates, and important information directly. ✅",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "provide_details_policy_status_health": {
                "title": "What\'s Your Health Insurance Policy Number? eg. UK032344-01 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_policy_status_car": {
                "title": "What\'s Your Car Registration Number? e.g RAA654U\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_policy_status_other": {
                "title": "What\'s Your Insurance Policy Number?\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_policy_status_life": {
                "title": "What\'s your Life Insurance policy number? e.g. OMK001429206 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_payment_health": {
                "title": "What\'s Your Health Insurance Policy Number? eg. UK032344-01 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_payment_car": {
                "title": "What\'s Your Car Registration Number? e.g RAA654U\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_payment_other": {
                "title": "What\'s Your Insurance Policy Number?\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_payment_life": {
                "title": "What\'s Your Life Insurance Policy Number? e.g. OMK001429206 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_car": {
                "title": "Now, please enter your *policy number* or *ID number.*",
                "next": {
                    "1": "provide_details_car_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },

            "provide_details_other": {
                "title": "What\'s Your Insurance policy number?\n \n0 Main Menu",
                "next": {
                    "1": "provide_details_other_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_life": {
                "title": "Now, please enter your *policy number* or *ID number.*",
                "next": {
                    "1": "provide_details_life_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health": {
                "title": "Now, please enter your *policy number* or *ID number.*",
                "next": {
                    "1": "provide_details_health_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "verify_id_passport": {
                "title": "What\'s your National ID e.g. 1234567 or Passport Number e.g. ********\n\n0 Main Menu",
                "next": {
                    "1": "verify_id_photo",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "health_worker_name": {
                "title": "Kindly enter your  first name and surname\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_national_id",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "health_worker_register_claim_hosp": {
                "title": "Select how to start your Hospital Benefit claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_id",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "health_worker_register_claim_last": {
                "title": "Select how to start the Last Expense Benefit claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_last_claim_id",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
        },
        "5": {
             "travel_insurance_last_name": {
                "title": "What is your last name?",
                "next": {
                    "1": "travel_insurance_phone_confirm",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "enter_mobile_number": {
                "title": "%s1, please enter your *mobile number*.\n\n(E.g. +256123456755)",
                "next": {
                    "1": "enter_email_address",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_photos": {
                "title": "Got it. Could you please *upload* photos of the following documents?\n\n- Health claim application form \n- Attending physician statement/report\n- Medical receipts",
                "next": {
                    "1": "more_photos",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_car_photos": {
                "title": "Got it. Could you please *upload* photos of the following documents?\n- Completed Claim Form \n- Copy of the log book \n- Police Abstract Report \n- Motor Vehicle Inspection report (for third party claims)\n- Copy of the Driver’s Driving licence\n- Notice of intention to prosecute(if any)\n- Applicable excess",
                "next": {
                    "1": "more_photos",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_life_photos": {
                "title": "Got it. Upload photos of your insurance *claim documents*\n\n(E.g. death certificate or police abstract)",
                "next": {
                    "1": "more_photos",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },

            "provide_details_other_photos": {
                "title": "Share photo of your Insurance supporting Documents? e.g. Medical receipts, Death certificate, Police abstract, Damaged property\n\nType 1 to complete your submission\n0 Main Menu",
                "next": {
                    "1": "provide_details_photos_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },

            "provide_details": {
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "verify_id_photo": {
                "title": "Kindly Take & Upload a Photo of Your National ID or Passport \n\n0 Main Menu",
                "next": {
                    "1": "selfie_passport_photo",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "health_worker_national_id": {
                "title": "Kindly enter your National ID Number e.g. 12345678\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_facility",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_id": {
                "title": "Kindly enter your National ID Number? e.g. 12345678\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_staff_card",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_hosp_claim_id": {
                "title": "Kindly share photo of your National ID Card\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_staff_card",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_last_claim_id": {
                "title": "Kindly share photo of your National ID Card\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_last_claim_id_hw",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "request_callback": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
        },
        "6": {
            "enter_email_address": {
                "title": "Got it. What’s your *email address?*",
                "next": {
                    "1": "enter_car_make",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "travel_last_name": {
                "title": "Do you agree to use this WhatsApp number? (Yes/No)",
                "next": {
                "yes": "travel_insurance_type",
                "no": "end"
                },
                "input_type": "SELECTION"
            },
            
            // "select_identification_document_health": {
            //     "title": "✅ *Thank you*\n An Old Mutual consultant will be in touch within 48 hours.\nIs there anything else we can assist you with *%s1*?",
            //     "next": {
            //         "1": "photo_national_id_health",
            //         "2": "photo_passport_health"
            //     },
            //     "input_type": "SELECTION"
            // },
            "provide_details_car_additional_photos": {
                "title": "Share a photo of police abstract or any other supportive photos\n\n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_other_additional_photos": {
                "title": "Share a photo of additional supportive documents\n 1. Complete Submission \n0. Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "more_photos": {
                "title": "Would you like to upload *additional documents?*",
                "next": {
                    "1": "upload_more",
                    "2": "done_uploading"
                },
                "input_type": "END"
            },
            "provide_details_life_additional_photos": {
                "title": "Share a photo of additional supportive documents\n 1. Complete Submission \n0. Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "selfie_passport_photo": {
                "title": "Kindly take & upload a Selfie or Passport Photo\n0. Main Menu",
                "next": {
                    "1": "signup_phone_no",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "health_worker_facility": {
                "title": "Kindly enter your primary health facility of operation?\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_reg_no",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_supporting_docs": {
                "title": "Kindly share photos of your insurance claim supporting documents e.g. Medical Receipts, Hospital Admission Form or Death Certificate\n\nMaximum 10 attachments.\n\nType 1 when done.\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_hosp_claim_staff_card": {
                "title": "Kindly share your a photo of staff card of employing health facility\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_discharge",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_last_claim_id_hw": {
                "title": "Kindly share a photo of the Health Worker\'s National ID Card\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_last_facility",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
        },
        "7": {
            "enter_car_make": {
                "title": "Kindly select your Car Make e.g. Honda CRV",
                "next": {
                    "1": "enter_car_model",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
             "travel_insurance_type": {
                    "title": "Select Travel insurance type:\n1. World Wide\n2. Schengen\n3. Student Cover",
                    "next": {
                    "1": "travel_duration",
                    "2": "travel_duration",
                    "3": "travel_duration"
                    },
                    "input_type": "SELECTION"
                },
             "continue_done": {
                "title": "✅ *Thank you*\n\nAn Old Mutual consultant will be in touch within 48 hours.\n\nIs there anything else we can assist you with, *%s1*?",
                "next": {
                    "1": "continue",
                    "2": "done"
                },
                "input_type": "SELECTION"
            },
            "done_uploading": {
                "title": "✅ *Thank you*\n\nAn Old Mutual consultant will be in touch within 48 hours.\n\nIs there anything else we can assist you with?",
                "next": {
                    "1": "continue",
                    "2": "complete"
                },
                "input_type": "SELECTION"
            },
            "success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "signup_phone_no": {
                "title": "Kindly enter the phone number you used to sign-up for the new product? e.g. 0721******\n0. Main Menu",
                "next": {
                    "1": "otp_verification",
                    "2": "end"
                },
                "input_type": "TEXT"
            },
            "health_worker_reg_no": {
                "title": "Kindly enter your professional registration / licence number\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_beneficiary_name",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_end": {
                "title": "We have successfully received your claim notification.\n One of our call center representatives will contact you within 24 Hours.\n\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "provide_details_health_worker_hosp_claim_discharge": {
                "title": "Kindly share your hospitalization discharge summary indicating dates of admission and discharge.\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_covid_report",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_last_facility": {
                "title": "Kindly share a photo staff ID card of the facility where the Healthcare Worker worked.\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_last_death_letter",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            }
        },
        "8": {
            "travel_duration": {
                "title": "Choose trip duration:\n1. 1-8 Days\n2. 9-14 Days\n3. 15-21 Days",
                "next": {
                "1": "travel_covering",
                "2": "travel_covering",
                "3": "travel_covering"
                },
                "input_type": "SELECTION"
            },
            "enter_car_model": {
                "title": "Kindly enter your Car Model e.g. CRV",
                "next": {
                    "1": "enter_car_year_make",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "continue": {
                "title": "Bye for now, %s1 🙂\nThank you for choosing us",
                "next": {
                    "1": "enter_car_make",
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "done": {
                "title": "Bye for now, %s1 🙂\nThank you for choosing us",
                "next": {
                    "1": "enter_car_make",
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "complete": {
                "title": "Bye for now,😊\nThank you for choosing us",
                "next": {
                    "1": "enter_car_make",
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "enter_tax_id_number": {
                "title": "Kindly enter your Tax Identification Number(Tin)e.g *********",
                "next": {
                    "1": "enter_car_make",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "enter_tax_id_number_health": {
                "title": "Kindly enter your Tax Identification Number(Tin)e.g *********",
                "next": {
                    "1": "end",
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "end": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform www.oldmutual.rw",
                "input_type": "END"
            },
            "back_to_main": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform www.oldmutual.rw",
                "input_type": "END"
            },
            "otp_verification": {
                "title": "Kindly Insert the 4 Digit Code Sent To You Via SMS\n0 Main Menu\n00. Resend OTP",
                "next": {
                    "1": "customer_verification_success_end_message",
                    "0": "back_to_main",
                    "00": "otp_verification",
                },
                "input_type": "TEXT"
            },
            "health_worker_beneficiary_name": {
                "title": "Beneficiary Information\nKindly enter your beneficiary\'s name (first name & surname)\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_beneficiary_phone_no",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_hosp_claim_covid_report": {
                "title": "Kindly share your positive COVID-19 diagnosis report\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_last_death_letter": {
                "title": "Kindly share a photo of Police or Hospital Death Notification Letter indicating details of death\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            }
        },
        "9": {
            "enter_car_year_make": {
                "title": "Kindly enter your Car's Year of Manufacture.",
                "next": {
                    "1": "enter_car_reg_number",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
             "travel_covering": {
                "title": "Who are you covering?",
                "next": { "input": "travel_destination" },
                "input_type": "TEXT"
            },
            "customer_verification_success_end_message": {
                "title": "We have successfully received your details. Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform www.oldmutual.rw",
                "input_type": "END"
            },
            "end": {
                "title": "Thank you for your details. One of our consultants will call you shortly.",
                "input_type": "END"
            },
            "health_worker_beneficiary_phone_no": {
                "title": "Kindly enter your beneficiary\'s phone number\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_registration_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_success_end_message": {
                "title": "Kindly respond with \n1. To complete Claim Submission\n\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            }
        },
        "10": {
            "enter_car_reg_number": {
                "title": "Enter your car registration number. eg UAA 654U",
                "next": {
                    "1": "motor_cover_start_date",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
             "travel_destination": {
                "title": "What is the Destination Country?",
                "next": { "input": "end" },
                "input_type": "TEXT"
            },
            "end": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform www.oldmutual.rw",
                "input_type": "END"
            },
            "health_worker_registration_end_message": {
                "title": "We have successfully received your details.\nTo view our terms and conditions, click here: www.oldmutual.rwprivacy-policy and to view our online portal, click here: www.oldmutual.rw \n\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "success_end_message": {
                "title": "Thank you for your details. One of our consultants will call you shortly.",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            }
        },
        "11": {
            "motor_cover_start_date": {
                "title": "When would you like your cover to start? (DD/MM/YYYY)",
                "next": {
                    "1": "is_car_evaluated",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            }
            
        },
        "12": {
            "is_car_evaluated": {
                "title": "Has your car undergone evaluation?",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "success_end_message": {
                "title": "Thank you for your details. One of our consultants will call you shortly.",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            }
        },
        /*"13": {
            "success_end_message": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform www.oldmutual.rw",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            }
        },*/

        "generic": {
            "response_options": {
                "next": {
                    "1": "provide_details",
                    "2": "request_callback",
                    "0": "back_to_main"
                }
            },
            "provide_details": {
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                }
            },
            "success_end_message": {
                "title": "Thank you for your details. One of our consultants will call you shortly.",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "exit": {
                "title": "Thank You For the Details Provided, One of Our Call Center Agents Will Call You Within 24 Hours."
            },
            "covid_end_message": {
                "title": "A Link Has Sent To You Via SMS on"
            },
            "covid_update_message": {
                "title": "A Link Has Been Sent To You Via SMS on  \n0 Main Menu",
                "next": {
                    "0": "back_to_main",
                    "11": "exit"
                }
            },
            "covid_menu": {
                "title": "A Link Has Sent To You Via SMS on",
                "1": "How to Keep Healthy\n0 Main Menu",
                "2": "What To Do If You Feel Unwell\n0 Main Menu",
                "3": "How to Work From Home\n0 Main Menu",
                "4": "How to Study From Home\n0 Main Menu",
                "5": "How to Unwind From Home\n0 Main Menu",
                "6": "Ministry of Health Updates\n0 Main Menu",
                "0": "Back to Menu"
            },
            "insurance_class": [
                "Health Insurance",
                "Motor Insurance",
                "Life Insurance",
                "Other Insurance Insurance"
            ],
            "end": {
                "title": "Thank you for your details. One of our consultants will call you shortly.",
                "input_type": "END"
            }
        }
    }
}