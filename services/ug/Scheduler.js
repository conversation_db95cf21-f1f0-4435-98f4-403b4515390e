var whatsappDBService = require('./WhatsappDBService');
var aws = require('./AWSService');
var cron = require("node-cron");
var Json2csvParser = require('json2csv').Parser;
const fs = require('fs');

var result = null;

var sendLogsToS3Bucketjob = cron.schedule("55 23 * * *", function () {
    whatsappDBService.getTodaysLogs(async function(err, response) {
        if (err) {
            console.log(content.from + ' | wa_response: ' + err)
            res.json(err)

            } else {
                if(response.length > 0){
                    console.log('length '+ response.length)
                    result = true;
                    console.log(JSON.stringify(response))
                    const csvFields = ['Platform','PhoneNumber','CountryCode','Function','TimeStamp'];
                    const json2csvParser = new Json2csvParser({ csvFields });
                    const csv = json2csvParser.parse(JSON.parse(JSON.stringify(response, { csvFields }))
                    );
                 
                    console.log(csv);
                 
                    fs.writeFile('file.csv', csv, function(err) {
                        if (err) throw err;
                        console.log('file saved');
                      });

                      aws. addTransactionLogs()
                }
                else{
                    console.log('no files to write')
                }
               
                  
    
    }})
    
});



sendLogsToS3Bucketjob.start()