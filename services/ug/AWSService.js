const fs = require('fs');
const AWS = require('aws-sdk');
const path = require('path')
//const { S3Client, PutObjectCommand } = require("@aws-sdk/client-s3");


async function addTransactionLogs() {


uploadFile()

}



const uploadFile = () => {


    const s3 = new AWS.S3({
        region: process.env['OBSERVABILITY_REGION'],
        accessKeyId: process.env['OBSERVABILITY_ACCESS_KEY'],
        secretAccessKey: process.env['OBSERVABILITY_SECRET_ACCESS_KEY']
    });

 

 const fileName = path.join(__dirname, '..', '..', 'file.csv');
 let fileToWrite = `ug_observability${(new Date().toJSON().slice(0,10))}.csv`
 
  fs.readFile(fileName,'utf-8', (err, data) => {
     if (err) throw err;
     const params = {
         Bucket: process.env['OBSERVABILITY_BUCKET'], // pass your bucket name
         Key: 'Uganda/WhatsApp/'+fileToWrite, // file will be saved as testBucket/contacts.csv
         Body: JSON.parse(JSON.stringify(data, null, 2))
     };

     data = JSON.parse(JSON.stringify(data))
     s3.upload(params, function(s3Err, data) {
         if (s3Err) throw s3Err
         console.log(`File uploaded successfully at ${data.Location}`)
     });
  });

//a client can be shared by different commands.
// const client = new S3Client({ region: process.env['OBSERVABILITY_REGION'],
//     credentials: {
//         accessKeyId: process.env['OBSERVABILITY_ACCESS_KEY'],
//         secretAccessKey: process.env['OBSERVABILITY_SECRET_ACCESS_KEY'] }
//     });

//  const fileName = path.join(__dirname, '..', '..', 'file.csv');
//   fs.readFile(fileName,'utf-8', (err, data) => {
//      if (err) throw err;
//      const params = {
//          Bucket: process.env['OBSERVABILITY_BUCKET'], // pass your bucket name
//          Key: 'test.csv', // file will be saved as testBucket/contacts.csv
//          Body: JSON.parse(JSON.stringify(data, null, 2))
//      };

//      data = JSON.parse(JSON.stringify(data))
//      client.send(new PutObjectCommand(params));
//   });

};



module.exports = {
    addTransactionLogs: addTransactionLogs
}


