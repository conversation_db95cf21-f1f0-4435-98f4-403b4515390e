'use strict';

let db = require('../../models');
let utils = require('../Utils');
const moment = require('moment');
const Op = require('sequelize').Op;
let phoneParser = require('../../utils/phone_parser');

var { wa_user, whatsapp_user_session, wa_requests, observability } = require("./DbService")


async function updateUserSession(params, callback) {
    whatsapp_user_session.upsert(params).then(result => {
        callback(false, result)
            /*if (result)
                callback(false, result)
            else {
                callback(true,'Update failed')
            }*/
    });
}

async function updateCountry(params, callback) {
    whatsapp_user_session.update(params, { where: { id: params.id } }).then(result => {
        callback(false, result)
            /*if (result)
                callback(false, result)
            else {
                callback(true,'Update failed')
            }*/
    });
}

async function isRequestExisting(params, callback) {
    wa_requests.findOne({ where: { phone_no: params.phone_no, message_id: params.message_id } })
        .then(result => {
            if (result) {
                params.request_count = result.request_count + 1
                console.log(utils.getDateTime() + " | DUPLICATE REQ: " + JSON.stringify(params))
                    // console.log('To update: ' + JSON.stringify(params))
                wa_requests.update(params, { where: { id: result.id } }).then(r => {
                    callback(true)
                })
            } else {
                wa_requests.create(params).then(result => {
                    // console.log('WA request created? ' + result)
                    callback(false)
                })
            }

        })
}

async function updateWaUser(params, callback) {
    wa_user.upsert(params).then(result => {
        callback(false, result)
    })
}


async function getUserLevel(phone_no, callback) {
    whatsapp_user_session.findOne({
        where: { phone_no: phone_no }
    }).then(
        result => {
            if (result) {
                callback(false, result)
            } else {
                callback(true, 'User does not exit');
            }
        });
}


async function getPreviousUniqueIdentifier(phone_no, callback) {
    observability.findAll({
        where: { phone_number: phone_no },
        order: [['time_stamp', 'DESC']] ,
        limit: 2
    }).then(
        result => {
            if (result) {
                callback(false, result)
            } else {
                callback(true, 'User does not exit');
            }
        });
}

async function getTodaysLogs(callback) {

      const TODAY_START = moment().format('YYYY-MM-DD 00:00');
      const NOW = moment().format('YYYY-MM-DD 23:59');

     observability.findAll({

        where: {
            time_stamp: {
                [Op.between]: [
                    TODAY_START,
                    NOW,
                ]
            }
        },
        attributes: ['platform','phone_number','country_code','user_function','time_stamp']
        }).then(
        result => {
            if (result) {
                callback(false, result)
            } else {
                callback(true, 'No');
            }
        });
}


async function addTransactionLogs(user_function, phone_no, callback) {

    const countryCode = phoneParser(phone_no).countryCode
    var params = {
        platform :'WA',
        phone_number: phone_no,
        country_code: countryCode,
        user_function:user_function,
        time_stamp :Date.now()
       
    }
    observability.upsert(params).then(result => {
        callback(false, result)
            /*if (result)
                callback(false, result)
            else {
                callback(true,'Update failed')
            }*/
    });
}

module.exports = {
    updateUserSession: updateUserSession,
    addTransactionLogs: addTransactionLogs,
    isRequestExisting: isRequestExisting,
    updateCountry: updateCountry,
    getUserLevel: getUserLevel,
    getPreviousUniqueIdentifier:getPreviousUniqueIdentifier,
    getTodaysLogs : getTodaysLogs
}