'use strict';

var whatsAppMessageService = require('./WhatsAppService');
var whatsAppMenuService = require('../WhatsAppMenuService');
var whatsappDBService = require('./WhatsappDBService');

var selfservice = require('./SelfService-API');
var motorService = require('./motorService');
var sessionService = require('./SessionService');
var validateService = require('./ValidateInputService');
var mailService = require('./MailService')
const dotenv = require('dotenv')
const { promisify } = require('util')


let utils = require('../Utils');
let source_channel = 2;
let enforceHWSingleRegistration = true
let tnc_link = 'https://www.uapoldmutual.co.ug/'
let site = 'https://www.uapoldmutual.co.ug/'

let salutation_trigger = [
    'hi', 'hello', 'helo', 'halo', 'hallo', 'hey', 'mambo', 'vipi', 'niaje', 'menu', 'help'
];
let salutation_response = ['Hi', 'Hello', 'Hello', 'Hi', 'Hi', 'Hi', 'Hi``', 'Hi', 'Hi', 'Hi', 'Hi'];
let res;
let level, shouldUpdateSession = true;
let menu_data = {}
let menu = '';
let client_name;

async function handleUserText(req, _res, source_country = 'ke') {
   
    res = _res;
    let content = req.body.results[0];
    let unique_identifier = content.message.text
    let phone_no = content.from



    let sal_index = salutation_trigger.indexOf(unique_identifier.toLocaleLowerCase());
 
    if (sal_index > -1) {
        unique_identifier = 'start';
        await saveUserDetails(salutation_response[sal_index], content, source_country)
        whatsappDBService.getUserLevel(phone_no, async function(err, user_response) {

            if (err) {
                initialWelcome(salutation_response[sal_index], content, source_country)
            } else {
                console.log(utils.getDateTime() + " | SAL USED: " + content.from + ' | SELECTION - input: ' + content.message.text)

                if (user_response.country != null) {
                    //console.log(content.from + ' | country: ' + country)
                    level = 1
                    unique_identifier = user_response.country
                } else if (user_response.country == null) {
                    // console.log('===> Enten')
                    level = 0
                    unique_identifier = 'start'
                    user_response.level = 0
                    updateCountry({ id: user_response.id, phone_no: phone_no, level: 0 })
                }
                console.log(`unique_identifier ${unique_identifier}`)
                menu = whatsAppMenuService.getMenu(level, unique_identifier, user_response)
                let name = ""
               
                if (level == '0' || level == '1') {
                    selfservice.create_wa_user(phone_no, name, user_response.country, function(err, resp) {
                        if (!err)
                            console.log('----> WhatsAPP user created error ' + resp)
                    })

                    user_response.path = 'start'
                }
                menu_data = getMenu(1,'uganda', user_response)
                
                const sleep = promisify(setTimeout)
                whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "Hi! Welcome to *Old Mutual Uganda*", function(err, wa_response) {
                    if (err) {
                        console.log(content.from + ' | wa_response: ' + err)
                        res.json(err)

                        } else {
                            
                        
                }})

                
                await sleep(1e3)
                whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "What would you like to do?", function(err, wa_response) {
                        if (err) {
                            console.log(content.from + ' | wa_response: ' + err)
                            res.json(err)
    
                            } else {
                            // if (shouldUpdateSession)
                            
                }})
                await sleep(1e3)
                whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, menu_data.menu, function(err, wa_response) {
                    if (err) {
                        console.log(content.from + ' | wa_response: ' + err)
                        res.json(err)

                    } else {
                        // if (shouldUpdateSession)
                        whatsappDBService.updateUserSession({
                            phone_no: content.from,
                            level: 1,
                            input: content.message.text,
                            unique_identifier: unique_identifier,
                            expected_input_type: 'SELECTION',
                            path: user_response.path + '*' + unique_identifier,
                            country_id: 2
                        }, function(err, session_response) {
                            if (err) {
                                console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                    // res.json(err)
                            } else {
                                try {
                                    res.json(wa_response)
                                } catch (e) {

                                }
                            }
                        })
                    }
                })
                logTransactions('Menu', phone_no)

            }
        })
    } 
    else if (content.message.text.toUpperCase() == 'Q') {
        whatsappDBService.getUserLevel(phone_no, function(err, user_response) {
            level = user_response.level -1
            
            console.log(`getting to quit ${user_response.unique_identifier}`)
            unique_identifier = user_response.unique_identifier
           
            if (!shouldUpdateSession)
                level = user_response.level
                    selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                    if (err) {
                        console.log('*** Unique ID ignored')
                    } else {
                        console.log('*** Session Saved')
                        var level = user_response.level + 1
                        menu_data = getMenu(8,'complete', user_response)
                      
                        sendToWhatsApp(source_country, phone_no, menu_data.menu, 'END', 'Quit', 8, user_response.path)
    
                    }
    
                })
        })
        
    }
    else if(content.message.text.toUpperCase() == 'B'){
    
        whatsappDBService.getUserLevel(phone_no, async function(err, user_response) {

            if (err) {
                initialWelcome(salutation_response[sal_index], content, source_country)
            } else {

                level = 1
                unique_identifier = user_response.country
                menu_data = getMenu(1,'uganda', user_response)
             
                const sleep = promisify(setTimeout)
           
                
                await sleep(1e3)
                whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "What would you like to do?", function(err, wa_response) {
                        if (err) {
                            console.log(content.from + ' | wa_response: ' + err)
                            res.json(err)
    
                            } else {
                            // if (shouldUpdateSession)
                            
                }})
                await sleep(1e3)
                whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, menu_data.menu, function(err, wa_response) {
                    if (err) {
                        console.log(content.from + ' | wa_response: ' + err)
                        res.json(err)

                    } else {
                        // if (shouldUpdateSession)
                        whatsappDBService.updateUserSession({
                            phone_no: content.from,
                            level: 1,
                            input: content.message.text,
                            unique_identifier: unique_identifier,
                            expected_input_type: 'SELECTION',
                            path: user_response.path + '*' + unique_identifier
                        }, function(err, session_response) {
                            if (err) {
                                console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                    // res.json(err)
                            } else {
                                try {
                                    res.json(wa_response)
                                } catch (e) {

                                }
                            }
                        })
                    }
                })
                logTransactions('Back', phone_no)
            }
    
        })

    }
    else {

        whatsappDBService.getUserLevel(phone_no, async function(err, user_response) {
            if (err) {
                initialWelcome(salutation_response[0], content, source_country)
            } else {
                
                console.log(utils.getDateTime() + " | REQ: " + content.from + ' Data: ' + JSON.stringify(user_response) + ' | USER INPUT: ' + content.message.text + ' Msg Id: ' + content.messageId)
                console.log(`user_response.expected_input_type ${user_response.expected_input_type}`)
                if (user_response.expected_input_type === 'SELECTION') { // Menu selected
                    if (content.message.text == '0' && user_response.level == '1') {
                        level = 0
                        user_response.level = 0
                        user_response.country = null
                        unique_identifier = 'start'
                        initialWelcome('Hi', content, source_country)
                        return;
                    } else if (content.message.text == '0' || user_response.unique_identifier == 'back_to_main') {
                        level = 1
                        user_response.level = 1
                        unique_identifier = user_response.country
                        user_response.unique_identifier = user_response.country
                        user_response.path = 'start'
                    }
                

                    
                    else {
                        level = user_response.level + 1
                        unique_identifier = getCurrentUniqueIdentifier(content.message.text, user_response)
                        console.log(`unique_identifier ${unique_identifier}`)
                        if (typeof unique_identifier == 'undefined') {
                            console.log('----> Unique undefined ' + content.message.text)
                            level = user_response.level
                            unique_identifier = user_response.unique_identifier
                        }
                        if (!shouldUpdateSession)
                            level = user_response.level
                    }
                  

                    console.log(utils.getDateTime() + " | " + content.from + ' | SELECTION level: ' + level + ' NEW unique_identifier ' + unique_identifier + ' OLD unique_identifier ' + user_response.unique_identifier + ' input ' + content.message.text + ' expected_input_type ' + user_response.expected_input_type)

                    let validation_error_msg = '';
                    if (unique_identifier === 'request_callback') {

                        menu_data = getMenu(level, unique_identifier, user_response)
                        unique_identifier = menu_data.unique_identifier
                        let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

                        user_response.path = user_response.path + '*end'
                        selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                            if (err) {} else {
                                menu = getTicketResponseMessage(user_response.whatsapp_name, ticket_no, unique_identifier)
                                sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                endTicketSession(phone_no)
                                formulateRequestCallBack(phone_no, user_response.whatsapp_name)
                            }
                        })

                    } else if (unique_identifier === unique_identifier.includes('success_end_message')) {
                        menu_data = getMenu(level, unique_identifier, user_response)
                        unique_identifier = menu_data.unique_identifier
                        let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

                        user_response.path = user_response.path + '*end'
                        selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                            if (err) {} else {
                                menu = getTicketResponseMessage(user_response.whatsapp_name, ticket_no, unique_identifier)
                                sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                endTicketSession(phone_no)
                            }
                        })
                    } else if (unique_identifier === 'covid_update_message') {
                        selfservice.SMS_Update(phone_no, content.message.text, function(err, _cov_resp) {
                            if (err) {
                                _res.json(false)
                                sendToWhatsApp(source_country, phone_no, "Error processing your request. Please try again.\n0 Main Menu", 'END', unique_identifier, level)
                            } else {
                                let cov_resp = JSON.parse(_cov_resp)
                                sendToWhatsApp(source_country, phone_no, cov_resp.content + '\n0 Main Menu', 'END', unique_identifier, level)
                            }
                        })
                    }
      
  

                    else if ((unique_identifier === 'register_claim' || unique_identifier === 'buy_insurance') &&  user_response.unique_identifier === 'uganda') {
                        let phone_no = content.from
                        menu_data = getMenu(level, unique_identifier, user_response)
                        user_response.target_product = unique_identifier
                        let name = ""
                        if (level == '1') {
        
                            selfservice.create_wa_user(phone_no, name, user_response.country, function(err, resp) {
                                if (!err)
                                    console.log('----> WhatsAPP user created ' + resp)
                            })
                        }

                        unique_identifier = menu_data.unique_identifier


                        let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);
                        
                            
                        selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, 'SELECTION', user_response.path, function(err, resp) {
                            if (err) {
                                console.log('*** Unique ID ignored' + err)
                            } else {
                                 const reply_options = [
                                        {
                                            "type": "REPLY",
                                            "id": "1",
                                            "title": "Motor insurance"
                                        },
                                        {
                                            "type": "REPLY",
                                            "id": "2",
                                            "title": "Health insurance"
                                        },
                                        {
                                            "type": "REPLY",
                                            "id": "3",
                                            "title": "Life insurance"
                                        }
                                    ]
                                whatsAppMessageService.sendWhatsReplyButtons(source_country, content.from, menu_data.menu,reply_options, function(err, wa_response) {
                                    if (err) {
                                        console.error(content.from + ' | wa_response: ' + err)
                                    } else {
                                        // let _level = user_response.level + 1

                                        console.error(content.from + ' | wa_response: ' + err)

                                        let params = {
                                                phone_no: content.from,
                                                level: level,
                                                input: content.message.text,
                                                unique_identifier: unique_identifier,
                                                country: user_response.country,
                                                country_id: user_response.country_id,
                                                expected_input_type: input_type,
                                                path: user_response.path + '*' + unique_identifier,
                                                target_product: unique_identifier
                                            }

                                        if (shouldUpdateSession)
                                            whatsappDBService.updateUserSession(params, function(err, session_response) {

                                                if (err) {
                                                    console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                        // res.json(err)
                                                } else {
                                                    try {
                                                        res.json(wa_response)
                                                    } catch (e) {

                                                    }
                                                }
                                            })
                                    }
                                })
                                logTransactions(unique_identifier, phone_no)
                            }

                        })
                    }
                   
                    else if (unique_identifier === 'uganda') {

                        if(content.message.text != '1' || content.message.text != '2')
                        {
                            menu_data = getMenu(1,'uganda', user_response)
                
                        const sleep = promisify(setTimeout)
                        user_response.path = 'start'
                        whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "Please type the number that *corresponds to your choice.*", function(err, wa_response) {
                            if (err) {
                                console.log(content.from + ' | wa_response: ' + err)
                                res.json(err)

                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (e) {

                                    }
                                
                        }})


                      
                      
                         }
                        
                    }
                    else if ((unique_identifier === 'register_claim' ||unique_identifier === 'buy_insurance') && user_response.unique_identifier !== 'uganda') {

                        if(content.message.text != null || content.message.text != '')
                        {
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "Please select the options provided.\n\n(e.g. select Motor insurance)", function(err, wa_response) {
                                if (err) {
                                    console.log(content.from + ' | wa_response: ' + err)
                                    res.json(err)
    
                                    } else {
                                        try {
                                            res.json(wa_response)
                                        } catch (e) {
    
                                        }
                                    
                            }})
                
                        }
                                    
               
                    }
                    else if (user_response.unique_identifier === 'register_claim') {
                    //else if ((unique_identifier === 'claim_car' ||unique_identifier === 'claim_health') && user_response.unique_identifier === 'register_claim') {

                        if(content.message.text != null || content.message.text != '')
                        {
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "Please select the options provided.\n\n(e.g. select Motor insurance)", function(err, wa_response) {
                                if (err) {
                                    console.log(content.from + ' | wa_response: ' + err)
                                    res.json(err)
    
                                    } else {
                                        try {
                                            res.json(wa_response)
                                        } catch (e) {
    
                                        }
                                    
                            }})
                
                        }
                                    
               
                    }
                    else if (unique_identifier === 'provide_details_car' || unique_identifier === 'provide_details_health'||  unique_identifier === 'provide_details_life') {

                        if(content.message.text != null || content.message.text != '')
                        {
                            if(content.message.text != null || content.message.text != '')
                            {
                                whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "Please select the options provided.\n\n(e.g. select Provide details", function(err, wa_response) {
                                    if (err) {
                                        console.log(content.from + ' | wa_response: ' + err)
                                        res.json(err)
        
                                        } else {
                                            try {
                                                res.json(wa_response)
                                            } catch (e) {
        
                                            }
                                        
                                }})
                            }

                        }
                      
                
                       
                                    
               
                    }
                    else if ((unique_identifier === 'health_insurance' || unique_identifier === 'motor_insurance' || unique_identifier === 'life_insurance') && user_response.unique_identifier ==='buy_insurance') {

                        if(content.message.text != null || content.message.text != '')
                        {
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "Please select the options provided.\n\n(e.g. select Motor insurance)", function(err, wa_response) {
                                if (err) {
                                    console.log(content.from + ' | wa_response: ' + err)
                                    res.json(err)
    
                                    } else {
                                        try {
                                            res.json(wa_response)
                                        } catch (e) {
    
                                        }
                                    
                            }})
                        }
                      
                
                       
                                    
               
                    }
                    else if (unique_identifier === 'health_insurance' || unique_identifier === 'motor_insurance') {

                        if(content.message.text.toUpperCase() != 'B' )
                        {
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "Please select the option provided.", function(err, wa_response) {
                                if (err) {
                                    console.log(content.from + ' | wa_response: ' + err)
                                    res.json(err)
    
                                    } else {
                                        try {
                                            res.json(wa_response)
                                        } catch (e) {
    
                                        }
                                    
                            }})
                        }
                
                       
                                    
               
                    }
                    else if ((user_response.unique_identifier === 'claim_car' || user_response.unique_identifier === 'claim_health' || user_response.unique_identifier === 'claim_life')&& content.message.text.toUpperCase() != 'B') {

                        if(content.message.text.toUpperCase() != 'B')
                        {
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "Please select the options provided.\n\n(e.g. select Provide details", function(err, wa_response) {
                                if (err) {
                                    console.log(content.from + ' | wa_response: ' + err)
                                    res.json(err)
    
                                    } else {
                                        try {
                                            res.json(wa_response)
                                        } catch (e) {
    
                                        }
                                    
                            }})
                        }
                
                       
                                    
               
                    }
                    else if (unique_identifier === 'continue_done' || unique_identifier === 'done_uploading' || unique_identifier ==='complete') {

                        if(content.message.text.toUpperCase() != 'B')
                        {
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "Please select the options provided.\n\n(e.g. select back to main menu)", function(err, wa_response) {
                                if (err) {
                                    console.log(content.from + ' | wa_response: ' + err)
                                    res.json(err)
    
                                    } else {
                                        try {
                                            res.json(wa_response)
                                        } catch (e) {
    
                                        }
                                    
                            }})
                        }
                
                       
                                    
               
                    }
                    else if (unique_identifier === 'enter_name_surname') {

                        if(content.message.text.toUpperCase() != 'B')
                        {
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "Please select the option provided.", function(err, wa_response) {
                                if (err) {
                                    console.log(content.from + ' | wa_response: ' + err)
                                    res.json(err)
    
                                    } else {
                                        try {
                                            res.json(wa_response)
                                        } catch (e) {
    
                                        }
                                    
                            }})
                        }
                
                       
                                    
               
                    }
                    else if((unique_identifier ==='continue' ||unique_identifier ==='done') && content.message.text.toUpperCase() != 'B' )
                    {
                       
                         
                                whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "Please select the options provided.\n\n(e.g. select back to main menu", function(err, wa_response) {
                                    if (err) {
                                        console.log(content.from + ' | wa_response: ' + err)
                                        res.json(err)
        
                                        } else {
                                            try {
                                                res.json(wa_response)
                                            } catch (e) {
        
                                            }
                                        
                                }})
                            

                        
                      
                    }
                    
                    // else {
                    //     validationPassed(source_country, level, unique_identifier, user_response, content)
                    // }

                } else if (user_response.expected_input_type === 'END') {
                    if (unique_identifier == 'success_end_message') {
                        user_response.unique_identifier = unique_identifier
                    }
                    selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                        if (err) {
                            console.log('*** Unique ID ignored')
                        } else {
                            endTicketSession(phone_no)
                        }

                    })
                    unique_identifier = getCurrentUniqueIdentifier(content.message.text, user_response)
                    console.log(utils.getDateTime() + " | REQ: " + content.from + ' | END level: ' + user_response.level + ' input ' + content.message.text + ' Msg Id: ' + content.messageId)
                    level = 1
                    unique_identifier = user_response.country
                    menu_data = getMenu(level, unique_identifier, user_response)
                    menu = menu_data.menu

                    if (content.message.text == 1) { //Done uploading photos


                    } else {
                        whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, menu, function(err, wa_response) {
                            if (err) {
                                console.error(content.from + ' | wa_response: ' + err)
                            } else {
                                // if (shouldUpdateSession)

                                whatsappDBService.updateUserSession({
                                    phone_no: content.from,
                                    level: 1,
                                    expected_input_type: 'SELECTION',
                                    unique_identifier: unique_identifier,
                                    path: user_response.path + '*end',
                                }, function(err, session_response) {

                                    if (err) {
                                        console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                            // res.json(err)
                                    } else {
                                        try {
                                            res.json(wa_response)
                                        } catch (e) {

                                        }
                                    }
                                })
                            }
                        })
                    }
                } else if (user_response.expected_input_type === 'IMAGE') {
                    if (content.message.text == '0' || user_response.unique_identifier == 'back_to_main') {
                        level = 1
                        user_response.level = 1
                        unique_identifier = user_response.country
                        user_response.unique_identifier = user_response.country
                        user_response.path = 'start'
                        console.log(content.from + ' | IMAGE ****: ')
                    } else {
                        level = user_response.level + 1
                        unique_identifier = getCurrentUniqueIdentifier(1, user_response) // An IMAGE share level always has one NEXT step!
                    }
                    console.log(content.from + ' | REQ: IMAGE level: ' + user_response.level + ' new unique_identifier ' + unique_identifier + ' expected_input_type ' + user_response.expected_input_type + ' Msg Id: ' + content.messageId)

                    menu_data = getMenu(level, unique_identifier, user_response)
                    menu = menu_data.menu
                    unique_identifier = menu_data.unique_identifier
                    let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);


                    if (content.message.text == 1) { //Done uploading photos
                        if (unique_identifier === 'provide_details_health_worker_success_end_message') {
                            // Send photos
                            // Close whatsapp session
                            // Respond back to user.
                            console.log('claim_req health worker:')
                            selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                                if (err) {
                                    _res.json(false)
                                } else {
                                    if (ticket_no > 0) {
                                        selfservice.healthcare_worker_claim_request(ticket_no, function(err, claim_response) {
                                            if (err) {
                                                _res.json(false)
                                            } else {
                                                console.log('claim_response: ' + claim_response)
                                                user_response.path = user_response.path + '*' + unique_identifier
                                                sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)

                                                console.log('Ticket to close - HealthWorker: ' + ticket_no)
                                                selfservice.close_user_WhatsApp_Session(phone_no, ticket_no, function(err, close_sess_response) {
                                                    if (err) {

                                                    } else {
                                                        console.log('Session closed for: @close_user_WhatsApp_Session' + phone_no)
                                                    }

                                                })

                                            }
                                        })

                                    }
                                }

                            })

                        } else if (unique_identifier === 'provide_details_photos_end') {
                            console.log('Formulate Email')
                            selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                                if (err) {
                                    console.log(`Error ${err}`)
                                    _res.json(false)
                                } else {
                                    console.log(`ticket_no ${ticket_no}`)
                                    console.log(`ticket_no (ticket_no > 0) ${(ticket_no > 0)}`)

                                    if (ticket_no > 0) {
                                        user_response.path = user_response.path + '*end'
                                        menu = getTicketResponseMessage(user_response.whatsapp_name, ticket_no, user_response.unique_identifier)
                                        sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                        console.log(`user_response.whatsapp_name  ${user_response.whatsapp_name}`)
                                        formulateClaimsData(phone_no, user_response.whatsapp_name)
                                        endTicketSession(phone_no)
                                    } else
                                        _res.json(false)
                                }
                            })

                        }
                    } else if (unique_identifier === 'provide_details_photos_end') {
                        console.log('Formulate Email 2 ' + content.message.text)
                        if (content.message.text == '1') {
                            selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                                if (err) {
                                    _res.json(false)
                                } else {
                                    if (ticket_no > 0) {
                                        user_response.path = user_response.path + '*end'
                                        menu = getTicketResponseMessage(user_response.whatsapp_name, ticket_no, user_response.unique_identifier)
                                        sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                        formulateClaimsData(phone_no, user_response.whatsapp_name)
                                        endTicketSession(phone_no)
                                    } else
                                        _res.json(false)
                                }
                            })

                        } else {
                            let validation_error_msg = 'Invalid input Type 1 to complete your submission \n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else
                                    res.json(wa_response)
                            })
                        }
                    } else {
                        if (input_type == 'END') {
                            user_response.unique_identifier = 'success_end_message'
                            user_response.path = user_response.path + '*end'
                        }

                        selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                            if (err) {
                                console.log('*** Unique ID ignored')
                            } else {
                                // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)

                                user_response.path = user_response.path + '*' + unique_identifier
                                sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                endTicketSession(phone_no)
                            }

                        })


                    }
                } else {
                    
                   
                    

                    console.log(content.from + ' | level: ' + level + ' new unique_identifier ' + unique_identifier + ' OLD ' + user_response.unique_identifier + ' expected_input_type ' + user_response.expected_input_type + ': ' + content.message.text)



                   
                    if (user_response.unique_identifier === 'health_worker_national_id' || user_response.unique_identifier === 'verify_id_passport') {
                        let data = validateService.isValidIdOrPassportNo(content.message.text)
                        if (data.error) {
                            level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = 'Invalid input\n' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                } else
                                    res.json(wa_response)
                            })
                        } else {
                            if (user_response.unique_identifier === 'health_worker_national_id')
                                selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                    if (err) {
                                        console.log('*** Unique ID ignored')
                                    } else {
                                        user_response.path = user_response.path + '*' + unique_identifier
                                        sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)

                                    }

                                })
                            else
                                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                    if (err) {
                                        console.log('*** Unique ID ignored')
                                    } else {
                                        user_response.path = user_response.path + '*' + unique_identifier
                                        sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)

                                    }

                                })
                        }

                    } else if (content.message.text == '00' && user_response.unique_identifier === 'otp_verification') {

                        selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                            if (err) {
                                _res.json(false)
                            } else {

                                sessionService.getWhatsAppSessionData(ticket_no, function(err, resp) {
                                    if (err) {
                                        _res.json(false)
                                    } else {

                                        resp.forEach(element => {

                                            if (element.key === "customer_verification_phone") {
                                                console.log('OTP Resend: ' + JSON.stringify(resp))
                                                selfservice.sendOTP(element.session_data, function(err, otp_resp) {
                                                    if (err) {
                                                        menu_data = getMenu(7, 'signup_phone_no', user_response)
                                                        menu = menu_data.menu
                                                        sendToWhatsApp(source_country, phone_no, menu, 'TEXT', 'signup_phone_no', 7, user_response.path)
                                                    } else {
                                                        menu_data = getMenu(8, 'otp_verification', user_response)
                                                        menu = menu = 'OTP code has been sent\n' + menu_data.menu
                                                        sendToWhatsApp(source_country, phone_no, menu, 'TEXT', 'otp_verification', 8, user_response.path)
                                                            // endTicketSession(phone_no)
                                                    }
                                                })
                                            }


                                        })

                                    }


                                })

                            }
                        })
                    } else if (user_response.unique_identifier === 'enter_tax_id_number') {
                        // validate tax number
                        let valid = validateService.validateTaxIDNumber(content.message.text, source_country)
                        if (!valid) {
                            let validation_error_msg = 'Invalid Taxpayers Identification Number\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {

                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    // console.log('*** Unique ID stored!' + resp)
                                    var currentPath = user_response.path.split("*")
                                    var menu = currentPath[3]
                                    if (menu === "1") {
                                        var level = user_response.level + 1
                                        menu_data = getMenu(level, 'enter_car_make', user_response)
                                        sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'enter_car_make', level, user_response.path)
                                    } else {
                                        endSesssionGetData(phone_no, menu)
                                        var level = user_response.level + 1
                                        menu_data = getMenu(level, 'success_end_message', user_response)
                                        sendToWhatsApp(source_country, phone_no, menu_data.menu, 'END', 'success_end_message', level, user_response.path)


                                    }
                                }

                            })

                        }
                    } 

                    else if(unique_identifier == 'continue_done'){
                        selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                            if (err) {
                                console.log('*** Unique ID ignored' + err)
                            } else {
            
                               
                                const reply_options = [
                                    {
                                        "type": "REPLY",
                                        "id": "1",
                                        "title": "Back to main menu"
                                    },
                                    {
                                        "type": "REPLY",
                                        "id": "2",
                                        "title": "I'm done, for now"
                                    }
                                ]
                                whatsAppMessageService.sendWhatsReplyButtons('rw', content.from, menu_data.menu, reply_options, function(err, wa_response) {
                                    if (err) {
                                        console.error(content.from + ' | wa_response: ' + err)
                                    } else {
                                        // let _level = user_response.level + 1
                    
                                        console.error(content.from + ' | wa_response: ' + err)
                    
                                        let params = {
                                                phone_no: content.from,
                                                level: level,
                                                input: content.message.id,
                                                unique_identifier: unique_identifier,
                                                country: user_response.country,
                                                country_id: user_response.country_id,
                                                expected_input_type: input_type,
                                                path: user_response.path + '*' + unique_identifier
                                            }
                    
                                        if (shouldUpdateSession)
                                            whatsappDBService.updateUserSession(params, function(err, session_response) {
                    
                                                if (err) {
                                                    console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                        // res.json(err)
                                                } else {
                                                    try {
                                                        res.json(wa_response)
                                                    } catch (e) {
                    
                                                    }
                                                }
                                            })
                                    }
                                })
                                logTransactions(unique_identifier, phone_no)

                                console.log('Target product'+ user_response.target_product)
                                if(user_response.target_product == 'register_claim'){
                                    endTicketSession(phone_no)
                                    formulateRequestCallBack(phone_no, user_response.whatsapp_name)
                                }
                                else{
                                    endTicketSession(phone_no)
                                    formulateData(response, menu, phone_no)
                                }
                            }
                    
                        })
                    } 

                    else if (user_response.unique_identifier === 'enter_national_id') {
                        console.log(`National ID ${content.message.text}`)
                        let valid = validateService.validateIdNumber(content.message.text, source_country)
                        if (!valid) {
                            let validation_error_msg = 'Invalid National ID\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, 'continue', function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    console.log('*** Session Saved')
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'continue', user_response)
                                    sendToWhatsApp(source_country, phone_no, menu_data.menu, 'IMAGE', 'continue', level, user_response.path)

                                }

                            })

                        }
                    
                
                    } else if (user_response.unique_identifier === 'provide_details_car') {
                        console.log(`Reg Number ${content.message.text}`)
                        let valid =  true//validateService.validateString(content.message.text)
                        if (!valid) {
                            let validation_error_msg = 'Invalid Car Registration No\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.json(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'provide_details_car_photos', user_response)
                                    sendToWhatsApp(source_country, phone_no, menu_data.menu, 'IMAGE', 'provide_details_car_photos', level, user_response.path)

                                }

                            })
                        }

                    } else if (user_response.unique_identifier === 'enter_name_surname') {
                        console.log(`full name ${content.message.text}`)
                        let valid = validateService.validateNameAndSurname(content.message.text)

                      
                        if (!valid) {
                            let validation_error_msg = 'Invalid Name and Surname\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "Please enter your name and surname correctly.", function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            var client_names = content.message.text.split(' ')
                            client_name = client_names[0]
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    console.log('*** Session Saved')
                                    selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                                        if (!err && ticket_no) {
                                            // Save to motor customer table
                                            motorService.createCustomer(phone_no, ticket_no, {
                                                fullName: content.message.text
                                            }, function(err, result) {
                                                if (err) {
                                                    console.log('*** Error saving to motor customer: ', err);
                                                } else {
                                                    console.log('*** Saved to motor customer table');
                                                }
                                            });
                                        }
                                    });
                                    logTransactions('enter_name_surname',phone_no)
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'enter_mobile_number', user_response)
                                    let menu = utils.parameterizedString(menu_data.menu, client_name)
                                    let path =  user_response.path + '*' + user_response.unique_identifier
                                    sendToWhatsApp(source_country, phone_no, menu, 'TEXT', 'enter_mobile_number', level, path)

                                }

                            })

                        }
                    }else if (user_response.unique_identifier === 'enter_mobile_number') {
                    
                        let valid =  validateService.validateInternationalPhoneNumber(content.message.text)
                        if (!valid) {
                            let validation_error_msg = 'Oops, that didn’t work. Please enter a correct *mobile number*.'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)
                            let menu = utils.parameterizedString(menu_data.menu, client_name)

                            menu = validation_error_msg + '' + menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, validation_error_msg, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {


                                    }
                                }
                            })
                        } else {
                            selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                                if (!err && ticket_no) {
                                    // Update existing customer record
                                    selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                        if (err) {
                                            console.log('*** Unique ID ignored')
                                        } else {
                                            motorService.getCustomerBySession(ticket_no, function(err, customer) {
                                            if (!err && customer) {
                                                // Update with mobile number
                                                motorService.updateCustomer(ticket_no, {
                                                    phoneNumber: content.message.text
                                                }, function(err, result) {
                                                    if (err) {
                                                        console.log('*** Error updating motor customer: ', err);
                                                    } else {
                                                        console.log('*** Updated motor customer with mobile', result);
                                                    }
                                                });
                                            }
                                            });
                                        }
                                    });
                                    console.log('*** Session Saved')
                                    logTransactions(user_response.unique_identifier, phone_no)
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'enter_email_address', user_response)
                                    let menu = utils.parameterizedString(menu_data.menu, content.message.text)
                                    let path =  user_response.path + '*' + user_response.unique_identifier
                                    sendToWhatsApp(source_country, phone_no, menu, 'TEXT', 'enter_email_address', level, path )

                                }

                            })

                        }
                    } else if (user_response.unique_identifier === 'enter_email_address') {

                        let valid = validateService.validateEmail(content.message.text)
                        if (!valid) {
                            let validation_error_msg = 'Oops, that didn\'t work. Please enter a correct *email address*.'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)
                            let menu = utils.parameterizedString(menu_data.menu, client_name)

                            menu = validation_error_msg + '' + menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, validation_error_msg, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                                if (!err && ticket_no) {
                                    // Update existing customer record
                                    selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                        if (err) {
                                            console.log('*** Unique ID ignored')
                                        } else {
                                            motorService.getCustomerBySession(ticket_no, function(err, customer) {
                                            if (!err && customer) {
                                                // Update with email address
                                                motorService.updateCustomer(ticket_no, {
                                                    emailAddress: content.message.text
                                                }, function(err, result) {
                                                    if (err) {
                                                        console.log('*** Error updating motor customer: ', err);
                                                    } else {
                                                        console.log('*** Updated motor customer with email', result);
                                                    }
                                                });
                                            }
                                            });
                                        }
                                    });
                                    console.log('*** Session Saved')
                                    logTransactions(user_response.unique_identifier, phone_no)
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'enter_car_make', user_response)
                                    let menu = utils.parameterizedString(menu_data.menu, content.message.text)
                                    let path =  user_response.path + '*' + user_response.unique_identifier
                                    sendToWhatsApp(source_country, phone_no, menu, 'TEXT', 'enter_car_make', level, path )

                                }

                            })

                        }
                    } else if (user_response.unique_identifier === 'enter_car_make') {

                        // Simple validation for car make - should not be empty and should have reasonable length
                        let valid = content.message.text && content.message.text.trim().length > 1 && content.message.text.trim().length < 50
                        if (!valid) {
                            let validation_error_msg = 'Oops, that didn\'t work. Please enter a correct *car make*.'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)
                            let menu = utils.parameterizedString(menu_data.menu, client_name)

                            menu = validation_error_msg + '' + menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, validation_error_msg, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                                if (!err && ticket_no) {
                                    // Create vehicle record with make
                                    selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                        if (err) {
                                            console.log('*** Unique ID ignored')
                                        } else {
                                            motorService.createVehicle(ticket_no, {
                                                make: content.message.text
                                            }, function(err, result) {
                                                if (err) {
                                                    console.log('*** Error creating vehicle: ', err);
                                                } else {
                                                    console.log('*** Created vehicle with make', result);
                                                }
                                            });
                                        }
                                    });
                                    console.log('*** Session Saved')
                                    logTransactions(user_response.unique_identifier, phone_no)
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'enter_car_model', user_response)
                                    let menu = utils.parameterizedString(menu_data.menu, content.message.text)
                                    let path =  user_response.path + '*' + user_response.unique_identifier
                                    sendToWhatsApp(source_country, phone_no, menu, 'TEXT', 'enter_car_model', level, path )

                                }

                            })

                        }
                    } else if (user_response.unique_identifier === 'enter_car_model') {

                        // Simple validation for car model - should not be empty and should have reasonable length
                        let valid = content.message.text && content.message.text.trim().length > 1 && content.message.text.trim().length < 50
                        if (!valid) {
                            let validation_error_msg = 'Oops, that didn\'t work. Please enter a correct *car model*.'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)
                            let menu = utils.parameterizedString(menu_data.menu, client_name)

                            menu = validation_error_msg + '' + menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, validation_error_msg, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                                if (!err && ticket_no) {
                                    // Update vehicle record with model
                                    selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                        if (err) {
                                            console.log('*** Unique ID ignored')
                                        } else {
                                            motorService.updateVehicle(ticket_no, {
                                                model: content.message.text
                                            }, function(err, result) {
                                                if (err) {
                                                    console.log('*** Error updating vehicle: ', err);
                                                } else {
                                                    console.log('*** Updated vehicle with model', result);
                                                }
                                            });
                                        }
                                    });
                                    console.log('*** Session Saved')
                                    logTransactions(user_response.unique_identifier, phone_no)
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'enter_car_year_make', user_response)
                                    let menu = utils.parameterizedString(menu_data.menu, content.message.text)
                                    let path =  user_response.path + '*' + user_response.unique_identifier
                                    sendToWhatsApp(source_country, phone_no, menu, 'TEXT', 'enter_car_year_make', level, path )

                                }

                            })

                        }
                    } else if (user_response.unique_identifier === 'enter_car_year_make') {

                        // Validate year of manufacture - should be a 4-digit year
                        let valid = validateService.validateYear(content.message.text)
                        if (!valid) {
                            let validation_error_msg = 'Oops, that didn\'t work. Please enter a correct *year of manufacture* (e.g., 2020).'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)
                            let menu = utils.parameterizedString(menu_data.menu, client_name)

                            menu = validation_error_msg + '' + menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, validation_error_msg, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                                if (!err && ticket_no) {
                                    // Update vehicle record with year of manufacture
                                    selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                        if (err) {
                                            console.log('*** Unique ID ignored')
                                        } else {
                                            motorService.updateVehicle(ticket_no, {
                                                yearOfManufacture: content.message.text
                                            }, function(err, result) {
                                                if (err) {
                                                    console.log('*** Error updating vehicle: ', err);
                                                } else {
                                                    console.log('*** Updated vehicle with year of manufacture', result);
                                                }
                                            });
                                        }
                                    });
                                    console.log('*** Session Saved')
                                    logTransactions(user_response.unique_identifier, phone_no)
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'motor_cover_start_date', user_response)
                                    let menu = utils.parameterizedString(menu_data.menu, content.message.text)
                                    let path =  user_response.path + '*' + user_response.unique_identifier
                                    sendToWhatsApp(source_country, phone_no, menu, 'TEXT', 'motor_cover_start_date', level, path )

                                }

                            })

                        }
                    } else if (user_response.unique_identifier === 'motor_cover_start_date') {

                        // Validate date format dd/mm/yyyy
                        let valid = /^\d{2}\/\d{2}\/\d{4}$/.test(content.message.text)
                        if (!valid) {
                            let validation_error_msg = 'Oops, that didn\'t work. Please enter a correct *date* in DD/MM/YYYY format (e.g., 15/12/2024).'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)
                            let menu = utils.parameterizedString(menu_data.menu, client_name)

                            menu = validation_error_msg + '' + menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, validation_error_msg, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                                if (!err && ticket_no) {
                                    // Update vehicle record with cover start date
                                    selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                        if (err) {
                                            console.log('*** Unique ID ignored')
                                        } else {
                                            motorService.updateVehicle(ticket_no, {
                                                coverStartDate: content.message.text
                                            }, function(err, result) {
                                                if (err) {
                                                    console.log('*** Error updating vehicle: ', err);
                                                } else {
                                                    console.log('*** Updated vehicle with cover start date', result);
                                                }
                                            });
                                        }
                                    });
                                    console.log('*** Session Saved')
                                    logTransactions(user_response.unique_identifier, phone_no)
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'is_rare_model', user_response)
                                    let menu = utils.parameterizedString(menu_data.menu, content.message.text)
                                    let path =  user_response.path + '*' + user_response.unique_identifier
                                    sendToWhatsApp(source_country, phone_no, menu, 'SELECTION', 'is_rare_model', level, path )

                                }

                            })

                        }
                    } else if (user_response.unique_identifier === 'enter_car_value') {

                        // Validate car value - should be a number
                        let valid = !isNaN(content.message.text) && parseFloat(content.message.text) > 0
                        if (!valid) {
                            let validation_error_msg = 'Oops, that didn\'t work. Please enter a correct *car value* (e.g., 50000000).'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)
                            let menu = utils.parameterizedString(menu_data.menu, client_name)

                            menu = validation_error_msg + '' + menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, validation_error_msg, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                                if (!err && ticket_no) {
                                    // Update vehicle record with car value
                                    selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                        if (err) {
                                            console.log('*** Unique ID ignored')
                                        } else {
                                            motorService.updateVehicle(ticket_no, {
                                                valueOfCar: parseFloat(content.message.text)
                                            }, function(err, result) {
                                                if (err) {
                                                    console.log('*** Error updating vehicle: ', err);
                                                } else {
                                                    console.log('*** Updated vehicle with car value', result);
                                                }
                                            });
                                        }
                                    });
                                    console.log('*** Session Saved')
                                    logTransactions(user_response.unique_identifier, phone_no)
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'has_car_alarm', user_response)
                                    let menu = utils.parameterizedString(menu_data.menu, content.message.text)
                                    let path =  user_response.path + '*' + user_response.unique_identifier
                                    sendToWhatsApp(source_country, phone_no, menu, 'SELECTION', 'has_car_alarm', level, path )

                                }

                            })

                        }
                    } else if (unique_identifier.includes('success_end_message')) {
                        let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

                        user_response.path = user_response.path + '*end'
                        selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                            if (err) {} else {
                                selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                    if (err) {
                                        console.log('*** Unique ID ignored')
                                    } else {
                                        // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)
                                        user_response.path = user_response.path + '*' + unique_identifier
                                        menu = getTicketResponseMessage(user_response.whatsapp_name, ticket_no)
                                        sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                        endTicketSession(phone_no)

                                    }
                                })
                            }
                        })

                    } 
                    else if (user_response.unique_identifier === 'request_callback') {
                        console.log(`National ID ${content.message.text}`)
                        let valid = validateService.validateNameAndSurname(content.message.text)

                      
                        if (!valid) {
                            let validation_error_msg = 'Invalid Surname\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "Please enter your name and surname correctly.", function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.statusCode(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            var client_names = content.message.text.split(' ')
                            client_name = client_names[0]
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    console.log('*** Session Saved')
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'enter_mobile_number', user_response)
                                    let menu = utils.parameterizedString(menu_data.menu, client_name)
                                    let path =  user_response.path + '*' + user_response.unique_identifier
                                    sendToWhatsApp(source_country, phone_no, menu, 'TEXT', 'enter_mobile_number', level, path)
                                    logTransactions('enter_name_surname', phone_no)

                                }

                            })

                        }
                    }else if (user_response.unique_identifier === 'provide_details_health') {
                        console.log(`Reg Number ${content.message.text}`)
                        let valid =  true//validateService.validateString(content.message.text)
                        if (!valid) {
                            let validation_error_msg = 'Invalid Car Registration No\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.json(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'provide_details_health_photos', user_response)
                                    sendToWhatsApp(source_country, phone_no, menu_data.menu, 'IMAGE', 'provide_details_health_photos', level, user_response.path)

                                }

                            })
                        }

                    }else if (user_response.unique_identifier === 'provide_details_life') {
                        console.log(`Reg Number ${content.message.text}`)
                        let valid =  true//validateService.validateString(content.message.text)
                        if (!valid) {
                            let validation_error_msg = 'Invalid Car Registration No\n'
                            var level = user_response.level
                            unique_identifier = user_response.unique_identifier
                            menu_data = getMenu(level, unique_identifier, user_response)

                            menu = validation_error_msg + '' + menu_data.menu
                            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, menu, function(err, wa_response) {
                                if (err) {
                                    console.error(content.from + ' | wa_response: ' + err)
                                    res.json(false)
                                } else {
                                    try {
                                        res.json(wa_response)
                                    } catch (err) {

                                    }
                                }
                            })
                        } else {
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'provide_details_life_photos', user_response)
                                    sendToWhatsApp(source_country, phone_no, menu_data.menu, 'IMAGE', 'provide_details_life_photos', level, user_response.path)

                                }

                            })
                        }

                    }
                  
                    // else {
                    //     console.log(`Last IF ${content.message.text}`)
                    //     if (input_type === 'END') {
                    //         user_response.unique_identifier = 'success_end_message'
                    //     }
                    //     selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                    //         if (err) {
                    //             console.log('*** Unique ID ignored')
                    //         } else {
                    //             // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)
                    //             user_response.path = user_response.path + '*' + unique_identifier
                    //             sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)
                    //             if (input_type === 'END') {
                    //                 endTicketSession(phone_no)
                    //             }
                    //         }

                    //     })
                    // }

                }
            }

        })

    }

}



function validationPassed(source_country, level, unique_identifier, user_response, content) {
    let phone_no = content.from
    menu_data = getMenu(level, unique_identifier, user_response)
    let name = ""

    if ("contact" in content && level == 1) {
        content.contact.name = content.contact.name.replace(/([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        if (typeof content.contact.name == 'string') {
            name = content.contact.name
            if (source_country === 'tz')
                menu = 'Hi ' + name + ', we\'re happy to have you here 🙂. Welcome to UAP. I will be your virtual assistant.' +
                ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n' + menu_data.menu
            else {
                menu = 'Hi ' + name + ', we\'re happy to have you here :-). Welcome to UAP Old Mutual and Faulu Microfinance Bank. I will be your virtual assistant.' +
                    ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n' + menu_data.menu
            }
        } else
            menu = menu_data.menu

    } else
        menu = menu_data.menu


    if (level == '1') {
        
        selfservice.create_wa_user(phone_no, name, user_response.country, function(err, resp) {
            if (!err)
                console.log('----> WhatsAPP user created ' + resp)
        })
    }

    unique_identifier = menu_data.unique_identifier


    let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);
   
    if (input_type == 'END') {
        user_response.unique_identifier = 'success_end_message'
    }
    selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
        if (err) {
            console.log('*** Unique ID ignored' + err)
        } else {
            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, menu, function(err, wa_response) {
                if (err) {
                    console.error(content.from + ' | wa_response: ' + err)
                } else {
                    // let _level = user_response.level + 1

                    console.error(content.from + ' | wa_response: ' + err)

                    let params = {
                            phone_no: content.from,
                            level: level,
                            input: content.message.text,
                            unique_identifier: unique_identifier,
                            country: user_response.country,
                            country_id: user_response.country_id,
                            expected_input_type: input_type,
                            path: user_response.path + '*' + unique_identifier
                        }

                    if (shouldUpdateSession)
                        whatsappDBService.updateUserSession(params, function(err, session_response) {

                            if (err) {
                                console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                    // res.json(err)
                            } else {
                                try {
                                    res.json(wa_response)
                                } catch (e) {

                                }
                            }
                        })
                }
            })
            logTransactions(unique_identifier, phone_no)
        }

    })
}

function endTicketSession(phone_no) {
    console.log(`endTicketSession ${phone_no}`)
    selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
        if (err) {

        } else {
            selfservice.close_user_WhatsApp_Session(phone_no, ticket_no, function(err, close_sess_response) {
                if (err) {

                } else {
                    console.log('Ticket to closed : ' + ticket_no + ' Message: ' + close_sess_response)
                }

            })
        }

    })
}


function handleUserImages(req, _res, source_country = 'ke') {
    res = _res;
    let content = req.body.results[0];
    let unique_identifier
    let phone_no = content.from

    console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: IMAGE ' + content.messageId)


    whatsappDBService.getUserLevel(phone_no, function(err, user_response) {
        if (err) {
            initialWelcome(salutation_response[0], content, source_country)
        } else {
            unique_identifier = getCurrentUniqueIdentifier(1, user_response)
            console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: IMAGE level: ' + user_response.level + ' new unique_identifier ' + unique_identifier + ' old unique_identifier ' + user_response.unique_identifier + ' input ' + content.message.url + ' expected_input_type ' + user_response.expected_input_type + ' Msg Id: ' + content.messageId)

            if (content.message.text == '0' || unique_identifier == 'back_to_main') {
                level = 1
                unique_identifier = user_response.country
                user_response.path = 'start'
            } else
                level = user_response.level

            //let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

            console.log(user_response.unique_identifier)


            console.log("before download")
            console.log(`user_response ${JSON.stringify(user_response)}`)
            

            selfservice.getSessionTicketNo(phone_no, async function(err, response) {

                await selfservice.download_reg_media(content.message.url, phone_no, response, content.message.caption, source_country, function(err, resp) {
                    if (err) {
                        console.error(utils.getDateTime() + ' | ' + phone_no + 'Error downloading HW registration images ' + err)
                        sendErrorMessage(phone_no, _res)
                    } else {
                        console.log('*** Identification media saved')
                        console.log(user_response.unique_identifier)
                        if (user_response.unique_identifier === 'provide_details_health_photos' || user_response.unique_identifier === 'more_photos'|| user_response.unique_identifier ==='provide_details_car_photos' || user_response.unique_identifier ==='provide_details_life_photos') {
                            const reply_options = [
                                {
                                    "type": "REPLY",
                                    "id": "1",
                                    "title": "Yes, please"
                                },
                                {
                                    "type": "REPLY",
                                    "id": "2",
                                    "title": "No, thank you"
                                }
                            ]
                            var level = 6
                            menu_data = getMenu(level, 'more_photos', user_response)
                            sendReplyTextToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', 'more_photos', level, user_response.path, reply_options)
                            
                            
                        } else if(user_response.unique_identifier === 'provide_details_photos_end' || user_response.unique_identifier === 'provide_details_health_photos')  {
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    console.log('***handling reg photos')
                                }
                            })

                        }else {
                            res.json(true)
                        }
                    }
                })
            }
            )




           

        }
    })

}

function handleUserVideos(req, _res) {
    console.log('*** Unique ID ignored')
    let content = req.body.results[0];
    console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: VIDEO TYPE UPLOADED Msg Id ' + content.messageId)

    let menu = 'Please upload a photo or a document.'
    whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, menu, function(err, wa_response) {
        if (err) {
            _res.json(false)
        } else {
            _res.json(menu)
        }
    })
}

function handleUserDocuments(req, _res) {
    handleUserImages(req, _res)

}
async function handleButtonEvent(req, _res, source_country = 'ug'){
    console.log('*** Processed inside handleButtonevent')
    res = _res;
    let content = req.body.results[0];
    let unique_identifier
    let phone_no = content.from

    console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: IMAGE ' + content.message.id)

    whatsappDBService.getUserLevel(phone_no, function(err, user_response) {
        if (err) {
            initialWelcome(salutation_response[0], content, source_country)
        } else {

            level = user_response.level + 1
            unique_identifier = getCurrentUniqueIdentifier(content.message.id, user_response)
            console.log(`unique_identifier ${unique_identifier}`)
            if (typeof unique_identifier == 'undefined') {
                console.log('----> Unique undefined ' + content.message.text)
                level = user_response.level
                unique_identifier = user_response.unique_identifier
            }
            // if (!shouldUpdateSession)
            //     level = user_response.level

            menu_data = getMenu(level, unique_identifier, user_response)
        
            if (content.message.text == '0' || unique_identifier == 'back_to_main') {
                level = 1
                unique_identifier = user_response.country
                user_response.path = 'start'
            } else
                level = user_response.level + 1
        
            let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);
        
            
        
        
          
        
            if(unique_identifier == 'health_insurance' || unique_identifier == 'motor_insurance' || unique_identifier == 'life_insurance'){
                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                    if (err) {
                        console.log('*** Unique ID ignored' + err)
                    } else {
    
                        const reply_options = [
                            {
                                "type": "REPLY",
                                "id": "1",
                                "title": "Please call me"
                            }
                        ]
                         
                        whatsAppMessageService.sendWhatsReplyButtons('rw', content.from, menu_data.menu,reply_options, function(err, wa_response) {
                            if (err) {
                                console.error(content.from + ' | wa_response: ' + err)
                            } else {
                                // let _level = user_response.level + 1
            
                                console.error(content.from + ' | wa_response: ' + err)
            
                                let params = {
                                        phone_no: content.from,
                                        level: level,
                                        input: content.message.id,
                                        unique_identifier: unique_identifier,
                                        country: user_response.country,
                                        country_id: user_response.country_id,
                                        expected_input_type: input_type,
                                        path: user_response.path + '*' + unique_identifier
                                    }
            
                                if (shouldUpdateSession)
                                    whatsappDBService.updateUserSession(params, function(err, session_response) {
            
                                        if (err) {
                                            console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                // res.json(err)
                                        } else {
                                            try {
                                                res.json(wa_response)
                                            } catch (e) {
            
                                            }
                                        }
                                    })
                            }
                        })
                        logTransactions(unique_identifier, phone_no)
                    }
            
                })
            }   
            else if(unique_identifier == 'enter_name_surname'){
                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                    if (err) {
                        console.log('*** Unique ID ignored' + err)
                    } else {
    
                        
                        whatsAppMessageService.sendWhatsAppMessageAPI('rw', content.from, menu_data.menu, function(err, wa_response) {
                            if (err) {
                                console.error(content.from + ' | wa_response: ' + err)
                            } else {
                                // let _level = user_response.level + 1
            
                                console.error(content.from + ' | wa_response: ' + err)
            
                                let params = {
                                        phone_no: content.from,
                                        level: level,
                                        input: content.message.id,
                                        unique_identifier: unique_identifier,
                                        country: user_response.country,
                                        country_id: user_response.country_id,
                                        expected_input_type: input_type,
                                        path: user_response.path + '*' + unique_identifier
                                    }
            
                                if (shouldUpdateSession)
                                    whatsappDBService.updateUserSession(params, function(err, session_response) {
            
                                        if (err) {
                                            console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                // res.json(err)
                                        } else {
                                            try {
                                                res.json(wa_response)
                                            } catch (e) {
            
                                            }
                                        }
                                    })
                            }
                        })
                        logTransactions('request_callback', phone_no)
                    }
            
                })
            }
            else if(unique_identifier == 'done'){
                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type,'done', function(err, resp) {
                    if (err) {
                        console.log('*** Unique ID ignored' + err)
                    } else {
    
                        menu = utils.parameterizedString(menu_data.menu, client_name)
                        whatsAppMessageService.sendWhatsAppMessageAPI('rw', content.from, menu, function(err, wa_response) {
                            if (err) {
                                console.error(content.from + ' | wa_response: ' + err)
                            } else {
                                // let _level = user_response.level + 1
            
                                console.error(content.from + ' | wa_response: ' + err)
            
                                let params = {
                                        phone_no: content.from,
                                        level: level,
                                        input: content.message.id,
                                        unique_identifier: unique_identifier,
                                        country: user_response.country,
                                        country_id: user_response.country_id,
                                        expected_input_type: input_type,
                                        path: user_response.path + '*' + unique_identifier
                                    }
            
                                if (shouldUpdateSession)
                                    whatsappDBService.updateUserSession(params, function(err, session_response) {
            
                                        if (err) {
                                            console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                // res.json(err)
                                        } else {
                                            try {
                                                res.json(wa_response)
                                            } catch (e) {
            
                                            }
                                        }
                                    })
                            }
                        })
                        logTransactions(unique_identifier, phone_no)
                    }
            
                })
            } 
            else if(unique_identifier == 'continue'){
                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, 'continue', async function(err, resp) {
                    if (err) {
                        console.log('*** Unique ID ignored' + err)
                    } else {

                        level = 1
                        unique_identifier = user_response.country
                        menu_data = getMenu(1,'uganda', user_response)
                     
                        const sleep = promisify(setTimeout)
                   
                        
                        await sleep(1e3)
                        whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, "What would you like to do?", function(err, wa_response) {
                                if (err) {
                                    console.log(content.from + ' | wa_response: ' + err)
                                    res.json(err)
            
                                    } else {
                                    // if (shouldUpdateSession)
                                    
                        }})
                        await sleep(1e3)
                        whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, menu_data.menu, function(err, wa_response) {
                            if (err) {
                                console.log(content.from + ' | wa_response: ' + err)
                                res.json(err)
        
                            } else {
                                // if (shouldUpdateSession)
                                whatsappDBService.updateUserSession({
                                    phone_no: content.from,
                                    level: 1,
                                    input: content.message.text,
                                    unique_identifier: unique_identifier,
                                    expected_input_type: 'SELECTION',
                                    path:'continue'
                                }, function(err, session_response) {
                                    if (err) {
                                        console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                            // res.json(err)
                                    } else {
                                        try {
                                            res.json(wa_response)
                                        } catch (e) {
        
                                        }
                                    }
                                })
                            }
                        })
                        logTransactions('Continue', phone_no)
                    }
            
                })
            } 
            else if(unique_identifier == 'complete'){
                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                    if (err) {
                        console.log('*** Unique ID ignored' + err)
                    } else {
    
                        
                        whatsAppMessageService.sendWhatsAppMessageAPI('rw', content.from, menu_data.menu, function(err, wa_response) {
                            if (err) {
                                console.error(content.from + ' | wa_response: ' + err)
                            } else {
                                // let _level = user_response.level + 1
            
                                console.error(content.from + ' | wa_response: ' + err)
            
                                let params = {
                                        phone_no: content.from,
                                        level: level,
                                        input: content.message.id,
                                        unique_identifier: unique_identifier,
                                        country: user_response.country,
                                        country_id: user_response.country_id,
                                        expected_input_type: input_type,
                                        path: user_response.path + '*' + unique_identifier
                                    }
            
                                if (shouldUpdateSession)
                                    whatsappDBService.updateUserSession(params, function(err, session_response) {
            
                                        if (err) {
                                            console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                // res.json(err)
                                        } else {
                                            try {
                                                res.json(wa_response)
                                            } catch (e) {
            
                                            }
                                        }
                                    })
                            }
                        })
                        logTransactions(unique_identifier, phone_no)
                    }
            
                })
            }
            else if(unique_identifier == 'is_rare_model'){
                selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                    if (!err && ticket_no) {
                        selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.id, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                            if (err) {
                                console.log('*** Unique ID ignored' + err)
                            } else {
                                // Update vehicle with rare model status
                                let isRareModel = content.message.id === '1' // 1 = Yes, 2 = No
                                motorService.updateVehicle(ticket_no, {
                                    isRareModel: isRareModel
                                }, function(err, result) {
                                    if (err) {
                                        console.log('*** Error updating vehicle: ', err);
                                    } else {
                                        console.log('*** Updated vehicle with rare model status', result);
                                    }
                                });

                                var level = user_response.level + 1
                                menu_data = getMenu(level, 'is_valuated', user_response)
                                let path = user_response.path + '*' + user_response.unique_identifier
                                sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', 'is_valuated', level, path)
                                logTransactions(user_response.unique_identifier, phone_no)
                            }
                        });
                    }
                });
            }
            else if(unique_identifier == 'is_valuated'){
                selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                    if (!err && ticket_no) {
                        selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.id, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                            if (err) {
                                console.log('*** Unique ID ignored' + err)
                            } else {
                                // Update vehicle with valuation status
                                let valuationDone = content.message.id === '1' // 1 = Yes, 2 = No
                                motorService.updateVehicle(ticket_no, {
                                    valuationDone: valuationDone
                                }, function(err, result) {
                                    if (err) {
                                        console.log('*** Error updating vehicle: ', err);
                                    } else {
                                        console.log('*** Updated vehicle with valuation status', result);
                                    }
                                });

                                var level = user_response.level + 1
                                menu_data = getMenu(level, 'enter_car_value', user_response)
                                let path = user_response.path + '*' + user_response.unique_identifier
                                sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'enter_car_value', level, path)
                                logTransactions(user_response.unique_identifier, phone_no)
                            }
                        });
                    }
                });
            }
            else if(unique_identifier == 'has_car_alarm'){
                selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                    if (!err && ticket_no) {
                        selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.id, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                            if (err) {
                                console.log('*** Unique ID ignored' + err)
                            } else {
                                // Update vehicle with alarm status
                                let alarmInstalled = content.message.id === '1' // 1 = Yes, 2 = No
                                motorService.updateVehicle(ticket_no, {
                                    alarmInstalled: alarmInstalled
                                }, function(err, result) {
                                    if (err) {
                                        console.log('*** Error updating vehicle: ', err);
                                    } else {
                                        console.log('*** Updated vehicle with alarm status', result);
                                    }
                                });

                                var level = user_response.level + 1
                                menu_data = getMenu(level, 'has_car_tracker', user_response)
                                let path = user_response.path + '*' + user_response.unique_identifier
                                sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', 'has_car_tracker', level, path)
                                logTransactions(user_response.unique_identifier, phone_no)
                            }
                        });
                    }
                });
            }
            else if(unique_identifier == 'has_car_tracker'){
                selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                    if (!err && ticket_no) {
                        selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.id, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                            if (err) {
                                console.log('*** Unique ID ignored' + err)
                            } else {
                                // Update vehicle with tracker status
                                let trackerInstalled = content.message.id === '1' // 1 = Yes, 2 = No
                                motorService.updateVehicle(ticket_no, {
                                    trackerInstalled: trackerInstalled
                                }, function(err, result) {
                                    if (err) {
                                        console.log('*** Error updating vehicle: ', err);
                                    } else {
                                        console.log('*** Updated vehicle with tracker status', result);
                                    }
                                });

                                var level = user_response.level + 1
                                menu_data = getMenu(level, 'is_car_withinEA', user_response)
                                let path = user_response.path + '*' + user_response.unique_identifier
                                sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', 'is_car_withinEA', level, path)
                                logTransactions(user_response.unique_identifier, phone_no)
                            }
                        });
                    }
                });
            }
            else if(unique_identifier == 'is_car_withinEA'){
                selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                    if (!err && ticket_no) {
                        selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.id, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                            if (err) {
                                console.log('*** Unique ID ignored' + err)
                            } else {
                                // Update vehicle with within EA status
                                let withinEA = content.message.id === '1' // 1 = Yes, 2 = No
                                motorService.updateVehicle(ticket_no, {
                                    withinEA: withinEA
                                }, function(err, result) {
                                    if (err) {
                                        console.log('*** Error updating vehicle: ', err);
                                    } else {
                                        console.log('*** Updated vehicle with within EA status', result);
                                    }
                                });

                                var level = user_response.level + 1
                                menu_data = getMenu(level, 'motor_years_license', user_response)
                                let path = user_response.path + '*' + user_response.unique_identifier
                                sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', 'motor_years_license', level, path)
                                logTransactions(user_response.unique_identifier, phone_no)
                            }
                        });
                    }
                });
            }
            else if(unique_identifier == 'motor_years_license'){
                selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                    if (!err && ticket_no) {
                        selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.id, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                            if (err) {
                                console.log('*** Unique ID ignored' + err)
                            } else {
                                // Update vehicle with years with license status (more than 3 years = true)
                                let yearsWithLicense = content.message.id === '1' // 1 = More than 3 years, 2 = Less than 3 years
                                motorService.updateVehicle(ticket_no, {
                                    yearsWithLicense: yearsWithLicense
                                }, function(err, result) {
                                    if (err) {
                                        console.log('*** Error updating vehicle: ', err);
                                    } else {
                                        console.log('*** Updated vehicle with years with license status', result);
                                    }
                                });

                                var level = user_response.level + 1
                                menu_data = getMenu(level, 'motor_first_registration', user_response)
                                let path = user_response.path + '*' + user_response.unique_identifier
                                sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', 'motor_first_registration', level, path)
                                logTransactions(user_response.unique_identifier, phone_no)
                            }
                        });
                    }
                });
            }
            else if(unique_identifier == 'motor_first_registration'){
                selfservice.getSessionTicketNo(phone_no, function(err, ticket_no) {
                    if (!err && ticket_no) {
                        selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.id, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                            if (err) {
                                console.log('*** Unique ID ignored' + err)
                            } else {
                                // Update vehicle with first application status
                                let firstApplication = content.message.id === '1' // 1 = Yes, 2 = No
                                motorService.updateVehicle(ticket_no, {
                                    firstApplication: firstApplication
                                }, function(err, result) {
                                    if (err) {
                                        console.log('*** Error updating vehicle: ', err);
                                    } else {
                                        console.log('*** Updated vehicle with first application status', result);
                                    }
                                });

                                var level = user_response.level + 1
                                menu_data = getMenu(level, 'get_motor_quote', user_response)
                                let path = user_response.path + '*' + user_response.unique_identifier
                                sendToWhatsApp(source_country, phone_no, menu_data.menu, 'END', 'get_motor_quote', level, path)
                                logTransactions(user_response.unique_identifier, phone_no)

                                // Generate PDF quote
                                console.log('PDF is generated for ticket:', ticket_no)
                            }
                        });
                    }
                });
            }
            else if(unique_identifier == 'claim_car' ){
                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                    if (err) {
                        console.log('*** Unique ID ignored' + err)
                    } else {
    
                        const reply_options = [
                            {
                                "type": "REPLY",
                                "id": "1",
                                "title": "Submit my details"
                            },
                            {
                                "type": "REPLY",
                                "id": "2",
                                "title": "Please call me"
                            }
                        ]
                         
                        whatsAppMessageService.sendWhatsReplyButtons('rw', content.from, menu_data.menu,reply_options, function(err, wa_response) {
                            if (err) {
                                console.error(content.from + ' | wa_response: ' + err)
                            } else {
                                // let _level = user_response.level + 1
            
                                console.error(content.from + ' | wa_response: ' + err)
            
                                let params = {
                                        phone_no: content.from,
                                        level: level,
                                        input: content.message.id,
                                        unique_identifier: unique_identifier,
                                        country: user_response.country,
                                        country_id: user_response.country_id,
                                        expected_input_type: input_type,
                                        path: user_response.path + '*' + unique_identifier
                                    }
            
                                if (shouldUpdateSession)
                                    whatsappDBService.updateUserSession(params, function(err, session_response) {
            
                                        if (err) {
                                            console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                // res.json(err)
                                        } else {
                                            try {
                                                res.json(wa_response)
                                            } catch (e) {
            
                                            }
                                        }
                                    })
                            }
                        })
                        logTransactions(unique_identifier, phone_no)
                    }
            
                })
            } 
            else if(unique_identifier == 'claim_health' || unique_identifier == 'claim_life' ){
                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                    if (err) {
                        console.log('*** Unique ID ignored' + err)
                    } else {
    
                        const reply_options = [
                            {
                                "type": "REPLY",
                                "id": "1",
                                "title": "Submit my details"
                            },
                            {
                                "type": "REPLY",
                                "id": "2",
                                "title": "Please call me"
                            }
                        ]
                         
                        whatsAppMessageService.sendWhatsReplyButtons('rw', content.from, menu_data.menu,reply_options, function(err, wa_response) {
                            if (err) {
                                console.error(content.from + ' | wa_response: ' + err)
                            } else {
                                // let _level = user_response.level + 1
            
                                console.error(content.from + ' | wa_response: ' + err)
            
                                let params = {
                                        phone_no: content.from,
                                        level: level,
                                        input: content.message.id,
                                        unique_identifier: unique_identifier,
                                        country: user_response.country,
                                        country_id: user_response.country_id,
                                        expected_input_type: input_type,
                                        path: user_response.path + '*' + unique_identifier
                                    }
            
                                if (shouldUpdateSession)
                                    whatsappDBService.updateUserSession(params, function(err, session_response) {
            
                                        if (err) {
                                            console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                // res.json(err)
                                        } else {
                                            try {
                                                res.json(wa_response)
                                            } catch (e) {
            
                                            }
                                        }
                                    })
                            }
                        })
                        logTransactions(unique_identifier, phone_no)
                    }
            
                })
            } 
            else if(unique_identifier == 'request_callback' ){
                console.log(`National ID ${content.message.text}`)
                let valid =  true// validateService.validateNameAndSurname(content.message.text)

              
                if (!valid) {
                    let validation_error_msg = 'Invalid Name\n'
                    var level = user_response.level
                    unique_identifier = user_response.unique_identifier
                    menu_data = getMenu(level, unique_identifier, user_response)

                    menu = validation_error_msg + '' + menu_data.menu
                    whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, menu, function(err, wa_response) {
                        if (err) {
                            console.error(content.from + ' | wa_response: ' + err)
                            res.statusCode(false)
                        } else {
                            try {
                                res.json(wa_response)
                            } catch (err) {

                            }
                        }
                    })
                } else {
                    selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                    if (err) {
                        console.log('*** Unique ID ignored' + err)
                    } else {
    
                        //var client_names = content.message.text.split(' ')
                        //client_name = client_names[0]
                        let menu = utils.parameterizedString(menu_data.menu, content.message.text) 
                        whatsAppMessageService.sendWhatsAppMessageAPI('rw', content.from, menu, function(err, wa_response) {
                            if (err) {
                                console.error(content.from + ' | wa_response: ' + err)
                            } else {
                                // let _level = user_response.level + 1
            
                                console.error(content.from + ' | wa_response: ' + err)
            
                                let params = {
                                        phone_no: content.from,
                                        level: level,
                                        input: content.message.id,
                                        unique_identifier: unique_identifier,
                                        country: user_response.country,
                                        country_id: user_response.country_id,
                                        expected_input_type: input_type,
                                        path: user_response.path + '*' + unique_identifier
                                    }
            
                                if (shouldUpdateSession)
                                    whatsappDBService.updateUserSession(params, function(err, session_response) {
            
                                        if (err) {
                                            console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                // res.json(err)
                                        } else {
                                            try {
                                                res.json(wa_response)
                                            } catch (e) {
            
                                            }
                                        }
                                    })
                            }
                        })
                        logTransactions(unique_identifier, phone_no)
                        
                    }
            
                })}
            }
            else if(unique_identifier == 'provide_details_car'){
                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                    if (err) {
                        console.log('*** Unique ID ignored' + err)
                    } else {
    
                        //var client_names = content.message.text.split(' ')
                        client_name = content.message.text
                        let menu = utils.parameterizedString(menu_data.menu, client_name) 
                        whatsAppMessageService.sendWhatsAppMessageAPI('ug', content.from, menu, function(err, wa_response) {
                            if (err) {
                                console.error(content.from + ' | wa_response: ' + err)
                            } else {
                                // let _level = user_response.level + 1
            
                                console.error(content.from + ' | wa_response: ' + err)
            
                                let params = {
                                        phone_no: content.from,
                                        level: level,
                                        input: content.message.id,
                                        unique_identifier: unique_identifier,
                                        country: user_response.country,
                                        country_id: user_response.country_id,
                                        expected_input_type: input_type,
                                        path: user_response.path + '*' + unique_identifier
                                    }
            
                                if (shouldUpdateSession)
                                    whatsappDBService.updateUserSession(params, function(err, session_response) {
            
                                        if (err) {
                                            console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                // res.json(err)
                                        } else {
                                            try {
                                                res.json(wa_response)
                                            } catch (e) {
            
                                            }
                                        }
                                    })
                            }
                        })
                        logTransactions(unique_identifier, phone_no)
                    }
            
                })
            }else if(unique_identifier == 'provide_details_health'){
                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                    if (err) {
                        console.log('*** Unique ID ignored' + err)
                    } else {
    
                        //var client_names = content.message.text.split(' ')
                        client_name = content.message.text
                        let menu = utils.parameterizedString(menu_data.menu, client_name) 
                        whatsAppMessageService.sendWhatsAppMessageAPI('ug', content.from, menu, function(err, wa_response) {
                            if (err) {
                                console.error(content.from + ' | wa_response: ' + err)
                            } else {
                                // let _level = user_response.level + 1
            
                                console.error(content.from + ' | wa_response: ' + err)
            
                                let params = {
                                        phone_no: content.from,
                                        level: level,
                                        input: content.message.id,
                                        unique_identifier: unique_identifier,
                                        country: user_response.country,
                                        country_id: user_response.country_id,
                                        expected_input_type: input_type,
                                        path: user_response.path + '*' + unique_identifier
                                    }
            
                                if (shouldUpdateSession)
                                    whatsappDBService.updateUserSession(params, function(err, session_response) {
            
                                        if (err) {
                                            console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                // res.json(err)
                                        } else {
                                            try {
                                                res.json(wa_response)
                                            } catch (e) {
            
                                            }
                                        }
                                    })
                            }
                        })
                        logTransactions(unique_identifier, phone_no)
                    }
            
                })
            }else if(unique_identifier == 'provide_details_life' ){
                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                    if (err) {
                        console.log('*** Unique ID ignored' + err)
                    } else {
    
                        //var client_names = content.message.text.split(' ')
                        client_name = content.message.text
                        let menu = utils.parameterizedString(menu_data.menu, client_name) 
                        whatsAppMessageService.sendWhatsAppMessageAPI('ug', content.from, menu, function(err, wa_response) {
                            if (err) {
                                console.error(content.from + ' | wa_response: ' + err)
                            } else {
                                // let _level = user_response.level + 1
            
                                console.error(content.from + ' | wa_response: ' + err)
            
                                let params = {
                                        phone_no: content.from,
                                        level: level,
                                        input: content.message.id,
                                        unique_identifier: unique_identifier,
                                        country: user_response.country,
                                        country_id: user_response.country_id,
                                        expected_input_type: input_type,
                                        path: user_response.path + '*' + unique_identifier
                                    }
            
                                if (shouldUpdateSession)
                                    whatsappDBService.updateUserSession(params, function(err, session_response) {
            
                                        if (err) {
                                            console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                // res.json(err)
                                        } else {
                                            try {
                                                res.json(wa_response)
                                            } catch (e) {
            
                                            }
                                        }
                                    })
                            }
                        })
                        logTransactions(unique_identifier, phone_no)
                    }
            
                })
            } else if(unique_identifier == 'more_photos' ){
                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                    if (err) {
                        console.log('*** Unique ID ignored' + err)
                    } else {
    
                        level = level -1
                        user_response.level = user_response.level -1
                        whatsAppMessageService.sendWhatsAppMessageAPI('rw', content.from, menu_data.menu, function(err, wa_response) {
                            if (err) {
                                console.error(content.from + ' | wa_response: ' + err)
                            } else {
                                // let _level = user_response.level + 1
            
                                console.error(content.from + ' | wa_response: ' + err)
            
                                let params = {
                                        phone_no: content.from,
                                        level: level,
                                        input: content.message.id,
                                        unique_identifier: unique_identifier,
                                        country: user_response.country,
                                        country_id: user_response.country_id,
                                        expected_input_type: input_type,
                                        path: user_response.path + '*' + unique_identifier
                                    }
            
                                if (shouldUpdateSession)
                                    whatsappDBService.updateUserSession(params, function(err, session_response) {
            
                                        if (err) {
                                            console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                // res.json(err)
                                        } else {
                                            try {
                                                res.json(wa_response)
                                            } catch (e) {
            
                                            }
                                        }
                                    })
                            }
                        })
                        logTransactions(unique_identifier, phone_no)
                    }
            
                })
            } else if(unique_identifier == 'done_uploading' ){
                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                    if (err) {
                        console.log('*** Unique ID ignored' + err)
                    } else {
    
                        const reply_options = [
                            {
                                "type": "REPLY",
                                "id": "1",
                                "title": "Back to main menu"
                            },
                            {
                                "type": "REPLY",
                                "id": "2",
                                "title": "I'm done, for now"
                            }
                        ]
                        
                        whatsAppMessageService.sendWhatsReplyButtons('rw', content.from, menu_data.menu, reply_options,function(err, wa_response) {
                            if (err) {
                                console.error(content.from + ' | wa_response: ' + err)
                            } else {
                                // let _level = user_response.level + 1
            
                                console.error(content.from + ' | wa_response: ' + err)
            
                                let params = {
                                        phone_no: content.from,
                                        level: level,
                                        input: content.message.id,
                                        unique_identifier: unique_identifier,
                                        country: user_response.country,
                                        country_id: user_response.country_id,
                                        expected_input_type: input_type,
                                        path: user_response.path + '*' + unique_identifier
                                    }
            
                                if (shouldUpdateSession)
                                    whatsappDBService.updateUserSession(params, function(err, session_response) {
            
                                        if (err) {
                                            console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                // res.json(err)
                                        } else {
                                            try {
                                                res.json(wa_response)
                                            } catch (e) {
            
                                            }
                                        }
                                    })
                            }
                        })
                        logTransactions(unique_identifier, phone_no)
                        formulateClaimsData(phone_no, user_response.whatsapp_name)
                        endTicketSession(phone_no)
                    }
            
                })
            }

        }})


    
}

function handleUserAudio() {

}

function goToMainMenu() {


}

function getMenu(level, unique_identifier, user_response) {

    console.log('--> 1 CHECK MENU  level: ' + level + ' Unique: ' + unique_identifier + ' Prev input: ' + user_response.input)
    console.log('User data: ' + JSON.stringify(user_response))
    let _menu = whatsAppMenuService.getMenu(level, unique_identifier, user_response)
    if (typeof _menu === 'undefined') {
        let _level = level - 1;
        console.log(utils.getDateTime() + " | " + user_response.phone_no + 'MENU: getMenu() not found  level: ' + _level + ' Unique: ' + unique_identifier + ' Prev input: ' + user_response.input)
        shouldUpdateSession = false;
        unique_identifier = whatsAppMenuService.getMenuByIndex(user_response.level, user_response.unique_identifier, user_response.input, user_response)
        console.log('--> Previous uniq %% ' + unique_identifier)
        _menu = whatsAppMenuService.getMenu(level, unique_identifier, user_response)
        return { menu: _menu, unique_identifier: unique_identifier };
    } else {
        console.log('--> Menu Found  level: ' + level + ' Unique: ' + unique_identifier + ' Prev input: ' + user_response.input)
        shouldUpdateSession = true;
        return { menu: _menu, unique_identifier: unique_identifier };
    }
}

function getCurrentUniqueIdentifier(input, user_response) {


    let _unique_identifier, level;
    _unique_identifier = whatsAppMenuService.getMenuByIndex(user_response.level, user_response.unique_identifier, input, user_response)


    if (typeof _unique_identifier === 'undefined') {
        shouldUpdateSession = false
        console.log(utils.getDateTime() + " | " + user_response.phone_no + 'MENU: getCurrentUniqueIdentifier() unique_identifier not Found  level: ')

        if (user_response.level == 1) {
            return whatsAppMenuService.getMenuByIndex(0, 'start', user_response.country_id, user_response)
        } else {

            return user_response.unique_identifier
        }

    } else {

        if (user_response.level == 1)
            shouldUpdateSession = true;
        return _unique_identifier;
    }
}


const saveUserDetails = async(salutation, content, source_country) => {
    console.log('saveUserDetails')
    let name = ""
    if ("contact" in content) {
        content.contact.name = content.contact.name.replace(/([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        if (typeof content.contact.name == 'string') {
            salutation = salutation + " " + content.contact.name
            name = content.contact.name
        }

    }
    whatsappDBService.updateUserSession({
        phone_no: content.from,
        whatsapp_name: name,
        level: 0,
        input: content.message.text,
        unique_identifier: 'start',
        expected_input_type: 'SELECTION',
        country: 'uganda',
        country_id: 2,
        path: 'start'
    }, function(err, msg) {
        if (err) {
            console.log(`updateUserSession err ${err}`)
        }
        console.log(`updateUserSession ${msg}`)
    })
}

function initialWelcome(salutation, content, source_country) {
    let name = ""
    if ("contact" in content) {
        content.contact.name = content.contact.name.replace(/([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        if (typeof content.contact.name == 'string') {
            salutation = salutation + " " + content.contact.name
            name = content.contact.name
        }
    }
    
    // Simplified welcome message for Uganda only
    let salute = salutation + ', we\'re happy to have you here 🙂. Welcome to Old Mutual Uganda. I will be your virtual assistant.' +
        ' By proceeding to the next step you\'re agreeing to our terms and conditions found here: https://www.uapoldmutual.co.ug/privacy-policy/ \n\n'

    whatsappDBService.updateUserSession({
        phone_no: content.from,
        whatsapp_name: name,
        level: 0,
        input: content.message.text,
        unique_identifier: 'start',
        expected_input_type: 'SELECTION',
        country: 'uganda',  // Set country to uganda directly
        country_id: 2,      // Set country_id to 2 (for Uganda)
        path: 'start'
    }, function(err, msg) {
        if (err) {
            console.log('Error updating user session:', err);
        } else {
            // Get the Uganda-specific menu for level 1
            salute = salute + '\n' + whatsAppMenuService.getMenu(1, 'uganda', { country: 'uganda' })
            
            whatsAppMessageService.sendWhatsAppMessageAPI(source_country, content.from, salute, function(err, wa_response) {
                if (err) {
                    console.error(content.from + ' | wa_response: ' + err)
                } else {
                    try {
                        res.json(wa_response)
                    } catch (e) {
                        console.log('Error sending JSON response:', e);
                    }
                }
            })
        }
    });
}
function sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, path) {
    whatsAppMessageService.sendWhatsAppMessageAPI(source_country, phone_no, menu, function(err, wa_response) {
        if (err) {
            console.error(phone_no + ' | wa_response: ' + err)
            try {
                res.json(false)
            } catch (e) {

            }
        } else {
            // if (shouldUpdateSession)

            whatsappDBService.updateUserSession({
                phone_no: phone_no,
                expected_input_type: input_type,
                unique_identifier: unique_identifier,
                level: level,
                path: path
            }, function(err, session_response) {

                if (err) {
                    console.error(phone_no + ' | Existing Error updating user session : ' + session_response)
                        // res.json(err)
                } else {
                   
                    try {
                        res.json(true)
                    } catch (e) {

                    }
                }
            })
        }
    })
    //logTransactions(unique_identifier, phone_no)
}

function sendReplyTextToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, path, reply_options) {
    whatsAppMessageService.sendWhatsReplyButtons(source_country, phone_no, menu, reply_options, function(err, wa_response) {
        if (err) {
            console.error(phone_no + ' | wa_response: ' + err)
            try {
                res.json(false)
            } catch (e) {

            }
        } else {
            // if (shouldUpdateSession)

            whatsappDBService.updateUserSession({
                phone_no: phone_no,
                expected_input_type: input_type,
                unique_identifier: unique_identifier,
                level: level,
                path: path
            }, function(err, session_response) {

                if (err) {
                    console.error(phone_no + ' | Existing Error updating user session : ' + session_response)
                        // res.json(err)
                } else {
                   
                    try {
                        res.json(true)
                    } catch (e) {

                    }
                }
            })
        }
    })
    //logTransactions(unique_identifier, phone_no)
}

function getTicketResponseMessage(name, ticket_no, unique_identifier = null) {

    if (unique_identifier !== null && unique_identifier == 'bank_with_us_option_success_end_message')
        return 'Hi ' + name + ', Thank you for contacting us. Your ticket number is ' + ticket_no + '. You will be contacted by our call centre team shortly. ' +
            'To view our online portal, click here: https://www.faulukenya.com/ and to view our T&Cs click here: https://bit.ly/3gF1AnI\n\n0. Main Menu'
    else if (unique_identifier !== null && unique_identifier === 'provide_details_car_photos')
        return 'Hi ' + name + ', Thank you for contacting us. Your ticket number is ' + ticket_no + '. You will be contacted by our call centre team shortly. ' +
            'To view our online portal, click here: ' + site + ' \n\n0. Main Menu'

    else
        return 'Hi ' + name + ', Thank you for contacting us. Your ticket number is ' + ticket_no + '. You will be contacted by our call centre team shortly. ' +
            'To view our online portal, click here: ' + site + ' \n\n0. Main Menu'

}


function updateCountry(params) {
    console.log(`params ${params}`)
    whatsappDBService.updateCountry(params, function(err, msg) {
        if (err) {
            console.log('Error updating country')
        } else {
            console.log('Country updated: ' + JSON.stringify(params))
        }
    })
}

function sendErrorMessage(phone_no, _res) {
    whatsAppMessageService.sendWhatsAppMessageAPI(source_country, phone_no, 'Error processing your request. Please try again. \n\n0. Main Menu', function(err, wa_response) {
        if (err) {
            _res.json(false)
        } else {
            _res.json('Error processing your request. Please try again. \n\n0. Main Menu ')
        }
    })
}

function endSesssionGetData(phone_no, menu = '0') {
    endTicketSession(phone_no)
    selfservice.getSessionTicketNo(phone_no, async function(err, response) {
        formulateData(response, menu, phone_no)
    })
}
/**
 * Fetch claims data and send email
 * @param {} phone 
 */
const formulateClaimsData = async(phone_no, username) => {
    console.log(`formulateClaimsData ${username}`)
    selfservice.getSessionTicketNo(phone_no, async function(err, response) {
        fetchClaimsData(response, phone_no, username)
    })
}
const fetchClaimsData = async(ticket_no, phone_no, username) => {
    dotenv.config();
    sessionService.getAllUserFiles(ticket_no, function(err, response) {
        sessionService.getWhatsAppSessionData(ticket_no, function(err, resp) {
            if (err) {
                _res.json(false)
            } else {
                console.log(`SessionData length ${resp}`)
                console.log(`UserFiles length ${response}`)
                var attachment = []
                var count = 1
                var file_path = ''
                response.forEach(element => {
                    console.log(`URL ${process.env['SERVER_IP']}`)
                    console.log(element.dataValues.file_path)
                    file_path = process.env['SERVER_IP'] + '' + element.dataValues.file_path.substring(1);
                    console.log(`element.dataValues.file_path ${element.dataValues.file_path}`)
                    console.log(`file_path ${file_path}`)
                    //attachment.push(file_path)
                    count++
                    var menuSelected = "Register Claim" + "\n\n"
                    var body
                    var userData = +"\n" + "Name: " + username + "\n" + "Phone Number: " + phone_no
                    userData = userData.substring(1)
                    resp.forEach(element => {
                        if (element.key === "provide_details_life") {
                            body = menuSelected + 'Life Insurance: ' + element.session_data + +"\n" + userData
                        } else if (element.key === "provide_details_health") {
                            body = menuSelected + 'Health Insurance: ' + element.session_data + "\n" + userData
                        } else if (element.key === "provide_details_car") {
                            body = menuSelected + 'Motor Insurance: ' + element.session_data + "\n" + userData
                        }
                    })
    
                    //attachment = JSON.stringify(attachment)
                    //console.log(attachment)
                    console.log(`body ${body}`)
                    mailService.sendMail(body, "", "ug", file_path, function(err, resp) {
                        if (err) {
                            console.log(err)
                        }
                        console.log(resp.status)
                    })
    


                })
               
            }
        })


    })
}

function formulateData(ticket_no, menu, phone_no) {
    //sessionService.getUserFiles(ticket_no, function(err, response) {
        sessionService.getWhatsAppSessionData(ticket_no, async function(err, resp) {
            if (err) {
                console.log('error session service')
                _res.json(false)
            } else {
                var firstName
                var surName
                var emailAddress
                var nationalId
                var passportNo
                var taxIdNumber
                var carMake
                var carYOM
                var carRegNo
                
                resp.forEach(element => {
                    if (element.key === "motor_insurance" || element.key === "health_insurance" ) {
                        firstName = element.session_data
                    } else if (element.key === "enter_name_surname") {
                        surName = element.session_data
                    } else if (element.key === "enter_email_address") {
                        emailAddress = element.session_data
                    } else if (element.key === "continue") {
                        nationalId = element.session_data
                    } else if (element.key === "enter_tax_id_number") {
                        taxIdNumber = element.session_data
                    } else if (element.key === "done") {
                        passportNo = element.session_data
                    } else if (element.key === "enter_car_make") {
                        carMake = element.session_data
                    } else if (element.key === "enter_car_year_make") {
                        carYOM = element.session_data
                    } else if (element.key === "enter_car_reg_number") {
                        carRegNo = element.session_data
                    }
                })
                
                var menuSelected = "Buy Insurance"
                var body = ''
                var identifcation = ''
                if (nationalId == undefined) {
                    identifcation = "Passport Number " + passportNo + "\n"
                } else {
                    identifcation = "National ID " + nationalId + "\n"
                }
                if (menu == "0") {
                    body = menuSelected + "\n" + "FirstName " + firstName + "\n" +
                        "Surname " + surName + "\n" +
                        "Email Address " + emailAddress + "\n" + "Phone Number " + phone_no + "\n" +
                        "CarMake " + carMake + "\n" +
                        "Car Year Of Make " + carYOM + "\n" +
                        "Car Registration Number " + carRegNo
                } else {
                    body = menuSelected + "\n"  +
                        "Name and Surname " + surName + "\n" +
                        "Email Address " + emailAddress + "\n" + "Phone Number " + phone_no 
                        
                }

                var attachment = []
                //var file_path = process.env['SERVER_IP'] + '' + response.file_path.substring(1);
                //attachment.push(file_path)
                //attachment = JSON.stringify(attachment)
                console.log(attachment)
                mailService.sendMail(body, "", "ug", '', function(err, resp) {
                    if (err) {
                        console.log(err)
                    }
                    console.log(resp.status)
                })


            }


        })
    //})

}


/**
 * formulateRequestCallBack and send email
 * @param {} phone 
 */
const formulateRequestCallBack = async(phone_no, username) => {
    var menuSelected = "Register Claim" + "\n"
    var task = "Request CallBack" + "\n"
    var body = ""
    body = menuSelected + task + " Name: " + username + "\n" + "" + " PhoneNumber: " + phone_no + "\n" + ""
    console.log(`body ${body}`)
    mailService.sendMail(body, "", "rw", "", function(err, resp) {
        if (err) {
            console.log(err)
        }
        console.log(resp.status)
    })
}


 function logTransactions(user_function, phone_no){
    whatsappDBService.addTransactionLogs(user_function, phone_no, function(err, msg) {
        if (err) {
            console.log('failed to log transaction')
        } else {
            console.log('successfully logged transaction')
        }
    })
}

function getPreviousOption(user_response){

    let paths = user_response.path.split('*')
    let level = paths.length - 1 
    let unique_identifier = paths[level - 1]
    console.log( "PU "+ unique_identifier)
    console.log( "PU "+ (user_response.level - 1))
    let input_type  = whatsAppMenuService.getMenuBlock(user_response.level-1 ,unique_identifier,user_response)
    console.log( "PU "+ input_type)
    return [unique_identifier, input_type, user_response.level -1]
   
}

module.exports = {
    handleUserText: handleUserText,
    handleButtonEvent: handleButtonEvent,
    handleUserImages,
    handleUserDocuments,
    handleUserVideos
}