exports .
getTravelMenu = function(){
    return {
  "1": {
    "main_menu": {
      "title": "Hello! Welcome to Old Mutual Uganda\n\nWhat would you like to do?\n\nChoose an option:\n1. Buy Travel insurance\n2. Learn more about Travel insurance\n\nType and send Q to quit the flow at any point.\n\nBy continuing to the next step, you agree to our terms and conditions found at: https://www.oldmutual.rw/individual/Travel/Travel-insurance/",
      "next": {
        "1": "travel_insurance_first_name",
        "2": "travel_insurance_info"
      },
      "input_type": "SELECTION"
    }
  },
  "2": {
    "travel_insurance_info": {
      "title": "Our Travel insurance helps cover *accidents*, *emergencies*, and *medical expenses* while traveling abroad or locally. It provides peace of mind with comprehensive coverage including:\n\n• Medical emergencies\n• Trip cancellation\n• Lost luggage\n• Emergency evacuation\n• 24/7 assistance\n\nWould you like to:\n1. Buy Travel insurance\n0. Back to main menu",
      "next": {
        "1": "travel_insurance_first_name",
        "0": "main_menu"
      },
      "input_type": "SELECTION"
    }
  },
  "3": {
    "travel_insurance_first_name": {
      "title": "What is your first name?",
      "next": {
        "1": "travel_insurance_middle_name"
      },
      "input_type": "TEXT"
    }
  },
  "4": {
    "travel_insurance_middle_name": {
      "title": "What is your middle name?\n\n*Note:* If not applicable, respond with N/A",
      "next": {
        "1": "travel_insurance_last_name"
      },
      "input_type": "TEXT"
    }
  },
  "5": {
    "travel_insurance_last_name": {
      "title": "What is your last name?",
      "next": {
        "1": "travel_insurance_phone_confirm"
      },
      "input_type": "TEXT"
    }
  },
  "6": {
    "travel_insurance_phone_confirm": {
      "title": "To make the process seamless, we'll use this WhatsApp number (*+256 7XX XXX XXX*) as our primary way to reach you. This allows us to share your quotes, policy updates, and important information directly. ✅\n\n1. Yes\n2. No",
      "next": {
        "1": "travel_type_selection",
        "2": "main_menu"
      },
      "input_type": "SELECTION"
    }
  },
  "7": {
    "travel_type_selection": {
      "title": "Great! First, let's calculate your Travel insurance quote\n\nWhat type of Travel insurance would you like?\n\n1. World Wide (Broader protection, covering medical emergencies and travel risks globally)\n2. Schengen (Coverage for Schengen area countries with visa requirements)\n3. Student Cover (Specialized coverage for students studying abroad)\n4. Inbound (Coverage for visitors coming to Uganda)\n5. Africa & Asia (Regional coverage for African and Asian destinations)",
      "next": {
        "1": "trip_duration_selection",
        "2": "trip_duration_selection",
        "3": "trip_duration_selection",
        "4": "trip_duration_selection",
        "5": "trip_duration_selection"
      },
      "input_type": "SELECTION"
    }
  },
  "8": {
    "trip_duration_selection": {
      "title": "Choose a trip duration that suits you:\n\n1. 1-8 Days\n2. 9-14 Days\n3. 15-21 Days\n4. 22-30 Days\n5. 31-60 Days\n6. 61-90 Days",
      "next": {
        "1": "coverage_selection",
        "2": "coverage_selection",
        "3": "coverage_selection",
        "4": "coverage_selection",
        "5": "coverage_selection",
        "6": "coverage_selection"
      },
      "input_type": "SELECTION"
    }
  },
  "9": {
    "coverage_selection": {
      "title": "Who are you covering?\n\n1. Myself\n2. Someone else\n3. Myself & someone else\n4. Family (spouse and children)",
      "next": {
        "1": "destination_country",
        "2": "destination_country",
        "3": "destination_country",
        "4": "destination_country"
      },
      "input_type": "SELECTION"
    }
  },
  "10": {
    "destination_country": {
      "title": "What is the Destination Country?\n\nPlease enter the country name (e.g., Rwanda, Kenya, Germany, USA)",
      "next": {
        "1": "travel_start_date"
      },
      "input_type": "TEXT"
    }
  },
  "11": {
    "travel_start_date": {
      "title": "When is the travel start date?\n\nPlease enter in DD/MM/YYYY format (e.g., 20/03/2026)",
      "next": {
        "1": "travel_end_date"
      },
      "input_type": "TEXT"
    }
  },
  "12": {
    "travel_end_date": {
      "title": "When is the travel end date?\n\nPlease enter in DD/MM/YYYY format (e.g., 27/03/2026)",
      "next": {
        "1": "travelers_0_17"
      },
      "input_type": "TEXT"
    }
  },
  "13": {
    "travelers_0_17": {
      "title": "Specify the number of travellers\n\nNumber of travelers (0-17 Years):",
      "next": {
        "1": "travelers_18_69"
      },
      "input_type": "TEXT"
    }
  },
  "14": {
    "travelers_18_69": {
      "title": "Number of travelers (18-69 Years):",
      "next": {
        "1": "travelers_70_75"
      },
      "input_type": "TEXT"
    }
  },
  "15": {
    "travelers_70_75": {
      "title": "Number of travelers (70-75 Years):",
      "next": {
        "1": "travelers_76_80"
      },
      "input_type": "TEXT"
    }
  },
  "16": {
    "travelers_76_80": {
      "title": "Number of travelers (76-80 Years):",
      "next": {
        "1": "travelers_81_85"
      },
      "input_type": "TEXT"
    }
  },
  "17": {
    "travelers_81_85": {
      "title": "Number of travelers (81-85 Years):",
      "next": {
        "1": "plan_selection"
      },
      "input_type": "TEXT"
    }
  },
  "18": {
    "plan_selection": {
      "title": "Thank you!\nBased on the information you provided, please choose a plan below:\n\n1. Essential - UGX 350,000\nBenefits: Medical coverage up to UGX 350,000, emergency evacuation, trip cancellation coverage\nPremium Payable: UGX 150,000\n\n2. Elite - UGX 500,000\nBenefits: Enhanced medical coverage up to UGX 500,000, comprehensive emergency services, extended trip benefits\nPremium Payable: UGX 200,000\n\nRead more at https://www.oldmutual.ug/individual/Travel/Travel-insurance/",
      "next": {
        "1": "purchase_confirmation",
        "2": "purchase_confirmation"
      },
      "input_type": "SELECTION"
    }
  },
  "19": {
    "purchase_confirmation": {
      "title": "Do you want to proceed to purchase the insurance?\n\n1. Yes\n2. No\n\n*Note:* Application form might take 15-20 mins, please spare some time to fill it in.",
      "next": {
        "1": "applicant_surname",
        "2": "main_menu"
      },
      "input_type": "SELECTION"
    }
  },
  "20": {
    "applicant_surname": {
      "title": "Now let's get some Traveller Details, let's start with your details\n\nWhat is your Surname?",
      "next": {
        "1": "applicant_given_names"
      },
      "input_type": "TEXT"
    }
  },
  "21": {
    "applicant_given_names": {
      "title": "What are your Given Names?",
      "next": {
        "1": "applicant_nationality"
      },
      "input_type": "TEXT"
    }
  },
  "22": {
    "applicant_nationality": {
      "title": "What is your Nationality?\n\n1. Ugandan\n2. Non-Ugandan",
      "next": {
        "1": "applicant_nin",
        "2": "applicant_nin"
      },
      "input_type": "SELECTION"
    }
  },
  "23": {
    "applicant_nin": {
      "title": "What is your NIN Number? (if applicable)\n\nEnter your National Identification Number or N/A if not applicable",
      "next": {
        "1": "applicant_passport"
      },
      "input_type": "TEXT"
    }
  },
  "24": {
    "applicant_passport": {
      "title": "What is your Passport Number?",
      "next": {
        "1": "applicant_tin"
      },
      "input_type": "TEXT"
    }
  },
  "25": {
    "applicant_tin": {
      "title": "What is your TIN Number? (if applicable)\n\nEnter your Tax Identification Number or N/A if not applicable",
      "next": {
        "1": "applicant_dob"
      },
      "input_type": "TEXT"
    }
  },
  "26": {
    "applicant_dob": {
      "title": "What is your Date of birth?\n\nPlease enter in DD/MM/YYYY format (e.g., 12/08/1996)",
      "next": {
        "1": "applicant_occupation"
      },
      "input_type": "TEXT"
    }
  },
  "27": {
    "applicant_occupation": {
      "title": "What is your Occupation/Profession?\n\n(e.g., Lawyer, Teacher, Engineer, Student)",
      "next": {
        "1": "applicant_phone"
      },
      "input_type": "TEXT"
    }
  },
  "28": {
    "applicant_phone": {
      "title": "Now let's get some of your contact details\n\nWhat is your phone number?\n\n(e.g., **********)",
      "next": {
        "1": "applicant_email"
      },
      "input_type": "TEXT"
    }
  },
  "29": {
    "applicant_email": {
      "title": "What is your email address?\n\n(e.g., <EMAIL>)",
      "next": {
        "1": "applicant_postal_address"
      },
      "input_type": "TEXT"
    }
  },
  "30": {
    "applicant_postal_address": {
      "title": "What is your postal address?\n\n(e.g., P.O. Box 36158-00100 Kampala, Uganda)",
      "next": {
        "1": "applicant_city"
      },
      "input_type": "TEXT"
    }
  },
  "31": {
    "applicant_city": {
      "title": "What Town/City do you live in?\n\n(e.g., Kampala, Entebbe, Jinja)",
      "next": {
        "1": "banking_details_intro"
      },
      "input_type": "TEXT"
    }
  },
  "32": {
    "banking_details_intro": {
      "title": "Now let's get your Banking details.\n\n*Note:* We get these details to help us serve you better.\n\n1. Continue with banking details\n2. Skip banking details",
      "next": {
        "1": "bank_name",
        "2": "document_bio_data"
      },
      "input_type": "SELECTION"
    }
  },
  "33": {
    "bank_name": {
      "title": "What is your Bank name?\n\n(e.g., ABSA, Stanbic, Centenary Bank)",
      "next": {
        "1": "bank_account_holder"
      },
      "input_type": "TEXT"
    }
  },
  "34": {
    "bank_account_holder": {
      "title": "What is your Bank Account Holder Name?",
      "next": {
        "1": "bank_account_number"
      },
      "input_type": "TEXT"
    }
  },
  "35": {
    "bank_account_number": {
      "title": "What is your Account Number?",
      "next": {
        "1": "bank_currency"
      },
      "input_type": "TEXT"
    }
  },
  "36": {
    "bank_currency": {
      "title": "What currency is your Bank Account?\n\n1. Uganda Shillings (UGX)\n2. US Dollars (USD)\n3. Other",
      "next": {
        "1": "document_bio_data",
        "2": "document_bio_data",
        "3": "document_bio_data"
      },
      "input_type": "SELECTION"
    }
  },
  "37": {
    "document_bio_data": {
      "title": "Let's get some documents\n\nUpload a copy of your Bio Data Page\n\n*Please upload a clear photo of your passport bio data page*",
      "next": {
        "1": "document_passport"
      },
      "input_type": "IMAGE"
    }
  },
  "38": {
    "document_passport": {
      "title": "Upload a copy of Passport\n\n*Please upload a clear photo of your full passport*",
      "next": {
        "1": "additional_travelers_check"
      },
      "input_type": "IMAGE"
    }
  },
  "39": {
    "additional_travelers_check": {
      "title": "Let's get some of the details of the people you're travelling with\n\nDo you have additional travelers to add?\n\n1. Yes, add traveler details\n2. No, continue to next of kin",
      "next": {
        "1": "traveler_1_surname",
        "2": "next_of_kin_consent"
      },
      "input_type": "SELECTION"
    }
  },
  "40": {
    "traveler_1_surname": {
      "title": "Add Details for Traveller 1:\n\nWhat is their Surname?",
      "next": {
        "1": "traveler_1_given_names"
      },
      "input_type": "TEXT"
    }
  },
  "41": {
    "traveler_1_given_names": {
      "title": "What are their Given Names?",
      "next": {
        "1": "traveler_1_dob"
      },
      "input_type": "TEXT"
    }
  },
  "42": {
    "traveler_1_dob": {
      "title": "What is their Date of birth?\n\nPlease enter in DD/MM/YYYY format (e.g., 12/06/1997)",
      "next": {
        "1": "traveler_1_passport"
      },
      "input_type": "TEXT"
    }
  },
  "43": {
    "traveler_1_passport": {
      "title": "What is their Passport Number?",
      "next": {
        "1": "traveler_1_passport_upload"
      },
      "input_type": "TEXT"
    }
  },
  "44": {
    "traveler_1_passport_upload": {
      "title": "Upload a copy of their Passport\n\n*Please upload a clear photo of their passport*",
      "next": {
        "1": "traveler_details_review"
      },
      "input_type": "IMAGE"
    }
  },
  "45": {
    "traveler_details_review": {
      "title": "Traveller Details Summary:\n\n*Your Details*\nFull Name: %applicant_name%\nPassport No.: %applicant_passport%\nPhone Number: %applicant_phone%\nEmail Address: %applicant_email%\n\n*Traveller 1 Details*\nFull Name: %traveler_1_name%\nPassport No.: %traveler_1_passport%\n\nDo you wish to edit or add more travelers?\n\n1. Edit traveler details\n2. Add another traveler\n3. Continue to next of kin",
      "next": {
        "1": "traveler_edit_options",
        "2": "additional_traveler_check",
        "3": "next_of_kin_consent"
      },
      "input_type": "SELECTION"
    }
  },
  "46": {
    "traveler_edit_options": {
      "title": "Select the traveler you wish to edit:\n\n1. Your details\n2. Traveller 1 details\n3. Cancel and continue",
      "next": {
        "1": "applicant_surname",
        "2": "traveler_1_surname",
        "3": "next_of_kin_consent"
      },
      "input_type": "SELECTION"
    }
  },
  "47": {
    "additional_traveler_check": {
      "title": "Do you want to add another traveller?\n\n*Note:* Premium will change with additional travellers\n\n1. Yes\n2. No",
      "next": {
        "1": "traveler_2_surname",
        "2": "next_of_kin_consent"
      },
      "input_type": "SELECTION"
    }
  },
  "48": {
    "traveler_2_surname": {
      "title": "Add Details for Traveller 2:\n\nWhat is their Surname?",
      "next": {
        "1": "traveler_2_given_names"
      },
      "input_type": "TEXT"
    }
  },
  "49": {
    "traveler_2_given_names": {
      "title": "What are their Given Names?",
      "next": {
        "1": "traveler_2_dob"
      },
      "input_type": "TEXT"
    }
  },
  "50": {
    "traveler_2_dob": {
      "title": "What is their Date of birth?\n\nPlease enter in DD/MM/YYYY format",
      "next": {
        "1": "traveler_2_passport"
      },
      "input_type": "TEXT"
    }
  },
  "51": {
    "traveler_2_passport": {
      "title": "What is their Passport Number?",
      "next": {
        "1": "traveler_2_passport_upload"
      },
      "input_type": "TEXT"
    }
  },
  "52": {
    "traveler_2_passport_upload": {
      "title": "Upload a copy of their Passport\n\n*Please upload a clear photo of their passport*",
      "next": {
        "1": "next_of_kin_consent"
      },
      "input_type": "IMAGE"
    }
  },
  "53": {
    "next_of_kin_consent": {
      "title": "Proceed to add next of Kin details?\n\n*Note:* By continuing to the next step, you agree to our Data Consent Clause found at: https://www.oldmutual.ug/individual/Travel/Travel-insurance/\n\n1. Yes\n2. No",
      "next": {
        "1": "next_of_kin_first_name",
        "2": "payment_total"
      },
      "input_type": "SELECTION"
    }
  },
  "54": {
    "next_of_kin_first_name": {
      "title": "Let's get your next of kin details.\n\nWhat is your next of Kin first name?",
      "next": {
        "1": "next_of_kin_middle_name"
      },
      "input_type": "TEXT"
    }
  },
  "55": {
    "next_of_kin_middle_name": {
      "title": "What is your next of Kin middle name?\n\nEnter N/A if not applicable",
      "next": {
        "1": "next_of_kin_relationship"
      },
      "input_type": "TEXT"
    }
  },
  "56": {
    "next_of_kin_relationship": {
      "title": "What is your next of Kin's relationship with you?\n\n1. Spouse\n2. Parent\n3. Child\n4. Sibling\n5. Other",
      "next": {
        "1": "next_of_kin_email",
        "2": "next_of_kin_email",
        "3": "next_of_kin_email",
        "4": "next_of_kin_email",
        "5": "next_of_kin_email"
      },
      "input_type": "SELECTION"
    }
  },
  "57": {
    "next_of_kin_email": {
      "title": "What is your next of Kin's email address?",
      "next": {
        "1": "next_of_kin_postal_address"
      },
      "input_type": "TEXT"
    }
  },
  "58": {
    "next_of_kin_postal_address": {
      "title": "What is your next of Kin's Postal Address?",
      "next": {
        "1": "payment_total"
      },
      "input_type": "TEXT"
    }
  },
  "59": {
    "payment_total": {
      "title": "That's it!\n\nYour total due today: *UGX 455,100*\n\nProceed to pay?\n\n1. Yes\n2. No",
      "next": {
        "1": "payment_method",
        "2": "main_menu"
      },
      "input_type": "SELECTION"
    }
  },
  "60": {
    "payment_method": {
      "title": "How would you like to pay?\n\n1. Mobile Money\n2. Bank Transfer\n3. Credit/Debit Card",
      "next": {
        "1": "mobile_money_network",
        "2": "bank_transfer_details",
        "3": "card_payment_details"
      },
      "input_type": "SELECTION"
    }
  },
  "61": {
    "mobile_money_network": {
      "title": "Which network do you prefer?\n\n1. MTN Mobile Money\n2. Airtel Money",
      "next": {
        "1": "mobile_money_number",
        "2": "mobile_money_number"
      },
      "input_type": "SELECTION"
    }
  },
  "62": {
    "mobile_money_number": {
      "title": "Enter the number you want to pay with\n\n(e.g., **********)",
      "next": {
        "1": "payment_processing"
      },
      "input_type": "TEXT"
    }
  },
  "63": {
    "bank_transfer_details": {
      "title": "Bank Transfer Details:\n\n*Account Name:* Old Mutual Uganda\n*Account Number:* *********\n*Bank:* Stanbic Bank\n*Branch:* Head Office\n\nPlease use your phone number as reference.\n\nType 'DONE' when payment is complete",
      "next": {
        "1": "payment_confirmation"
      },
      "input_type": "TEXT"
    }
  },
  "64": {
    "card_payment_details": {
      "title": "You will be redirected to our secure payment gateway.\n\nProceed with card payment?\n\n1. Yes\n2. Choose different payment method",
      "next": {
        "1": "payment_processing",
        "2": "payment_method"
      },
      "input_type": "SELECTION"
    }
  },
  "65": {
    "payment_processing": {
      "title": "A prompt has been sent to the number provided. Enter your PIN to continue.\n\nProcessing payment...",
      "next": {
        "1": "payment_success"
      },
      "input_type": "END"
    }
  },
  "66": {
    "payment_confirmation": {
      "title": "Please confirm your payment by sharing the transaction reference or screenshot.\n\nOur team will verify and process your policy.",
      "next": {
        "1": "payment_success"
      },
      "input_type": "TEXT"
    }
  },
  "67": {
    "payment_success": {
      "title": "Success! Payment processed successfully.\n\nYour travel insurance policy has been generated and will be sent to your email within 24 hours.\n\nPlease find your receipt attached.\n\nThank you for choosing Old Mutual Uganda!\n\n0. Main Menu",
      "next": {
        "0": "main_menu"
      },
      "input_type": "END"
    }
  }
}
}