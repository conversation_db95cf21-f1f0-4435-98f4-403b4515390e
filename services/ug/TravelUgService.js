const validateService = require('./ValidateInputService'); // Assuming validateService is available
const whatsAppMessageService = require('./WhatsAppService'); // Assuming this service is available
const whatsappDBService = require('./WhatsappDBService'); // Assuming this service is available
const selfservice = require('./SelfService-API'); // Assuming this service is available

// Travel menu data (as provided)
const travelMenu =  require('./TravelMenu');

// Helper function to validate date format (DD/MM/YYYY)
function isValidDate(dateString) {
  const regex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
  if (!regex.test(dateString)) return false;
  const [day, month, year] = dateString.split('/').map(Number);
  const date = new Date(year, month - 1, day);
  return date.getDate() === day && date.getMonth() === month - 1 && date.getFullYear() === year;
}

// Helper function to validate phone number
function validatePhoneNumber(phone) {
  return validateService.validatePhoneNo(phone);
}

// Helper function to validate email
function validateEmail(email) {
  return validateService.validateEmail(email);
}

// Helper function to validate passport number (basic validation)
function validatePassport(passport) {
  return /^[A-Za-z0-9]{6,12}$/.test(passport);
}

// Helper function to validate NIN or TIN (if applicable, or N/A)
function validateNINorTIN(value) {
  if (value.toUpperCase() === 'N/A') return true;
  return /^[A-Za-z0-9]{8,16}$/.test(value);
}

// Main travel menu handler function
function handleTravelMenu(req, res, source_country, user_response, content) {
  let { level, unique_identifier, path, country, expected_input_type } = user_response;
  const phone_no = content.from;
  const user_input = content.message.text.trim();
  let new_level = level;
  let new_unique_identifier = unique_identifier;
  let menu_title = '';
  let input_type = expected_input_type;
  let validation_error = '';

  // Handle quit command
  if (user_input.toUpperCase() === 'Q') {
    new_level = 1;
    new_unique_identifier = country || 'main_menu';
    path = 'start';
    menu_title = travelMenu[1].main_menu.title;
    input_type = 'SELECTION';
  } else if (user_input === '0' && level > 1) {
    // Handle back to main menu
    new_level = 1;
    new_unique_identifier = country || 'main_menu';
    path = 'start';
    menu_title = travelMenu[1].main_menu.title;
    input_type = 'SELECTION';
  } else {
    // Get current menu node
    const current_menu = travelMenu[level] && travelMenu[level][unique_identifier];
    if (!current_menu) {
      // Invalid menu state, reset to main menu
      new_level = 1;
      new_unique_identifier = 'main_menu';
      menu_title = travelMenu[1].main_menu.title;
      input_type = 'SELECTION';
      path = 'start';
    } else {
      // Validate input based on input_type
      switch (current_menu.input_type) {
        case 'SELECTION':
          if (current_menu.next[user_input]) {
            new_level = level + 1;
            new_unique_identifier = current_menu.next[user_input];
            menu_title = travelMenu[new_level][new_unique_identifier].title;
            input_type = travelMenu[new_level][new_unique_identifier].input_type;
            path += '*' + user_input;
          } else {
            validation_error = 'Invalid selection. Please choose a valid option.\n';
            menu_title = current_menu.title;
            input_type = current_menu.input_type;
          }
          break;

        case 'TEXT':
          // Perform specific validations based on unique_identifier
          switch (unique_identifier) {
            case 'travel_start_date':
            case 'travel_end_date':
            case 'applicant_dob':
            case 'traveler_1_dob':
            case 'traveler_2_dob':
              if (!isValidDate(user_input)) {
                validation_error = 'Invalid date format. Please use DD/MM/YYYY.\n';
                menu_title = current_menu.title;
                input_type = current_menu.input_type;
              }
              break;
            case 'applicant_phone':
            case 'mobile_money_number':
              if (!validatePhoneNumber(user_input)) {
                validation_error = 'Invalid phone number format.\n';
                menu_title = current_menu.title;
                input_type = current_menu.input_type;
              }
              break;
            case 'applicant_email':
            case 'next_of_kin_email':
              if (!validateEmail(user_input)) {
                validation_error = 'Invalid email address.\n';
                menu_title = current_menu.title;
                input_type = current_menu.input_type;
              }
              break;
            case 'applicant_passport':
            case 'traveler_1_passport':
            case 'traveler_2_passport':
              if (!validatePassport(user_input)) {
                validation_error = 'Invalid passport number.\n';
                menu_title = current_menu.title;
                input_type = current_menu.input_type;
              }
              break;
            case 'applicant_nin':
            case 'applicant_tin':
              if (!validateNINorTIN(user_input)) {
                validation_error = 'Invalid NIN or TIN number, or enter N/A if not applicable.\n';
                menu_title = current_menu.title;
                input_type = current_menu.input_type;
              }
              break;
            case 'bank_transfer_details':
              if (user_input.toUpperCase() !== 'DONE') {
                validation_error = 'Please type "DONE" when payment is complete.\n';
                menu_title = current_menu.title;
                input_type = current_menu.input_type;
              }
              break;
            case 'travelers_0_17':
            case 'travelers_18_69':
            case 'travelers_70_75':
            case 'travelers_76_80':
            case 'travelers_81_85':
              if (!/^\d+$/.test(user_input) || parseInt(user_input) < 0) {
                validation_error = 'Please enter a valid number of travelers.\n';
                menu_title = current_menu.title;
                input_type = current_menu.input_type;
              }
              break;
            default:
              // No specific validation, proceed
              break;
          }

          if (!validation_error) {
            new_level = level + 1;
            new_unique_identifier = current_menu.next['1'];
            menu_title = travelMenu[new_level][new_unique_identifier].title;
            input_type = travelMenu[new_level][new_unique_identifier].input_type;
            path += '*' + user_input;
          }
          break;

        case 'IMAGE':
          if (user_input === '1') {
            new_level = level + 1;
            new_unique_identifier = current_menu.next['1'];
            menu_title = travelMenu[new_level][new_unique_identifier].title;
            input_type = travelMenu[new_level][new_unique_identifier].input_type;
            path += '*' + user_input;
          } else {
            validation_error = 'Please upload the requested image or select the correct option.\n';
            menu_title = current_menu.title;
            input_type = current_menu.input_type;
          }
          break;

        case 'END':
          if (user_input === '0') {
            new_level = 1;
            new_unique_identifier = 'main_menu';
            menu_title = travelMenu[1].main_menu.title;
            input_type = 'SELECTION';
            path += '*end';
          } else {
            validation_error = 'Session ended. Type 0 to return to main menu.\n';
            menu_title = current_menu.title;
            input_type = current_menu.input_type;
          }
          break;

        default:
          validation_error = 'Invalid menu configuration.\n';
          menu_title = current_menu.title;
          input_type = current_menu.input_type;
      }
    }
  }

  // Prepare menu response
  menu_title = validation_error + menu_title;

  // Send WhatsApp message and update session
  whatsAppMessageService.sendWhatsAppMessage(source_country, phone_no, menu_title, (err, wa_response) => {
    if (err) {
      console.error(`${phone_no} | wa_response: ${err}`);
      res.json({ error: err });
      return;
    }

    // Update user session
    const session_params = {
      phone_no,
      level: new_level,
      input: user_input,
      unique_identifier: new_unique_identifier,
      country: country,
      expected_input_type: input_type,
      path: path
    };

    whatsappDBService.updateUserSession(session_params, (err, session_response) => {
      if (err) {
        console.error(`${phone_no} | Error updating user session: ${session_response}`);
        res.json({ error: err });
        return;
      }

      // Store ticket session for TEXT or IMAGE inputs
      if (['TEXT', 'IMAGE'].includes(input_type)) {
        selfservice.ticket_session(
          phone_no,
          unique_identifier,
          user_input,
          level,
          expected_input_type,
          path,
          (err, resp) => {
            if (err) {
              console.error(`Error storing ticket session: ${err}`);
            }
          }
        );
      }

      // Handle payment processing
      if (new_unique_identifier === 'payment_success') {
        selfservice.check_ticket_no(phone_no, (err, ticket_no) => {
          if (!err && ticket_no) {
            selfservice.close_user_WhatsApp_Session(phone_no, ticket_no, (err, close_sess_response) => {
              if (err) {
                console.error(`Error closing session: ${err}`);
              }
            });
          }
        });
      }

      res.json(wa_response);
    });
  });
}

// Function to check if the unique_identifier is a travel-related option
function isTravelOption(unique_identifier) {
  return unique_identifier === 'travel_insurance' || unique_identifier.startsWith('travel_');
}

// Export functions
module.exports = {
  handleTravelMenu,
  isTravelOption,
  getTravelMenu: () => travelMenu
};