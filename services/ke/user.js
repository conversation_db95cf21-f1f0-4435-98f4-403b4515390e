exports.login = function (req, res) {

    if (req.method === "POST") {
        var post = req.body;
        if (req.body.token != null) {
            // console.log('Body:::'+(req.body));
            /* res.status(200).send(req.body.phone_no);
             return;*/
            /*console.log("post.phone_no:" + req.body.phone_no);
            console.log("post.password:" + req.body.password);*/
            phone_no = customValidate.formatMobileNo(post.phone_no);
            var pass = req.body.password;
            var token = req.body.token;

            console.log(sender_psid + ' Login request');

            dbObj.session_level
                .findOne(
                    {
                        where: {
                            phone_no: phone_no,
                            user_unique_identifier: token
                        }
                    }
                )
                .then(result => {
                    if (result !== null) {
                        console.log(result.psid + " Valid token ");
                        // encrypted = encryptService.encrypt(result.phone_no);
                        try {
                            validatePIN(result.psid, pass, res, token, phone_no);
                            /*let decryptedData = encryptService.decrypt({
                                "iv": result.user_unique_identifier,
                                "encryptedData": token
                            });
                            console.log("Decryp 1" + decryptedData + " result.psid " + result.psid);
                            sender_psid = result.psid;
                            if (decryptedData === phone_no) {

                                console.log("Valid 1");
                                /!* console.log("Valid payload");
                                 res.render('login_success.ejs');*!/
                                validatePIN(result.psid, pass, res, token, phone_no);
                            } else {
                                console.log("Valid 2");
                                invalidEntry(res, token);
                            }*/
                        } catch (e) {
                            console.log('Decrypt:' + e);
                        }

                    } else {
                        console.log("Invalid 3");
                        invalidEntry(res, token);
                    }

                });
        }
        else
            res.sendStatus(403);
    }

};
let system_retries = 0;

function validatePIN(sender_psid, pin, res, token, phone_no) {
    try {
        let body_part = {
            "pin": pin,
            "msisdn": phone_no
        };

        let body = Object.assign({}, api_body, body_part);

        console.log(sender_psid + " ======> validatePIN Request ");
        request.post({
            url: api_url + 'VALIDATE PIN/',
            body: body,
            json: true
        }, function (error, response, body) {
            if (!error && response.statusCode === 200) {
                console.log(sender_psid + " validatePIN Last Response: " + body.last_response + " response_status:" + body.response_status + " last_response:" + body.last_response);
                if (body.response_status === "00") {
                    validEntry(sender_psid, res);
                }

                else if (body.response_status === "55")
                    invalidEntry(res, token);
                // sendTextMessage(sender_psid, body.last_response);
                else
                    console.log(sender_psid + " Error PIN: " + "response_status:" + body.response_status + " last_response:" + body.last_response)
            } else if (!error && response.statusCode === 502) {

                system_retries = system_retries + 1;
                if (system_retries <= 3) {
                    console.log("===> System retry: " + system_retries);
                    validatePIN(sender_psid, pin, res, token, phone_no);//retry 3 times
                }

                else {
                    sendSystemErrorMsg(sender_psid)
                    // validEntry(sender_psid, res);
                }

            }
            else {
                console.log("Response: " + error)
                sendSystemErrorMsg(sender_psid)
            }


        });
    } catch (e) {
        sendSystemErrorMsg(sender_psid)
        console.log('Error: ' + e);
    }

}