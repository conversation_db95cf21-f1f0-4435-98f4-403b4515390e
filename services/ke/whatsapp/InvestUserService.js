'use strict';

var mcache = require('memory-cache');
var whatsAppMessageService = require('../../WhatsAppService');
var whatsAppMenuService = require('../../WhatsAppMenuService');
var whatsappDBService = require('../../WhatsappDBService');
var whatsAppSessionService = require('../../WhatsAppSessionService');
var selfservice = require('../../SelfService-API');
var sessionService = require('../../SessionService');
var validateService = require('../../ValidateInputService');
var investAPIService = require('../InvestAPIService');
let utils = require('../../Utils');
const { response } = require('express');
let source_channel = 2;
let enforceHWSingleRegistration = true
let tnc_link = 'https://bit.ly/2XfWN4o'

let APP_BASE_URL=`localhost:3010/whatsapp/confirm_otp/`;


let salutation_trigger = [
    'hi', 'hello', 'helo', 'halo', 'hallo', 'hey', 'mambo', 'vipi', 'niaje', 'menu', 'help', 'ke hi'
];
let salutation_response = ['Hi', 'Hello', 'Hello', 'Hi', 'Hi', 'Hi', 'Hi', 'Hi', 'Hi', 'Hi', 'Hi', 'Hi'];
let res;
let level, shouldUpdateSession = true;
let menu_data = {}
let menu = '';

async function handleMenuSelection(req, _res, source_country = 'ke', user_response) {
    res = _res;
    let content = req.body.results[0];
    let unique_identifier = content.message.text
    let phone_no = content.from


    /**
     * USER MADE A MENU SELECTION e.g. user entered 1,  etc
     *
     * */

    if (content.message.text == '0' && user_response.level == '1') {
        level = 0
        user_response.level = 0
        user_response.country = null
        unique_identifier = 'start'
        initialWelcome('Hi', content, source_country)
        return;
    }
    else if (content.message.text == '0' || user_response.unique_identifier == 'back_to_main') {
        level = 1
        user_response.level = 1
        unique_identifier = user_response.country
        user_response.unique_identifier = user_response.country
        user_response.path = 'start'
        // console.log(content.from + ' | SELECTION ****: ')
    }
    //update country
    else if (user_response.country == null) {
        user_response.level = 0
        let country = getCurrentUniqueIdentifier(content.message.text, user_response)
        user_response.country = country
        if (country === 'start') { // user entered an option not available.
            level = 0
            unique_identifier = 'start'
        } else {
            level = 1
            unique_identifier = country
            user_response.country = country
            user_response.country_id = content.message.text
        }

    } else {
        level = user_response.level + 1
        unique_identifier = getCurrentUniqueIdentifier(content.message.text, user_response)
        console.log(`unique_identifier ${unique_identifier}`)
        if (typeof unique_identifier == 'undefined') {
            level = user_response.level
            unique_identifier = user_response.unique_identifier
        }
        if (!shouldUpdateSession)
            level = user_response.level
    }
    console.log(utils.getDateTime() + " | " + content.from 
    + ' ||| SELECTION level: ' + level + ' NEW unique_identifier ' 
    + unique_identifier + ' OLD unique_identifier ' + user_response.unique_identifier 
    + ' input ' + content.message.text + ' expected_input_type ' + user_response.expected_input_type)
    // Validation
    let validation_error_msg = '';

    if (unique_identifier === 'invest_with_us') {
       //todo check if user exists on InterIntel API
       let userExists=await investAPIService.checkUserExists(phone_no)
        if(userExists['response']['data_source']['rows'].length>0){
            level = 3
             menu_data = getMenu(level, 'invest_existing_user', user_response)
             sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', 'invest_existing_user', level, user_response.path)
         } else {
            level = 3
            menu_data = getMenu(level, 'invest_new_user', user_response)
            sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', 'invest_new_user', level, user_response.path)
         }
    }
    else if (unique_identifier === 'invest_new_user') {
        menu_data = getMenu(level, unique_identifier, user_response)
        sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', unique_identifier, level, user_response.path)
    }
    else if (unique_identifier === 'invest_existing_user') {
        level = 3
        menu_data = getMenu(level, unique_identifier, user_response)
        sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', unique_identifier, level, user_response.path)
    }
    else if (unique_identifier === 'invest_confirm_phone_no') {
        // level = level +1;
        menu_data = getMenu(level, unique_identifier, user_response)
        menu = utils.parameterizedString(menu_data.menu, phone_no)
        sendToWhatsApp(source_country, phone_no, menu, 'SELECTION', unique_identifier, user_response.level, user_response.path)
    }
    else if (unique_identifier === 'invest_enter_another_phone_number') {
        // level = level +1;
        menu_data = getMenu(level, unique_identifier, user_response)
        sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', unique_identifier, level, user_response.path)
    }
    else if (unique_identifier === 'invest_verify_otp') {
        // level = 3;
        menu_data = getMenu(level, unique_identifier, user_response)
        menu = utils.parameterizedString(menu_data.menu, phone_no)
        sendToWhatsApp(source_country, phone_no, menu, 'TEXT', unique_identifier, level, user_response.path)
    }
    else if (unique_identifier === 'check_balance') {
        //todo check session
        level = level + 1;
        investAPIService.checkBalance({
            msisdn: phone_no,
            user_response: user_response
        }, function (response) {
            if (!response.error) {
                sendToWhatsApp(source_country, phone_no, response.user_message, 'SELECTION', unique_identifier, level)
            } else {
                sendToWhatsApp(source_country, phone_no, response.user_message, 'SELECTION', unique_identifier, level)
            }
        })
    }
    else if (unique_identifier === 'mpesa_stk_push_message') {
        level = level + 1;
        menu_data = getMenu(level, 'mpesa_stk_push_message', user_response)
        investAPIService.despositFunds({
            msisdn: phone_no,
            amount: 10,
            payment_method: 'M-PESA',
            sourceoffund: 'Salary',
            user_response: user_response
        }, function (response) {
            if (!response.error) {
                sendToWhatsApp(source_country, phone_no, response.user_message, 'SELECTION', unique_identifier, level)
            } else {
                sendToWhatsApp(source_country, phone_no, response.user_message, 'SELECTION', unique_identifier, level)
            }
        })
    }
    else if (unique_identifier === 'view_statement') {
        
        let accounts
        let customerAccounts 
        let balancedFundAccount
        let bondAccount
        let equityAccount
        let moneyMarketAccount

        investAPIService.fetchFundingAccounts({
            msisdn: phone_no
        }).then(result => {
            accounts = JSON.stringify(result)
            console.log("aaaacccounts " + accounts);

            let accountsArray = JSON.parse(accounts);

            balancedFundAccount = accountsArray[0][1]
            bondAccount = accountsArray[1][1]
            equityAccount = accountsArray[2][1]
            moneyMarketAccount = accountsArray[3][1]

            customerAccounts = "1." + accountsArray[0][0] + "\n2." + accountsArray[1][0] + "\n3." + accountsArray[2][0] + "\n4." + accountsArray[3][0]

            console.log(">>>>>>>>>> customer accounts >>>>>>>>>>>" + customerAccounts)

            console.log("BalancedFundAccount => " + balancedFundAccount 
                    + "\nBondAccount=> "  + bondAccount
                    + "\nEquityAccount => "+equityAccount
                    + "\nMoneyMarketAccount => " + moneyMarketAccount)

             mcache.put(phone_no+1, balancedFundAccount)
             mcache.put(phone_no+2, bondAccount)
             mcache.put(phone_no+3, equityAccount)
             mcache.put(phone_no+4, moneyMarketAccount)

        menu_data = getMenu('generic', 'user_invest_accounts', user_response)
        let menu = utils.parameterizedString(menu_data.menu, customerAccounts)
        sendToWhatsApp(source_country, phone_no, menu, 'SELECTION', "user_statement", level, user_response.path)

        }).catch(err => {
            console.log(err);
        });

    
    } else if (unique_identifier === 'user_statement') {

        console.log(">>>>>>>>>.. user_statement")
        // todo check session
        // todo getStatement
        // todo Parse on the selected
        investAPIService.fetchStatements({

            msisdn : phone_no,
            account : mcache.get(phone_no+1)  

        }).then(response=>{

            console.log("Statement: " + JSON.stringify(response))

            console.log("from cache => " + response['user_message'])

            menu_data = getMenu('generic', 'user_statement', user_response)

            let menu = utils.parameterizedString(menu_data.menu,response['user_message'])

          sendToWhatsApp(source_country, phone_no, menu, 'END', "", level, user_response.path)

          // remove the accounts from cache

          mcache.del(phone_no+1)
          mcache.del(phone_no+2)
          mcache.del(phone_no+3)
          mcache.del(phone_no+4)
        })
    }else if(unique_identifier === 'register_new_invest_user' ){
        menu_data = getMenu(level, 'register_new_invest_user', user_response)
        sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', 'register_new_invest_user', level, user_response.path)
    }else if(unique_identifier === 'invest_enter_national_id' ){
        menu_data = getMenu(level, 'invest_enter_national_id', user_response)
        sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'invest_enter_national_id', level, user_response.path)
    }else if(unique_identifier === 'invest_enter_passport' ){
        menu_data = getMenu(level, 'invest_enter_passport', user_response)
        sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'invest_enter_passport', level, user_response.path)
    }else if(unique_identifier === 'invest_accept_tnc' ){

       let occupationAux={
        "1": "Employed",
        "2": "Self employed",
        "3": "Student"
       };

       let argsAux= {
            "terms": true,
            "msisdn": phone_no,
            "document_number": mcache.get(phone_no+'-document_number'),
            "email": mcache.get(phone_no+'-email'),
            "kra_pin": mcache.get(phone_no+'-kra_pin'),
            "occupation": occupationAux[mcache.get(phone_no+'-occupation')],
            "physical_address":  mcache.get(phone_no+'-address')
        }

        let userURL=`${APP_BASE_URL}${phone_no}`
        menu_data = getMenu(level, 'invest_accept_tnc', user_response)

        let registerUser=await investAPIService.registerUser(argsAux,''); 

        if(registerUser['status']==true){
            menu_data.menu=`Thanks. Let's setup your username and password\n\nPlease follow this link to capture your information ${userURL}`;
            sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'invest_accept_tnc', level, user_response.path)    
       
        }else{
            menu_data.menu=registerUser['message'];
            sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'invest_accept_tnc', level, user_response.path)  
        }

    }
    /**
     * Else Goto Next Menu SELECTED
     * */
    else {
        console.log('-------> *Generic =>> ')
        menu_data = getMenu(level, unique_identifier, user_response)
        sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', unique_identifier, level, user_response.path)

    }
}

function handleUserUnstructuredTextInput(req, _res, source_country = 'ke', user_response) {
    res = _res;
    let content = req.body.results[0];
    let unique_identifier = content.message.text
    let phone_no = content.from

    // unique_identifier = user_response.unique_identifier
    let input_type = ''
    if (content.message.text == '0') {
        level = 1
        user_response.level = 1
        unique_identifier = user_response.country
        user_response.unique_identifier = user_response.country
        user_response.path = 'start'

        input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);
        // console.log(content.from + ' | SELECTION ****: ')
    } else {
        level = user_response.level + 1
        unique_identifier = getCurrentUniqueIdentifier(1, user_response) // And entry level always has one next step!

        input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);
    }

    console.log(content.from + ' | level: ' + level + ' new unique_identifier ' + unique_identifier + ' OLD ' + user_response.unique_identifier + ' expected_input_type ' + user_response.expected_input_type + ': ' + content.message.text)

    menu_data = getMenu(level, unique_identifier, user_response)
    menu = menu_data.menu
    unique_identifier = menu_data.unique_identifier

    console.log(utils.getDateTime() + " | REQ: " + content.from + ' | TEXT level: ' + level + ' new unique_identifier ' + unique_identifier + ' | CURRENT input_type ' + input_type)

    /**
     * Add here any unique_identifier that requires ID or Passport verification
     * */
    if (user_response.unique_identifier === 'invest_enter_national_id') {
        mcache.put(phone_no+'-document_number',content.message.text)
        let valid = validateService.validateIdNumber(content.message.text, source_country)
        if (!valid) {
            let validation_error_msg = 'Invalid Passport Number\n'
            var level = user_response.level
            unique_identifier = user_response.unique_identifier
            menu_data = getMenu(level, unique_identifier, user_response)
            menu = validation_error_msg + '' + menu_data.menu
            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function (err, wa_response) {
                if (err) {
                    console.error(content.from + ' | wa_response: ' + err)
                    res.statusCode(false)
                } else
                    res.json(wa_response)
            })
        } else {
            var level = 7;
            menu_data = getMenu(level, 'invest_enter_kra_pin', user_response)
            let params = {
                phone_no: content.from,
                level: level + 1,
                input: content.message.text,
                unique_identifier: unique_identifier,
                country: user_response.country,
                country_id: user_response.country_id,
                expected_input_type: 'TEXT',
                path: user_response.path + '*' + content.message.text
            }
            updateWhatsappUserSession(params)
            sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'invest_enter_kra_pin', level, user_response.path)
        }
    }
    else if (user_response.unique_identifier === 'invest_verify_otp') {
        console.log(`invest_verify_otp ${content.message.text}`)
        // let valid = validateService.va(content.message.text)
        // todo Validate OTP
        let valid = true
        if (!valid) {
            let validation_error_msg = 'Invalid OTP\n'
            var level = user_response.level
            unique_identifier = user_response.unique_identifier
            menu_data = getMenu(level, unique_identifier, user_response)

            menu = validation_error_msg + '' + menu_data.menu
            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function (err, wa_response) {
                if (err) {
                    console.error(content.from + ' | wa_response: ' + err)
                    res.statusCode(false)
                } else
                    res.json(wa_response)
            })
        } else {
            var level = 3;
            menu_data = getMenu(level, 'invest_existing_user', user_response)
            menu = `Welcome ${user_response.whatsapp_name} to i-INVEST\n${menu_data.menu}`
            sendToWhatsApp(source_country, phone_no, menu, 'SELECTION', 'invest_existing_user', level, user_response.path)
        }

    }
    else if (user_response.unique_identifier === 'invest_enter_another_phone_number') {
        //todo validate phone number
        let user_status='does-not-exist-on-i-invest'
        switch(user_status){
            case 'exists-on-i-invest':
                break;
            case 'does-not-exist-on-i-invest':
                break;
        }
        investAPIService.checkUserExists(phone_no)
        level = 4;
        menu_data = getMenu(level, 'invest_verify_otp', user_response)
        menu = utils.parameterizedString(menu_data.menu, phone_no)
        sendToWhatsApp(source_country, phone_no, menu, 'TEXT', 'invest_verify_otp', level, user_response.path)
    }
    else if (user_response.unique_identifier === 'invest_enter_passport') {
        mcache.put(phone_no+'-document_number',content.message.text)
        let valid = validateService.isValidIdOrPassportNo(content.message.text)
        if (valid.error != null) {
            let validation_error_msg = 'Invalid Passport Number\n'
            var level = user_response.level
            unique_identifier = user_response.unique_identifier
            menu_data = getMenu(level, unique_identifier, user_response)
            menu = validation_error_msg + '' + menu_data.menu
            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function (err, wa_response) {
                if (err) {
                    console.error(content.from + ' | wa_response: ' + err)
                    res.statusCode(false)
                } else
                    res.json(wa_response)
            })
        } else {
            var level = 7;
            menu_data = getMenu(level, 'invest_enter_kra_pin', user_response)
            let params = {
                phone_no: content.from,
                level: level + 1,
                input: content.message.text,
                unique_identifier: unique_identifier,
                country: user_response.country,
                country_id: user_response.country_id,
                expected_input_type: 'TEXT',
                path: user_response.path + '*' + content.message.text
            }
            updateWhatsappUserSession(params)
            sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'invest_enter_kra_pin', level, user_response.path)
        }

    }
    else if (user_response.unique_identifier === 'invest_enter_kra_pin') {
        mcache.put(phone_no+'-kra_pin',content.message.text)
        // let valid = validateService.validateTaxIDNumber(content.message.text, source_country)
        let valid = true
        if (!valid && (content.message.text).toLowerCase() !== 's') {
            let validation_error_msg = 'Invalid KRA PIN\n'
            var level = user_response.level
            unique_identifier = user_response.unique_identifier
            menu_data = getMenu(level, unique_identifier, user_response)

            menu = validation_error_msg + '' + menu_data.menu
            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function (err, wa_response) {
                if (err) {
                    console.error(content.from + ' | wa_response: ' + err)
                    res.statusCode(false)
                } else
                    res.json(wa_response)
            })
        } else {
            var level = 9;
            menu_data = getMenu(level, 'invest_enter_email', user_response)
            let params = {
                phone_no: content.from,
                level: level,
                input: content.message.text,
                unique_identifier: unique_identifier,
                country: user_response.country,
                country_id: user_response.country_id,
                expected_input_type: 'TEXT',
                path: user_response.path + '*' + content.message.text
            }
            updateWhatsappUserSession(params)
            sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'invest_enter_email', level, user_response.path)
        }

    }
    else if (user_response.unique_identifier === 'make_an_investment') {
        console.log(`make_an_investment ${content.message.text}`)
        // let valid = validateService.val(content.message.text, source_country)
        let valid = true
        if (!valid) {
            let validation_error_msg = 'Invalid Passport Number\n'
            var level = user_response.level
            unique_identifier = user_response.unique_identifier
            menu_data = getMenu(level, unique_identifier, user_response)
            menu = validation_error_msg + '' + menu_data.menu
            let params = {
                phone_no: content.from,
                level: level + 1,
                input: content.message.text,
                unique_identifier: unique_identifier,
                country: user_response.country,
                country_id: user_response.country_id,
                expected_input_type: input_type,
                path: user_response.path + '*' + content.message.text
            }
            updateWhatsappUserSession(params)
            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function (err, wa_response) {
                if (err) {
                    console.error(content.from + ' | wa_response: ' + err)
                    res.statusCode(false)
                } else
                    res.json(wa_response)
            })
        } else {
            var level = user_response.level + 1;
            menu_data = getMenu(level, 'mpesa_stk_push_message', user_response)
            sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'mpesa_stk_push_message', level, user_response.path)
        }

    }
    else if (user_response.unique_identifier === 'invest_enter_email') {
        mcache.put(phone_no+'-email',content.message.text)
        let valid = validateService.validateEmail(content.message.text)
        if (!valid) {
            let validation_error_msg = 'Invalid email address\n'
            var level = user_response.level
            unique_identifier = user_response.unique_identifier
            menu_data = getMenu(level, unique_identifier, user_response)

            menu = validation_error_msg + '' + menu_data.menu
            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function (err, wa_response) {
                if (err) {
                    console.error(content.from + ' | wa_response: ' + err)
                    res.statusCode(false)
                } else
                    res.json(wa_response)
            })
        } else {
            var level = user_response.level + 1
            menu_data = getMenu(level, 'invest_enter_postal_address', user_response)
            let params = {
                phone_no: content.from,
                level: level,
                input: content.message.text,
                unique_identifier: unique_identifier,
                country: user_response.country,
                country_id: user_response.country_id,
                expected_input_type: 'TEXT',
                path: user_response.path + '*' + content.message.text
            }
            updateWhatsappUserSession(params)
            sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'invest_enter_postal_address', level, user_response.path)
        }

    }
    else if (user_response.unique_identifier === 'invest_enter_postal_address') {
        mcache.put(phone_no+'-address',content.message.text)

        var level = user_response.level + 1
        menu_data = getMenu(level, 'invest_choose_occupation', user_response)
        let params = {
            phone_no: content.from,
            level: level,
            input: content.message.text,
            unique_identifier: unique_identifier,
            country: user_response.country,
            country_id: user_response.country_id,
            expected_input_type: 'SELECTION',
            path: user_response.path + '*' + content.message.text
        }
        updateWhatsappUserSession(params)
        sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', 'invest_choose_occupation', level, user_response.path)
    }
    else if (user_response.unique_identifier === 'invest_choose_occupation') {
        mcache.put(phone_no+'-occupation',content.message.text)
        var level = user_response.level + 1
        menu_data = getMenu(level, 'invest_terms_and_condition', user_response)
        let params = {
            phone_no: content.from,
            level: level,
            input: content.message.text,
            unique_identifier: unique_identifier,
            country: user_response.country,
            country_id: user_response.country_id,
            expected_input_type: 'SELECTION',
            path: user_response.path + '*' + content.message.text
        }
        updateWhatsappUserSession(params)
        sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', 'invest_terms_and_condition', level, user_response.path)
    }
}

async function updateWhatsappUserSession(params) {
    await whatsappDBService.updateUserSession(params, function (err, session_response) {
        if (err) {
            console.error(content.from + ' | Existing Error updating user session : ' + session_response)
            // res.json(err)
        } else {
            try {
                res.json(session_response)
            } catch (e) {

            }
        }
    })
}

function endTicketSession(phone_no) {
    selfservice.check_ticket_no(phone_no, function (err, ticket_no) {
        if (err) {

        } else {

            // console.log('provide_details_photos_end NON healthWorker:')
            selfservice.update_ticket(ticket_no, function (err, update_ticket_response) {
                if (err) {

                } else {
                    selfservice.close_user_WhatsApp_Session(phone_no, ticket_no, function (err, close_sess_response) {
                        if (err) {

                        } else {
                            // console.log('Ticket to closed NON Healthworker: ' + ticket_no + ' Message: ' + update_ticket_response)
                        }

                    })
                }
            })
        }

    })
}

function handleUserImages(req, _res, source_country = 'ke') {
    res = _res;
    let content = req.body.results[0];
    let unique_identifier
    let phone_no = content.from

    console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: IMAGE ' + content.messageId)
    whatsappDBService.getUserLevel(phone_no, function (err, user_response) {
        if (err) {
            initialWelcome(salutation_response[0], content, source_country)
        } else {
            unique_identifier = getCurrentUniqueIdentifier(1, user_response)
            console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: IMAGE level: ' + user_response.level + ' new unique_identifier ' + unique_identifier + ' old unique_identifier ' + user_response.unique_identifier + ' input ' + content.message.url + ' expected_input_type ' + user_response.expected_input_type + ' Msg Id: ' + content.messageId)

            if (content.message.text == '0' || unique_identifier == 'back_to_main') {
                level = 1
                unique_identifier = user_response.country
                user_response.path = 'start'
            } else
                level = user_response.level

            let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

            if (user_response.unique_identifier === 'photo_national_id' || user_response.unique_identifier === 'photo_passport') {
                var level = user_response.level + 1
                menu_data = getMenu(level, 'enter_tax_id_number', user_response)
                sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'enter_tax_id_number', level, user_response.path)
                // }
            }
            if (input_type == 'END') {
                user_response.unique_identifier = 'success_end_message'
            }
            if (user_response.unique_identifier === 'health_worker_national_id_photo' || user_response.unique_identifier === 'health_worker_staff_id_photo') {

                selfservice.download_reg_media(content.message.url, phone_no, content.message.caption, function (err, resp) {
                    if (err) {
                        console.error(utils.getDateTime() + ' | ' + phone_no + 'Error downloading HW registration images ' + err)
                        sendErrorMessage(phone_no, _res)
                    } else {
                        console.log('*** Health worker media saved')
                        level = user_response.level + 1
                        menu_data = getMenu(level, unique_identifier, user_response)
                        menu = menu_data.menu
                        unique_identifier = menu_data.unique_identifier
                        input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);
                        sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)
                    }
                })

            } else
                selfservice.get_ticket_no(phone_no, 2, user_response.level, content.message.url, user_response.unique_identifier, user_response.expected_input_type, user_response.path, function (err, ticket_no) {
                    if (err) {
                        res.json(false)
                    } else {
                        selfservice.download_file(content.message.url, phone_no, ticket_no, content.message.caption, function (err, resp) {
                            if (err) {
                                console.log("Error => " + err);
                            } else {
                                selfservice.ticket_session(phone_no, user_response.unique_identifier, 'image', user_response.level, user_response.expected_input_type, user_response.path, function (err, resp) {
                                    if (err) {
                                        console.log('*** Unique ID ignored')
                                    } else {
                                        // console.log('*** Unique ID stored!' + unique_identifier)
                                        if (unique_identifier.includes('health_worker') ||
                                            unique_identifier === 'signup_phone_no' ||
                                            unique_identifier === 'selfie_passport_photo') {
                                            console.log('--HW photo' + resp)
                                            level = user_response.level + 1
                                            menu_data = getMenu(level, unique_identifier, user_response)
                                            menu = menu_data.menu
                                            unique_identifier = menu_data.unique_identifier
                                            input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);
                                            sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)
                                        }
                                        /*else if (unique_identifier === 'provide_details_photos_end') {
                                                                               level = user_response.level + 1
                                                                               menu_data = getMenu(level, unique_identifier, user_response)
                                                                               menu = menu_data.menu
                                                                               unique_identifier = menu_data.unique_identifier
                                                                               input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier,user_response)
                                                                               sendToWhatsApp(source_country,phone_no, menu, input_type, unique_identifier, level)
                                                                               endTicketSession(phone_no)
                                                                           }*/
                                        else

                                            res.json(true)
                                    }

                                })
                            }
                        })
                    }
            })

        }
    })

}

function handleUserVideos(req, _res) {
    console.log('*** Unique ID ignored')
    let content = req.body.results[0];
    console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: VIDEO TYPE UPLOADED Msg Id ' + content.messageId)

    let menu = 'Please upload a photo or a document.'
    whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function (err, wa_response) {
        if (err) {
            _res.json(false)
        } else {
            _res.json(menu)
        }
    })
}

function handleUserDocuments(req, _res) {
    console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: DOCUMENT ' + content.messageId)
    handleUserImages(req, _res)

}

function getMenu(level, unique_identifier, user_response) {
    console.log('--> 1 CHECK MENU  level: ' + level + ' Unique: ' + unique_identifier + ' Prev input: ' + user_response.input)
    console.log('User data: ' + JSON.stringify(user_response))
    let _menu = whatsAppMenuService.getMenu(level, unique_identifier, user_response)
    if (typeof _menu === 'undefined') {
        let _level = level - 1;
        console.log(utils.getDateTime() + " | " + user_response.phone_no + 'MENU: getMenu() not found  level: ' + _level + ' Unique: ' + unique_identifier + ' Prev input: ' + user_response.input)
        shouldUpdateSession = false;
        unique_identifier = whatsAppMenuService.getMenuByIndex(user_response.level, user_response.unique_identifier, user_response.input, user_response)
        //console.log('--> Previous uniq %% ' + unique_identifier)
        _menu = whatsAppMenuService.getMenu(level, unique_identifier, user_response)
        return {menu: _menu, unique_identifier: unique_identifier};
    } else {
        console.log('--> Menu Found  level: ' + level + ' Unique: ' + unique_identifier + ' Prev input: ' + user_response.input + ' - ' + _menu.title)
        shouldUpdateSession = true;
        return {menu: _menu, unique_identifier: unique_identifier};
    }
}

function getCurrentUniqueIdentifier(input, user_response) {
    let _unique_identifier, level;
    _unique_identifier = whatsAppMenuService.getMenuByIndex(user_response.level, user_response.unique_identifier, input, user_response)
    if (typeof _unique_identifier === 'undefined') {
        shouldUpdateSession = false
        console.log(utils.getDateTime() + " | " + user_response.phone_no + 'MENU: getCurrentUniqueIdentifier() unique_identifier not Found  level: ')

        if (user_response.level == 1) {
            return whatsAppMenuService.getMenuByIndex(0, 'start', user_response.country_id, user_response)
        } else {
            return user_response.unique_identifier
        }
        return whatsAppMenuService.getMenu(level, _unique_identifier)
    } else {
        if (user_response.level == 1)
            shouldUpdateSession = true;
        return _unique_identifier;
    }
}

function initialWelcome(salutation, content, source_country) {
    let name = ""
    if ("contact" in content) {
        content.contact.name = content.contact.name.replace(/([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        if (typeof content.contact.name == 'string') {
            salutation = salutation + " " + content.contact.name
            name = content.contact.name
        }

    }
    let salute = ''
    if (source_country === 'tz')
        salute = salutation + ', we\'re happy to have you here 🙂. Welcome to UAP. I will be your virtual assistant.' +
            ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n'
    else
        salute = salutation + ', we\'re happy to have you here 🙂. Welcome to UAP Old Mutual and Faulu Microfinance Bank. I will be your virtual assistant.' +
            ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n'
    whatsappDBService.updateUserSession({
        phone_no: content.from,
        whatsapp_name: name,
        level: 0,
        input: content.message.text,
        unique_identifier: 'start',
        expected_input_type: 'SELECTION',
        country: null,
        country_id: null,
        path: 'start'
    }, function (err, msg) {
        if (err) {

        } else {
            salute = salute + '\n' + whatsAppMenuService.getMenu(0, 'start', {country: null})
            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, salute, function (err, wa_response) {
                if (err) {
                    console.error(content.from + ' | wa_response: ' + err)
                } else {
                    // whatsAppSessionService.startSession(content.from,'name','Emmanuel')
                    try {
                        res.json(wa_response)
                    } catch (e) {

                    }
                }

            })

        }
    });

}

function sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, path) {
    whatsAppMessageService.sendWhatsAppMessage(source_country, phone_no, menu, function (err, wa_response) {
        if (err) {
            console.error(phone_no + ' | wa_response: ' + err)
            try {
                res.json(false)
            } catch (e) {

            }
        } else {
            whatsappDBService.updateUserSession({
                phone_no: phone_no,
                expected_input_type: input_type,
                unique_identifier: unique_identifier,
                level: level,
                path: path
            }, function (err, session_response) {

                if (err) {
                    console.error(phone_no + ' | Existing Error updating user session : ' + session_response)
                        res.json(err)
                } else {
                    try {
                        return res.json(wa_response);
                    } catch (e) {

                    }
                }
            })
        }
    })
}

function getTicketResponseMessage(name, ticket_no, unique_identifier = null) {

    if (unique_identifier !== null && unique_identifier == 'bank_with_us_option_success_end_message')
        return 'Hi ' + name + ', Thank you for contacting us. Your ticket number is ' + ticket_no + '. You will be contacted by our call centre team shortly. ' +
            'To view our online portal, click here: https://www.faulukenya.com/ and to view our T&Cs click here: https://bit.ly/3gF1AnI\n\n0. Main Menu'
    else if (unique_identifier !== null && unique_identifier === 'provide_details_car_photos')
        return 'Hi ' + name + ', Thank you for contacting us. Your ticket number is ' + ticket_no + '. You will be contacted by our call centre team shortly. ' +
            'If within Nairobi and its environs, kindly proceed to our assessment centre at Lower Upper Hill road for review and authorization. ' +
            'To view our online portal, click here: https://bit.ly/2V2jEze and to view our T&Cs click here:  ' + tnc_link + ' \n\n0. Main Menu'
    else
        return 'Hi ' + name + ', Thank you for contacting us. Your ticket number is ' + ticket_no + '. You will be contacted by our call centre team shortly. ' +
            'To view our online portal, click here: https://bit.ly/2V2jEze and to view our T&Cs click here:  ' + tnc_link + ' \n\n0. Main Menu'

}

function updateCountry(params) {
    whatsappDBService.updateCountry(params, function (err, msg) {
        if (err) {
            console.log('Error updating country')
        } else {
            console.log('Country updated: ' + JSON.stringify(params))
        }
    })
}

function sendErrorMessage(phone_no, _res) {
    whatsAppMessageService.sendWhatsAppMessage(source_country, phone_no, 'Error processing your request. Please try again. \n\n0. Main Menu', function (err, wa_response) {
        if (err) {
            _res.json(false)
        } else {
            _res.json('Error processing your request. Please try again. \n\n0. Main Menu ')
        }
    })
}


module.exports = {
    handleUserUnstructuredTextInput: handleUserUnstructuredTextInput,
    handleMenuSelection: handleMenuSelection,
    handleUserImages,
    handleUserDocuments,
    handleUserVideos
};