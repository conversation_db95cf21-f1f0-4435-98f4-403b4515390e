/**
 *  Authenticate user before any post login service on i-INVEST(InterIntel APIs provided)
 * The user can access any post login service if they're in 5 minutes login window. 
 */
function authenticateUserSession(request) {
    console.log('USER => ' + request.user)
    console.log('USER AUTHENTICATED => ' + request.isAuthenticated())
    if (!request.isAuthenticated()) {
        return false
    }
    // CONTINUE WITH API CALL
    return true
}

module.exports = {
    authenticateUserSession: authenticateUserSession
}