const request = require('request')
const dotenv = require('dotenv')
const passport = require('passport')
const axios = require('axios')
const {errorName} = require('../../utils/constants')
const utils = require('../../services/Utils')
//let userUtils = require('../utils/user_log_activities');
var formatter = new Intl.NumberFormat('en-KE');

const BASEURL = 'https://bot.uapoldmutual.com/ke/inter-intel/'
const API_USERNAME = "apiuser"
const API_PASSWORD = "eCcH%CJA7Rnt"
const GATEWAY_HOST = "i-invest.interintel.co"
const TEST_GATEWAY_HOST = "i-invest.interintel.co"
const TEST_API_USERNAME = "apiuser"
const STATEMENT_TRANSACTIONS = 5



/**
 *  First checks if the user exists (DATA SOURCE)
 *  Registers user on Interintel
 *
 */
const registerUser = async (args, req) => {

    let res={};

    //VALIDATE IF USER EXISTS
    const userData = await checkUserExists(args.msisdn)
    if(userData['response']['data_source']['rows'].length<1){
        let onBoardUserAux=await onBoardUser(args)
        if(onBoardUserAux['response_status']!='00'){
            res={
               'status':true,
               'message':'success'
            };
        }else{
            res= {
                'status':false,
                'message':onBoardUserAux['last_response']
             };
        }

    } else {
        //RETURN THE USER NAME
        let result = {};
        const name = userData[0][2] + ' ' + userData[0][3] + ' ' + userData[0][4]
        result['success'] = true
        result['userExists'] = true
        result['name'] = name
        result['msisdn'] = args.msisdn
        result['message'] = 'User already registered'
        //CREATE SESSION AND COOKIE FOR USER IN PASSPORT
        /* req.login(args.msisdn, () => {
            console.log('USER LOGGED IN ==>' + args.msisdn)
        }) */
        return result
    }
    return res;
}

/**
 * Registers User in InterIntel
 */
const onBoardUser = async (args) => {

    const accessToken = await getTokenData()
    return new Promise((resolve, reject) => {
        var options = {
            'method': 'POST',
            'url': BASEURL + 'REGISTER NEW USER/',
            'headers': {
                'Authorization': 'Bearer ' + accessToken,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "credentials": {
                    "username": API_USERNAME,
                    "password": API_PASSWORD
                },
                "gateway_host": GATEWAY_HOST,
                "api_token": "0b8688df-3bd6-484f-9ee7-1f8ca39eacb3",
                "ip_address": "*************",
                "chid": "14",
                "terms": args.terms,
                "postal_address": "12345-0100",
                "accesspoint": "808",
                "access_point": "*808*72#",
                "msisdn": args.msisdn,
                "document_number": args.document_number,
                "email": args.email,
                "kra_pin": args.kra_pin,
                "occupation": args.occupation,
                "physical_address": args.physical_address
            })
        };
        request(options, function (error, response) {
            if (error) {
                console.error(error);
                throw new Error(error);
            }
            let obj = JSON.parse(response.body)
            console.log('Save lead ==> ' + JSON.stringify(obj))
            resolve(obj)
        });
    }).catch((error) => () => {
        console.log(error);
    });}

const checkBalance = async (args, callback) => {
    //FETCH FUNDING ACCOUNTS
    let balanceData = "*Balances*\n";
    let result = {}
    try {
        const fundingAccounts = await fetchFundingAccounts(args)
        console.log("balanceData lenght => " + fundingAccounts.length)
        if (fundingAccounts.length === 0) {
            result = {
                'error': false,
                'user_message': 'There was an error retrieving your fund balance',
                'msisdn': args.msisdn
            }
        } else {
            for (let i = 0; i < fundingAccounts.length; i++) {
                const balance = await fetchBalance(args, fundingAccounts[i][0])
                balanceData += balance + '\n'
            }
            result = {'error': false, 'user_message': balanceData, 'msisdn': args.msisdn}
            console.log("balanceData => " + JSON.stringify(balanceData))
        }

        callback(result)
    } catch (e) {
        result = {
            'error': true,
            'user_message': 'Error getting fund balance',
            'system_message': null,
            'msisdn': args.msisdn
        }
        console.log("balanceData => " + JSON.stringify(balanceData))
        callback(result)
    }

}

/**
 * Fetch Balance from Interintel
 */
const fetchBalance = async (args, fundAccount) => {
    const accessToken = await getTokenData()
    let params = {
        "credentials": {
            "username": API_USERNAME,
            "password": API_PASSWORD
        },
        "gateway_host": GATEWAY_HOST,
        "api_token": "0b8688df-3bd6-484f-9ee7-1f8ca39eacb3",
        "ip_address": "*************",
        "chid": "14",
        "msisdn": args.msisdn,
        "trigger": "balance",
        "investmentfund": fundAccount

    }

    try {
        let response = await axios.post(BASEURL + 'CHECK BALANCE/', params,
            {
                'headers': {
                    'Authorization': 'Bearer ' + accessToken,
                    'Content-Type': 'application/json'
                }
            });
        const obj = response.data


        if (obj['response_status'] === '00') {
            return `${fundAccount}: ${utils.getCountryDetailsByCountryName(args.user_response.country).currencyCode}${formatter.format(obj['market_value'])}`;

        } else {
            const error = new Error(obj['last_response'])
            return error
        }
    } catch (err) {

        console.log('error', err.response);

    }
    /*

        return new Promise((resolve, reject) => {
            var options = {
                'method': 'POST',
                'url': BASEURL + 'CHECK BALANCE/',
                'headers': {
                    'Authorization': 'Bearer ' + accessToken,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    "credentials": {
                        "username": API_USERNAME,
                        "password": API_PASSWORD
                    },
                    "gateway_host": GATEWAY_HOST,
                    "api_token": "0b8688df-3bd6-484f-9ee7-1f8ca39eacb3",
                    "ip_address": "*************",
                    "chid": "14",
                    "msisdn": args.msisdn,
                    "trigger": "balance",
                    "investmentfund": fundAccount

                })

            };
            request(options, function (error, response) {
                if (error) {
                    console.error(error);
                    throw new Error(error);
                }
                const obj = JSON.parse(response.body)

                //log action
                // userUtils.store_activity_log("onBoard Interintel Balance  Request", "", args, args.msisdn,undefined , function(err, resp) {
                if (error) {
                    console.log("Error=> " + error)
                } else {
                    console.log("Resp => " + JSON.stringify(obj))
                }
            });
            // userUtils.store_activity_log("onBoard Interintel Balance Response", "", obj, args.msisdn, undefined, function(err, resp) {
            /!* if (error) {
                 console.log("Error=> " + error)
             } else {
                 console.log("Resp => " + resp)
             }*!/
        });
        //end log action
        if (obj['response_status'] === '00') {
            const res = fundAccount + ' ' + obj['market_value'] + ' '
            resolve(res)

        } else {
            const error = new Error(obj['last_response'])
            reject(error)
        }
    */

}

/**
 * Check is user exists in interintel
 */
const checkUserExists = async (msisdn) => {
    const accessToken = await getTokenData()
    console.log(`accessToken.. ${accessToken}`)
    return new Promise((resolve, reject) => {
        var options = {
            'method': 'POST',
            'url': BASEURL + 'DATA SOURCE/',
            'headers': {
                'Authorization': 'Bearer ' + accessToken,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "credentials": {
                    "username": TEST_API_USERNAME,
                    "password": API_PASSWORD
                },
                "gateway_host": GATEWAY_HOST,
                "api_token": "0b8688df-3bd6-484f-9ee7-1f8ca39eacb3",
                "ip_address": "*************",
                "chid": "14",
                "q": msisdn,
                "data_name": "completed_registrations"

            })

        };
        request(options, function (error, response) {
            if (error) {
                console.error(error);
                throw new Error(error);
            }
            let obj = JSON.parse(response.body)
            console.log('Check user ==> ' + JSON.stringify(obj))
            resolve(obj)
        })
    }).catch((error) => () => {
        console.log(error);
    });
}

/**
 * Validate users pin in interintel
 */
const validatePin = async (args) => {
    const accessToken = await getTokenData()
    const result = {}
    return new Promise((resolve, reject) => {
        var options = {
            'method': 'POST',
            'url': BASEURL + 'VALIDATE PIN/',
            'headers': {
                'Authorization': 'Bearer ' + accessToken,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "credentials": {
                    "username": API_USERNAME,
                    "password": API_PASSWORD
                },
                "gateway_host": GATEWAY_HOST,
                "api_token": "0b8688df-3bd6-484f-9ee7-1f8ca39eacb3",
                "ip_address": "*************",
                "chid": "14",
                "msisdn": args.msisdn,
                "pin": args.pin

            })

        };
        request(options, function (error, response) {
            if (error) {
                console.error(error);
                throw new Error(error);
            }
            let obj = JSON.parse(response.body)
            if (obj['response_status'] === '00') {
                result['success'] = true
                result['msisdn'] = args.msisdn
                result['message'] = obj['response']['validate_pin']
                resolve(result)

            } else {
                const error = new Error(obj['last_response'])
                reject(error)
            }

        });
    })
}

/**
 * Change pin in interintel
 */
const changePin = async (args) => {
    const accessToken = await getTokenData()
    const result = {}
    return new Promise((resolve, reject) => {
        var options = {
            'method': 'POST',
            'url': BASEURL + 'CHANGE PIN/',
            'headers': {
                'Authorization': 'Bearer ' + accessToken,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "credentials": {
                    "username": API_USERNAME,
                    "password": API_PASSWORD
                },
                "gateway_host": GATEWAY_HOST,
                "api_token": "0b8688df-3bd6-484f-9ee7-1f8ca39eacb3",
                "ip_address": "*************",
                "chid": "14",
                "msisdn": args.msisdn,
                "pin": args.pin,
                "new_pin": args.new_pin,
                "confirm_pin": args.confirm_pin


            })

        };
        request(options, function (error, response) {
            if (error) {
                console.error(error);
                throw new Error(error);
            }
            let obj = JSON.parse(response.body)

            //log action
            // userUtils.store_activity_log("Interintel Change PIN Request", "", args, args.msisdn,undefined , function(err, resp) {
            if (err) {
                console.log("Error=> " + err)
            } else {
                console.log("Resp => " + resp)
            }
        });
        //  userUtils.store_activity_log("Interintel Change Pin Response", "", obj, args.msisdn, undefined, function(err, resp) {
        if (err) {
            console.log("Error=> " + err)
        } else {
            console.log("Resp => " + resp)
        }
    });
    //end log action
    console.log('CHANGE  PIN RESPONSE =>> ' + JSON.stringify(obj))
    console.log('Response ' + obj['last_response'])
    if (obj['response_status'] === '00') {
        result['success'] = true
        result['msisdn'] = args.msisdn
        result['message'] = obj['last_response']
        resolve(result)

    } else {
        const error = new Error(obj['last_response'])
        reject(error)
    }

}


/**
 *  Validate OTP interintel
 */
const validateOneTimePin = async (args) => {
    const accessToken = await getTokenData()
    const result = {}
    return new Promise((resolve, reject) => {
        var options = {
            'method': 'POST',
            'url': BASEURL + 'CONFIRM ONE TIME PIN PENDING/',
            'headers': {
                'Authorization': 'Bearer ' + accessToken,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "credentials": {
                    "username": API_USERNAME,
                    "password": API_PASSWORD
                },
                "gateway_host": GATEWAY_HOST,
                "api_token": "0b8688df-3bd6-484f-9ee7-1f8ca39eacb3",
                "ip_address": "*************",
                "chid": "14",
                "msisdn": args.msisdn,
                "pin": args.onetime_pin,
                "confirm_pin": args.onetime_pin

            })

        };
        request(options, function (error, response) {
            if (error) {
                console.error(error);
                throw new Error(error);
            }
            let obj = JSON.parse(response.body)
            console.log('VALIDATE OTP RESPONSE =>> ' + JSON.stringify(obj))
            if (obj['response_status'] === '00') {
                result['success'] = true
                result['msisdn'] = args.msisdn
                result['message'] = obj['last_response']
                resolve(result)

            } else {
                result['success'] = false
                result['msisdn'] = args.msisdn
                result['message'] = obj['last_response']
                resolve(result)
            }

        });
    })
}


/**
 *  Create Pin invoking CHANGE PIN/
 */
const createPin = async (args) => {
    const accessToken = await getTokenData()
    const result = {}
    return new Promise((resolve, reject) => {
        var options = {
            'method': 'POST',
            'url': BASEURL + 'CHANGE PIN/',
            'headers': {
                'Authorization': 'Bearer ' + accessToken,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "credentials": {
                    "username": API_USERNAME,
                    "password": API_PASSWORD
                },
                "gateway_host": GATEWAY_HOST,
                "api_token": "0b8688df-3bd6-484f-9ee7-1f8ca39eacb3",
                "ip_address": "*************",
                "chid": "14",
                "api_token": "0b8688df-3bd6-484f-9ee7-1f8ca39eacb3",
                "ip_address": "*************",
                "chid": "14",
                "msisdn": args.msisdn,
                "pin": args.pin,
                "confirm_pin": args.confirm_pin

            })

        };
        request(options, function (error, response) {
            if (error) {
                console.error(error);
                throw new Error(error);
            }
            let obj = JSON.parse(response.body)
            console.log('VALIDATE  OTP RESPONSE =>> ' + JSON.stringify(obj))
            if (obj['response_status'] === '00') {
                result['success'] = true
                result['msisdn'] = args.msisdn
                resolve(result)

            } else {
                const error = new Error(obj['response']['overall_status'])
                reject(error)
            }

        });
    })
}


/**
 *   FETCHING FUNDING ACCOUNTS interintel
 */
const fetchFundingAccounts = async (args) => {
    const accessToken = await getTokenData()
    const result = {}

    console.log("dddaaata => " + JSON.stringify(args))
    let params = {
        "credentials": {
            "username": API_USERNAME,
            "password": API_PASSWORD
        },
        "gateway_host": GATEWAY_HOST,
        "api_token": "0b8688df-3bd6-484f-9ee7-1f8ca39eacb3",
        "ip_address": "*************",
        "chid": "14",
        "msisdn": args.msisdn,
        "data_name": "investment_profile.investment_fund"

    }

    let response = await axios.post(BASEURL + 'DATA SOURCE/', params,
        {
            'headers': {
                'Authorization': 'Bearer ' + accessToken,
                'Content-Type': 'application/json'
            }
        });

    const obj = response.data
    console.log(obj['response']['data_source']['lines'])    

    // console.log('obj FF', obj);
    if (obj['response_status'] === '00') {
        return obj['response']['data_source']['lines']

    } else {
        return new Error(obj['response']['overall_status'])

    }

    /*return new Promise((resolve, reject) => {
        var options = {
            'method': 'POST',
            'url': BASEURL + 'DATA SOURCE/',
            'headers': {
                'Authorization': 'Bearer ' + accessToken,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "credentials": {
                    "username": API_USERNAME,
                    "password": API_PASSWORD
                },
                "gateway_host": GATEWAY_HOST,
                "api_token": "0b8688df-3bd6-484f-9ee7-1f8ca39eacb3",
                "ip_address": "*************",
                "chid": "14",
                "msisdn": args.msisdn,
                "data_name": "investment_profile.investment_fund"

            })

        };
        request(options, function (error, response) {
            if (error) {
                console.error(error);
                throw new Error(error);
            }
            const obj = JSON.parse(response.body)
            if (obj['response_status'] === '00') {
                const accounts = obj['response']['data_source']['lines']

                console.log('Accounts =>> ' + JSON.stringify(accounts))
                resolve(accounts)

            } else {
                const error = new Error(obj['response']['overall_status'])
                reject(error)
            }

        });
    })*/
}

/**
 * Deposit Funds  in interintel INVEST api
 */
const despositFunds = async (args, callback) => {
    const accessToken = await getTokenData()
    const result = {}


    let params = {
        "credentials": {
            "username": API_USERNAME,
            "password": API_PASSWORD
        },
        "gateway_host": GATEWAY_HOST,
        "api_token": "0b8688df-3bd6-484f-9ee7-1f8ca39eacb3",
        "ip_address": "*************",
        "msisdn": args.msisdn,
        "amount": args.amount,
        "chid": 14,
        "product_item_id": "100067",
        "investmentfund": "Money Market",
        "payment_method": "M-PESA",
        "sourceoffund": args.sourceoffund,
        "trigger": "sale"

    }
    try {
        let response = await axios.post(BASEURL + 'INVEST/', params,
            {
                'headers': {
                    'Authorization': 'Bearer ' + accessToken,
                    'Content-Type': 'application/json'
                }
            });
        const obj = response.data
        console.log('obj FF', JSON.stringify(obj));
        if (obj['response_status'] === '00') {
            callback({
                "error": false,
                "user_message": "Please enter your M-PESA PIN when prompted, then wait for SMS confirmation.",
                "system_message": null
            })

        } else {
            // return new Error(obj['response']['overall_status'])
            callback({
                "error": true,
                "user_message": "A problem occurred while issuing your investment request. Please try again",
                "system_message": null
            })

        }
    } catch (e) {
        callback({
            "error": true,
            "user_message": "A problem occurred while issuing your investment request. Please try again",
            "system_message": e.message
        })
    }


    /*return new Promise((resolve, reject) => {
        var options = {
            'method': 'POST',
            'url': BASEURL + 'INVEST/',
            'headers': {
                'Authorization': 'Bearer ' + accessToken,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "credentials": {
                    "username": API_USERNAME,
                    "password": API_PASSWORD
                },
                "gateway_host": GATEWAY_HOST,
                "api_token": "0b8688df-3bd6-484f-9ee7-1f8ca39eacb3",
                "ip_address": "*************",
                "msisdn": args.msisdn,
                "amount": args.amount,
                "chid": 14,
                "product_item_id": "100067",
                "investmentfund": "Money Market",
                "payment_method": "M-PESA",
                "sourceoffund": args.sourceoffund,
                "trigger": "sale"

            })

        };
        request(options, function (error, response) {
            if (error) {
                console.error(error);
                throw new Error(error);
            }
            let obj = JSON.parse(response.body)
            console.log('Deposit Funds RESPONSE =>> ' + JSON.stringify(obj))
            if (obj['response_status'] === '00') {
                result['success'] = true
                result['msisdn'] = args.msisdn
                result['message'] = obj['last_response']
                resolve(result)

            } else {
                console.error('Error: ' + obj['last_response']);
                const error = new Error(obj['last_response'])
                reject(error)
            }

        });
    }).catch((e) => {
        console.error('Catch Error: ' + e);
        result['success'] = false
        result['message'] = e
        return result
    })*/
}

const getTokenData = async () => {
    return new Promise((resolve, reject) => {
        var options = {
            'method': 'POST',
            'url': BASEURL + 'get-token',
            'headers': {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "username": "test",
                "password": "y6w666zmJX5LFLMM"
            })

        };
        request(options, function (error, response) {
            if (error) {
                console.error(error);
                throw new Error(error);
            }
            let obj = JSON.parse(response.body)
            resolve(obj['token'])

        });
    })

}

/**
 *  Send SMS interintel
 */
const send_OTP = async (args) => {
    const accessToken = await getTokenData()
    const result = {}
    return new Promise((resolve, reject) => {
        var options = {
            'method': 'POST',
            'url': BASEURL + 'SEND SMS/',
            'headers': {
                'Authorization': 'Bearer ' + accessToken,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "credentials": {
                    "username": API_USERNAME,
                    "password": API_PASSWORD
                },
                "gateway_host": GATEWAY_HOST,
                "api_token": "0b8688df-3bd6-484f-9ee7-1f8ca39eacb3",
                "ip_address": "*************",
                "chid": "14",
                "msisdn": args.msisdn,
                "message": args.onetime_pin

            })

        };
        request(options, function (error, response) {
            if (error) {
                console.error(error);
                throw new Error(error);
            }
            let obj = JSON.parse(response.body)
            if (obj['response_status'] === '00') {
                result['success'] = true
                result['message'] = obj['response']['send_notification']
                result['msisdn'] = args.msisdn
                resolve(result)
            } else {
                const error = new Error(obj['response']['overall_status'])
                reject(error)
            }

        });
    })
}

const fetchStatements = async (args,callback) => {
    
      console.log(">>>>>>>>>>>>>> fetchStatements <<<<<<<<<<<<<<< " + args.account)

      let statementMessage = "Your statement\n===========\n";
      let result = {}
      try{
  
  
           const statement = await getStatements(args.account)
  
           if(statement){
              let statementData = JSON.stringify(statement);
              const obj = JSON.parse(statementData);
              console.log(">>> Statement Txns  => " + obj.transactions.length)
  
              for(let k =0; k < obj.transactions.length; k++){
                  let x = k + 1;
  
                 if(k < STATEMENT_TRANSACTIONS){
                 //  statementMessage +=   "\n\n Transaction " +x+" \n Transaction Ref:" +obj.transactions[k].transactionNo+"\n Amount: "+obj.transactions[k].totalValue+"\n Account: " +obj.transactions[k].clientAccNo+ "\n Description: "+obj.transactions[k].unitTranTypeDescr+"\n Date:" +obj.transactions[k].valueDate
                 var d = new Date(obj.transactions[k].valueDate);
  
                 statementMessage += 'Value date: ' + d.toLocaleString("en-GB") + '\n'
                                  + 'Unit Transaction Type: ' + obj.transactions[k].unitTranTypeDetDescr + '\n'
                                  + 'Net Amount: ' + obj.transactions[k].netAmount + '\n'
                                  + 'Unit Balance: ' + obj.transactions[k].unitBalance + '\n'
                                  + '----------------' + '\n'
                     }
                  }
              }
        
  
           console.log("statements msg" + statementMessage)
           result = {'error': false, 'user_message': statementMessage, 'msisdn': args.msisdn}
  
           return result
  
      }catch(e){
          result = {
               'error': true,
               'user_message': 'Error getting statement data',
               'system_message': null,
               'msisdn': args.msisdn
               }
          return result
  
      }
  }

  const getStatements = async (clientAccNo) => {

    console.log(">>>>>>>>> getStatements <<<<<<<<<<<" + clientAccNo)

    var todayDate = new Date().toISOString().slice(0, 10);

    const accessToken = await getTokenData()
    const response = await axios({
        url: BASEURL + 'statement',
        method: 'POST',
        headers: {
         'Authorization': 'Bearer ' + accessToken,
         'Content-Type': 'application/json'
          },
         data: { "fromDate": '2010-01-01',
                 "toDate": todayDate,
                 "clientAccNo": clientAccNo
         }
      });
     const obj = response.data
     console.log(">>>>>>>>>> response data <<<<<<<<<<<" + JSON.stringify(obj))
     
     return obj
}


passport.serializeUser(function (msisdn, done) {
    done(null, msisdn);
});

passport.deserializeUser(function (msisdn, done) {
    done(null, msisdn);

});

module.exports = {
    registerUser: registerUser,
    checkUserExists: checkUserExists,
    checkBalance: checkBalance,
    validatePin: validatePin,
    changePin: changePin,
    send_OTP: send_OTP,
    validateOneTimePin: validateOneTimePin,
    createPin: createPin,
    despositFunds: despositFunds,
    fetchStatements: fetchStatements,
    fetchFundingAccounts: fetchFundingAccounts,

}
