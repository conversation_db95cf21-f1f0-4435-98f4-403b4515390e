var express = require('express');


const config = require(__dirname + '/../config/config');
const request = require('request');

const fs = require('fs');
const FileType = require('file-type');
const readChunk = require('read-chunk');


const util = require('util');
const https = require('https');

const SessionService = require('../services/SessionService');

const download = require('image-downloader');


let db = require('../models');

var country_code;
var ticket_no;


async function API_Function(phone_number, line_of_business, request_category, source_channel, policy_reg_no, bank_subcategory, is_healthworker, claim_type) {
    console.log('API_Function')
    var options = {
        'method': 'POST',
        'url': "https://selfservice.uapoldmutual.com/apps/tickets",
        'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': 'ci_session=s64e0n487l5k149g8bfr9v6mn5jfiibk'
        },
        form: form_builder(phone_number, line_of_business, request_category, source_channel, policy_reg_no, bank_subcategory, is_healthworker, claim_type)
    };


    process_request(options);


}


async function SMS_Function(phone_number, option) {
    console.log('SMS_Function')
    var options = {
        'method': 'POST',
        'url': "https://selfservice.uapoldmutual.com/apps/sms",
        'headers': {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        form: {
            'phone_number': phone_number,
            'option': option
        }
    };


    process_request(options);


}

async function CheckIfHWUserExists(phone_number, callback) {
    console.log('CheckIfHWUserExists')

    get_User_Country(phone_number, function(error, response) {
        if (error) {
            callback(true, error)
        } else {
            country_code = response;


            var options = {
                'method': 'POST',
                'url': "https://selfservice.uapoldmutual.com/apps/checkHealthworker",
                'headers': {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                form: {
                    'phone_number': phone_number,
                    'country_code': country_code
                }
            }
            try {
                request(options, function(error, response) {

                    if (error) {
                        callback(true, error)

                    } else {
                        if (response.statusCode === 400)
                            callback(false, true)
                        else if (response.statusCode === 200) {
                            callback(false, false)
                        }
                    }

                });
            } catch (e) {
                callback(true, error)
            }
        }

    });

}


// This should work in node.js and other ES5 compliant implementations.
function isEmptyObject(obj) {
    return !Object.keys(obj).length;
}

// This should work both there and elsewhere.
function isEmptyObject(obj) {
    for (var key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            return false;
        }
    }
    return true;
}


async function check_ticket_no(phone_no, callback) {
    let ticket_no;
    try {

        SessionService.checkActiveWhatsAppTicketNo(phone_no, function(err, resp) {
            if (err) {

                throw err;
            } else {


                const string = JSON.stringify(resp);

                const objectValue = JSON.parse(string);


                if (isEmptyObject(objectValue)) {

                    ticket_no = '0';
                    callback(false, ticket_no);
                } else {
                    ticket_no = objectValue[0].ticket_no;
                    callback(false, ticket_no);
                }

            }


        })


    } catch (e) {

        throw e;
    }
}

/*
Create a new whats app user in the  wa_user table
 */
async function create_wa_user(phone_no, whatsapp_name, country, callback) {
    let whatsapp_user_name;
    try {


        if (typeof whatsapp_name == 'string') {
            whatsapp_user_name = " " + whatsapp_name
        } else {
            whatsapp_user_name = " "
        }


        SessionService.getWhatsAppCountry(phone_no, function(error, response) {


            if (error) {

                throw error;
            } else {


                const string = JSON.stringify(response);

                const objectValue = JSON.parse(string);


                if (isEmptyObject(objectValue)) {
                    /*
                    Create a new WhatsApp User
                     */
                    SessionService.saveWhatsAppUser(phone_no, whatsapp_user_name, country, function(err, resp) {
                        if (err) {

                            throw err;
                        }
                        callback(false, resp);
                    });


                } else {
                    /*
                     * update the  current whatsapp user details
                     *
                     * */

                    SessionService.updateWhatsAppUser(phone_no, whatsapp_user_name, country, function(error2, response2) {

                        if (error2) {
                            throw error2;
                        }
                        callback(false, response2);
                    });


                }

            }


        });


        ;
    } catch (e) {

        throw e;
    }

}

/*
 * Create a new session for our already existing WhatsApp users
 * */

async function new_wa_user_session(phone_no, ticket_no, level, input, unique_identifier, expected_input_type, path_taken, callback) {
    try {

        SessionService.saveWhatsAppUserSession(phone_no, ticket_no, level, input, unique_identifier, expected_input_type, path_taken, function(err, resp) {
            if (err) {

                throw err;
            }


            callback(false, resp);
        })
    } catch (e) {

        throw e;
    }
}

/*
Get ticket no ,
1. Check if an active ticket no exists,
2. If exists, pick it and use it
3. If it doesnt exists, create a new one and insert it into the
 */
async function get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, callback) {
    console.log('get_ticket_no')
    try {

        var ticket_no;

        check_ticket_no(phone_no, function(err, response) {
            if (err) {
                throw err;
            } else if (response == '0') {

                generate_new_ticket(phone_no, source_channel, input, function(err, ticket_no) {
                    if (err) {

                        callback(true, ticket_no)
                    } else {


                        new_wa_user_session(phone_no, ticket_no, level, input, unique_identifier, expected_input_type, path_taken, function(err, response) {
                            if (err) {
                                callback(true, 'Error creating user')
                            } else {
                                callback(false, ticket_no)
                            }
                        });
                    }

                })


            } else {


                callback(false, response);
                //  res.json(response);

            }
        });


    } catch (e) {

        throw e;
    }

}

async function generate_new_ticket(phone_number, source_channel, input, callback) {

    try {


        /*get_User_Country(phone_number, function (error, response) {
            if (error) {
                throw error;
            }

            country_code = response;

        });*/


        get_User_Country(phone_number, function(error, response) {
            if (error) {
                throw error;
            }

            let country_code = response;


            const options = {
                'method': 'POST',
                'url': 'https://selfservice.uapoldmutual.com/apps/getticket',
                'headers': {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                form: {
                    'phone_number': phone_number,
                    'source_channel': source_channel,
                    'request_category': input,
                    'country_code': country_code

                }
            };
            request(options, function(error, resp) {
                if (error) {
                    console.log(phone_number + ' | Ticket response data: ' + resp)
                    callback(true, 'We encountered an issue processing your request. Please try again')
                } else {

                    try {
                        console.log(phone_number + ' | Ticket response data: ' + resp.body)
                        const json_response = JSON.parse(resp.body);
                        let ticket_no = json_response.ticket;
                        callback(false, ticket_no);
                    } catch (e) {
                        console.log(phone_number + ' | Error generating new ticket, please try again')
                        callback(true, 'We encountered an issue processing your request. Please try again')
                            // callback(false, 1);

                    }


                }
            });


        });


    } catch (e) {

        throw e;
    }
}


async function ticket_session(phone_no, unique_identifier, input, level, expected_input_type, path_taken, callback) {

    let source_channel = config.whatsApp.source_channel;

    // console.log('********* unique_identifier : ' + unique_identifier)
    if (unique_identifier.includes('health_worker_')) {

        if (unique_identifier === "health_worker_reg_no_public" || unique_identifier === "health_worker_reg_no_private") {
            let key = "license_number";


            health_worker_session(phone_no, key, input, function(error, response) {
                if (error) {

                    callback(true, error)
                } else
                    callback(false, response);
            });
        } else if (unique_identifier === "health_worker_profession_public") {
            let key = "profession";


            health_worker_session(phone_no, 'profession', '', function(error, response) {});

            health_worker_session(phone_no, 'profession_public', input, function(error, response) {
                if (error) {
                    callback(true, error)
                } else
                    callback(false, response);
            });
        } else if (unique_identifier === "health_worker_sector") {
            let key = "sector";
            let _input = input;
            if (input === '1') {
                _input = 'Public'
            } else if (input === '2') {
                _input = 'Private'
            }

            health_worker_session(phone_no, key, _input, function(error, response) {
                if (error) {

                    callback(true, error)
                } else
                    callback(false, response);
            });
        } else if (unique_identifier === "health_worker_profession_private") {

            health_worker_session(phone_no, 'profession', input, function(error, response) {});

            health_worker_session(phone_no, 'profession_public', '', function(error, response) {
                if (error) {
                    callback(true, error)
                } else
                    callback(false, response);
            });
        } else if (unique_identifier === "health_worker_name") {
            let key = "name";


            health_worker_session(phone_no, key, input, function(error, response) {
                if (error) {

                    callback(true, error)
                } else
                    callback(false, response);
            });
        } else if (unique_identifier === "health_worker_national_id") {
            let key = "id_number";
            health_worker_session(phone_no, key, input, function(error, response) {
                if (error) {

                    throw error;
                }
                callback(false, response);
            });
        } else if (unique_identifier === "health_worker_facility") {
            let key = "facility";
            health_worker_session(phone_no, key, input, function(error, response) {
                if (error) {

                    throw error;
                }
                callback(false, response);
            });
        } else if (unique_identifier === "health_worker_email") {
            let key = "email";
            health_worker_session(phone_no, key, input, function(error, response) {
                if (error) {

                    throw error;
                }
                callback(false, response);
            });
        } else if (unique_identifier === "health_worker_beneficiary_name") {
            let key = "beneficiary_name";
            health_worker_session(phone_no, key, input, function(error, response) {
                if (error) {

                    throw error;
                }
                callback(false, response);
            });
        } else if (unique_identifier === "health_worker_beneficiary_phone_no") {
            let key = "beneficiary_phone_number";
            health_worker_session(phone_no, key, input, function(error, response) {
                if (error) {

                    throw error;
                }
                callback(false, response);
            });
        } else if (unique_identifier === "health_worker_registration_end_message") {
            let key = "beneficiary_phone_number";
            health_worker_session(phone_no, key, input, function(error, response) {
                if (error) {

                    throw error;
                }
                callback(false, response);
            });
        } else if (unique_identifier === "health_worker_register_claim_type") {
            let key = "claim_type";


            get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, function(error, response) {
                if (error) {

                    throw error;
                } else {
                    let ticket_no = response;


                    let claim_type = "claim_type";


                    //Proceed to save in the  wa_sessions_date table

                    SessionService.saveWhatsAppSessionData(phone_no, claim_type, ticket_no, input, function(err, response) {
                        if (err) {

                            throw err;
                        }
                        callback(false, response);
                    });


                }

            })


        } else if (unique_identifier === "provide_details_health_worker_hosp_claim_id_public" || unique_identifier === "provide_details_health_worker_hosp_claim_id_private") { // Hospital Benefit
            let key = "policy_reg_no";

            input = 0; // since we aren't collecting ID text from the user, we are just taking their ID photo. This is a mandatory field to submit form.
            get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, function(error, response) {
                if (error) {

                    throw error;
                } else {
                    let ticket_no = response;


                    let claim_type = "policy_reg_no";


                    //Proceed to save in the  wa_sessions_date table

                    SessionService.saveWhatsAppSessionData(phone_no, claim_type, ticket_no, input, function(err, response) {
                        if (err) {

                            throw err;
                        }
                        callback(false, response);
                    });


                }

            })


        } else if (unique_identifier === "provide_details_health_worker_last_claim_id_hw") { // Last Benefit has no ID card/policy no entered. We'll use 0
            let key = "policy_reg_no";


            get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, function(error, response) {
                if (error) {

                    throw error;
                } else {
                    let ticket_no = response;


                    let claim_type = "policy_reg_no";


                    //Proceed to save in the  wa_sessions_date table

                    SessionService.saveWhatsAppSessionData(phone_no, claim_type, ticket_no, 0, function(err, response) {
                        if (err) {

                            throw err;
                        }
                        callback(false, response);
                    });


                }

            })


        } else {
            health_worker_session(phone_no, unique_identifier, input, function(error, response) {
                if (error) {

                    throw error;
                }
                callback(false, response);
            });

        }
    } else {


        if (unique_identifier === "register_claim" || unique_identifier === "policy_status" ||
            unique_identifier === "buy_insurance" || unique_identifier === "make_insurance_payment" ||
            unique_identifier === "invest_with_us" || unique_identifier === "health_worker" ||
            unique_identifier === "update_personal_details" || unique_identifier === "covid_update_message" ||
            unique_identifier === "bank_with_us") {


            /*
    * Request Category Option
    *
            phone_number, source_channel, request_category,level,input,unique_identifier,expected_input_type,path_taken,
             */
            await
            get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, function(error, response) {
                if (error) {

                    callback(true, 'Get Ticket Error')
                }
                if (response == null) { // No ticket found
                    generate_new_ticket(phone_no, source_channel, input, function(err, res) {
                        let ticket_no;
                        if (err) {

                            callback(true, res)
                        } else {


                            ticket_no = res;


                            new_wa_user_session(phone_no, ticket_no, level, input, unique_identifier, expected_input_type, path_taken, function(err, response) {
                                if (err) {

                                    throw err;
                                }


                            });
                            callback(false, ticket_no)
                        }

                    })
                } else {
                    let ticket_no = response;
                    let request_category = "request_category"


                    //Proceed to save in the  wa_sessions_date table
                    SessionService.saveWhatsAppSessionData(phone_no, request_category, ticket_no, input, function(err, response) {
                        if (err) {

                            throw err;
                        }

                        callback(false, response);
                    })
                }
            })


        } else if (unique_identifier == "bank_with_us") {

            await
            get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, function(error, response) {
                if (error) {

                    throw error;
                }
                let ticket_no = response;
                let line_of_business = "line_of_business";

                //Proceed to save in the  wa_sessions_date table
                SessionService.saveWhatsAppSessionData(phone_no, line_of_business, ticket_no, input, function(err, response) {
                    if (err) {

                        throw err;
                    }

                    callback(false, response);
                })
            })

        } else if (unique_identifier === "bank_with_us_option_success_end_message") {


            await
            get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, function(error, response) {
                if (error) {

                    throw error;
                }
                let ticket_no = response;
                let bank_subcategory = "bank_subcategory";

                //Proceed to save in the  wa_sessions_date table
                SessionService.saveWhatsAppSessionData(phone_no, bank_subcategory, ticket_no, input, function(err, response) {
                    if (err) {

                        throw err;
                    }

                    callback(false, response);
                })
            })

        } else if (unique_identifier === "provide_details_policy_status_car" || unique_identifier === "provide_details_policy_status_health" ||
            unique_identifier === "provide_details_policy_status_other" || unique_identifier === "provide_details_policy_status_life" ||
            unique_identifier === "provide_details_payment_health" || unique_identifier === "provide_details_payment_life" ||
            unique_identifier === "provide_details_payment_car" || unique_identifier === "provide_details_payment_other" ||
            unique_identifier === "provide_details_car" || unique_identifier === "provide_details_other" ||
            unique_identifier === "provide_details_life" || unique_identifier === "provide_details_health") {


            await
            get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, function(error, response) {
                if (error) {

                    throw error;
                }
                let ticket_no = response;
                let policy_reg_no = "policy_reg_no";

                //Proceed to save in the  wa_sessions_date table
                SessionService.saveWhatsAppSessionData(phone_no, policy_reg_no, ticket_no, input, function(err, response) {
                    if (err) {

                        throw err;
                    }

                    callback(false, response);
                })
            })

        } else if (unique_identifier === "new_customer_verification") {

            await
            get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, function(error, response) {
                if (error) {

                    throw error;
                }
                let ticket_no = response;
                let first_name = "request_category";

                //Proceed to save in the  wa_sessions_date table
                SessionService.saveWhatsAppSessionData(phone_no, first_name, ticket_no, input, function(err, response) {
                    if (err) {

                        throw err;
                    }

                    callback(false, response);
                })
            })
        } else if (unique_identifier === "verify_enter_name") {

            await
            get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, function(error, response) {
                if (error) {

                    throw error;
                }
                let ticket_no = response;
                let first_name = "line_of_business";

                //Proceed to save in the  wa_sessions_date table
                SessionService.saveWhatsAppSessionData(phone_no, first_name, ticket_no, input, function(err, response) {
                    if (err) {

                        throw err;
                    }

                    callback(false, response);
                })
            })
        } else if (unique_identifier === "verify_id_passport") {

            await
            get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, function(error, response) {
                if (error) {

                    throw error;
                }
                let ticket_no = response;
                let first_name = "name";

                //Proceed to save in the  wa_sessions_date table
                SessionService.saveWhatsAppSessionData(phone_no, first_name, ticket_no, input, function(err, response) {
                    if (err) {

                        throw err;
                    }

                    callback(false, response);
                })
            })
        } else if (unique_identifier === "verify_id_photo") {

            await
            get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, function(error, response) {
                if (error) {

                    throw error;
                }
                let ticket_no = response;
                let id_number = "id_number";

                //Proceed to save in the  wa_sessions_date table
                SessionService.saveWhatsAppSessionData(phone_no, id_number, ticket_no, input, function(err, response) {
                    if (err) {

                        throw err;
                    }

                    callback(false, response);
                })
            })
        } else if (unique_identifier === "signup_phone_no") {

            await
            get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, function(error, response) {
                if (error) {

                    throw error;
                }
                let ticket_no = response;
                let id_number = "phone_number";

                //Proceed to save in the  wa_sessions_date table
                SessionService.saveWhatsAppSessionData(phone_no, id_number, ticket_no, input, function(err, response) {
                    if (err) {

                        throw err;
                    }

                    callback(false, response);
                })
            })
        } else if (unique_identifier === "claim_health" || unique_identifier === "claim_car" || unique_identifier === "claim_life" ||
            unique_identifier === "claim_other" || unique_identifier === "policy_status_health" || unique_identifier === "policy_status_car" || unique_identifier === "policy_status_life" ||
            unique_identifier === "policy_status_other" || unique_identifier === "buy_insurance_health_success_end_message" || unique_identifier === "buy_insurance_car_success_end_message" ||
            unique_identifier === "buy_insurance_life_success_end_message" || unique_identifier === "buy_insurance_other_success_end_message" || unique_identifier === "make_insurance_payment_health" ||
            unique_identifier === "make_insurance_payment_car" || unique_identifier === "make_insurance_payment_life" || unique_identifier === "make_insurance_payment_other" || unique_identifier === "bought_health" ||
            unique_identifier === "bought_car" || unique_identifier === "bought_life" || unique_identifier === "bought_other" || unique_identifier === "bought_other") {
            await
            get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, function(error, response) {
                if (error) {

                    callback(true, 'Error updating ticket session');
                }
                let ticket_no = response;
                let line_of_business = "line_of_business";

                //Proceed to save in the  wa_sessions_date table
                SessionService.saveWhatsAppSessionData(phone_no, line_of_business, ticket_no, input, function(err, response) {
                    if (err) {

                        callback(true, 'Error updating ticket session');
                    } else
                        callback(false, response);
                })
            })
        } else if (unique_identifier === "provide_details_health_claim_mpesa_no" || unique_identifier === "provide_details_health_claim_kra_pin" || unique_identifier === "provide_details_health_claim_bank_name" ||
            unique_identifier === "provide_details_health_claim_bank_branch" || unique_identifier === "provide_details_health_claim_bank_account_name" || unique_identifier === "provide_details_health_claim_bank_account_number") {
            await
            get_ticket_no(phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel, function(error, ticket_no) {
                if (error) {

                    callback(true, 'Error updating ticket session');
                } else

                //Proceed to save in the  wa_sessions_date table
                    SessionService.saveWhatsAppSessionData(phone_no, unique_identifier, ticket_no, input, function(err, response) {
                    if (err) {

                        callback(true, 'Error updating ticket session');
                    } else
                        callback(false, response);
                })

            })
        } else {
            callback(false, 'Ignored')
        }


    }


}


async function close_user_WhatsApp_Session(phone_no, ticket_no, callback) {


    SessionService.closeWhatsAppUserSession({
        phone_no: phone_no,
        ticket_no: ticket_no,
        status: 'COMPLETE'
    }, ticket_no, function(err, session_response) {
        if (err) {
            //console.error(err);
            res.json(err);
        }


        callback(false, session_response);


    });


}

async function get_User_Country(phone_no, callback) {

    SessionService.getWhatsAppCountry(phone_no, function(err, resp) {
        if (err) {

            throw err;
        } else {


            const string = JSON.stringify(resp);

            const objectValue = JSON.parse(string);


            if (isEmptyObject(objectValue)) {

                country_code = 'KE';
                callback(false, country_code);
            } else {
                let country = objectValue.country;
                let country_code;


                if (country === "Kenya" || country == "kenya" || country == "KENYA" || country == "KE" || country == "ke") {
                    country_code = "KE";


                    callback(false, country_code);
                } else if (country == "uganda") {
                    country_code = "UG";

                    callback(false, country_code);
                } else if (country == "rwanda") {
                    country_code = "RW";

                    callback(false, country_code);
                } else if (country == "tanzania") {
                    country_code = "TZ";

                    callback(false, country_code);
                } else if (country == "south_sudan") {
                    country_code = "SS";

                    callback(false, country_code);
                } else {
                    country_code = "KE";
                    callback(false, country_code);
                }


            }

        }


    })


}


async function healthcare_worker_claim_request(ticket_no, callback) {
    console.log('healthcare_worker_claim_request')
    let is_healthworker = '1';
    let source_channel = config.whatsApp.source_channel;
    let line_of_business = '1';
    let request_category = '1';
    let bank_subcategory = '';
    let claim_type;
    let policy_reg_no;


    SessionService.getWhatsAppSessionData(ticket_no, function(err, resp) {
        if (err) {

            throw err;
        }


        const string = JSON.stringify(resp);
        const objectValue = JSON.parse(string);


        if (isEmptyObject(objectValue)) {

            callback(false, ticket_no);
        } else {
            let phone_no = ''
            objectValue.forEach(element => {

                if (element.key == "claim_type") {
                    claim_type = element.session_data;
                }

                if (element.key == "policy_reg_no") {
                    policy_reg_no = element.session_data;
                }
                phone_no = element.phone_no;

            })


            if (typeof request_category !== 'undefined' && typeof policy_reg_no !== 'undefined' && typeof line_of_business !== 'undefined') {


                get_User_Country(phone_no, function(error, response) {
                    if (error) {
                        callback(true, error);
                    } else {
                        country_code = response;


                        let form = {
                            'phone_number': phone_no,
                            'source_channel': source_channel,
                            'request_category': request_category,
                            'ticket_no': ticket_no,
                            'policy_reg_no': policy_reg_no,
                            'line_of_business': line_of_business,
                            'bank_subcategory': bank_subcategory,
                            'is_healthworker': is_healthworker,
                            'claim_type': claim_type,
                            'country_code': country_code

                        }


                        const options = {
                            'method': 'POST',
                            'url': 'https://selfservice.uapoldmutual.com/apps/updateticket',
                            'headers': {
                                'Content-Type': 'application/x-www-form-urlencoded'
                            },
                            form: form
                        };
                        console.log('@healthcare_worker_claim_request Request: ' + JSON.stringify(form))
                        request(options, function(error, response) {
                            if (error) {
                                callback(true, error)
                            } else {


                                const json_response = JSON.parse(response.body);

                                console.log(phone_no + ' @healthcare_worker_claim_request Response: ' + json_response)

                                callback(false, response.body);
                            }

                        });
                    }
                });


            }
        }


    });


    // callback(false, resp);

    /*    }

    })
*/

}


async function end_health_worker_session(phone_no, callback) {

    SessionService.CompleteWhatsAppHealthWorker(phone_no, function(err, resp) {
        if (err) {

            callback(true, 'Error ending health worker @end_health_worker_session')
        } else
            callback(false, resp);

    });

}


async function health_worker_session(phone_no, key, value, callback) {

    SessionService.saveWhatsAppHealthWorker(phone_no, key, value, function(error, response) {
        if (error) {

            callback(true, 'Error saving health worker session @health_worker_session')
        } else
            callback(false, response);

    })

}

async function update_ticket(ticket_no, callback) {


    SessionService.getWhatsAppSessionData(ticket_no, function(err, resp) {
        if (err) {

            throw err;
        }

        var phone_number = '';

        let source_channel = config.whatsApp.source_channel;
        let request_category;
        let bank_subcategory;
        let policy_reg_no;
        let line_of_business;
        let claim_type;
        let license_number;
        let is_healthworker = '0';


        const string = JSON.stringify(resp);
        const objectValue = JSON.parse(string);


        if (isEmptyObject(objectValue)) {

            ticket_no = '0';
            callback(false, ticket_no);
        } else {


            let data_form;


            objectValue.forEach(element => {

                // console.log("Form Data => " + element.key + ":" + element.session_data);


                if (element.key == "request_category") {
                    request_category = element.session_data;

                }

                if (element.key == "policy_reg_no") {
                    policy_reg_no = element.session_data;
                }

                if (element.key == "line_of_business") {
                    line_of_business = element.session_data;
                }

                if (element.key == "bank_subcategory") {
                    bank_subcategory = element.session_data;
                }


                if (element.key == "claim_type") {
                    claim_type = element.session_data;
                }

                if (element.key == "license_number") {
                    license_number = element.session_data;
                }


                phone_number = element.phone_no;


            });


            get_User_Country(phone_number, function(error, response) {
                let country_code;
                if (error) {
                    throw error;
                } else {


                    country_code = response;

                    /*console.log("Country code  => " + country_code);
                    console.log("Policy reg no  => " + policy_reg_no);
                    console.log("Bank sub category  => " + bank_subcategory);
                    console.log("Claim type => " + claim_type);*/


                    data_form = {
                        'phone_number': phone_number,
                        'source_channel': source_channel,
                        'request_category': request_category,
                        'ticket_no': ticket_no,
                        'policy_reg_no': policy_reg_no,
                        'line_of_business': line_of_business,
                        'bank_subcategory': bank_subcategory,
                        'is_healthworker': is_healthworker,
                        'claim_type': claim_type,
                        'country_code': country_code

                    };


                    const options = {
                        'method': 'POST',
                        'url': 'https://selfservice.uapoldmutual.com/apps/updateticket',
                        'headers': {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        form: data_form
                    };
                    request(options, function(error, response) {

                        // console.log(util.inspect(data_form, false, null, true /* enable colors */));
                        console.log(phone_number + ' | Requesting: Submitting ticket data');

                        if (error) {

                            callback(false, error);
                        } else {


                            const json_response = JSON.parse(response.body);

                            console.log(phone_number + ' | Response: ' + json_response);

                            callback(false, response.body);

                        }

                    });


                }


            });


            // console.log("Our Output array   #2 => " + data_form);


        }


    })


}

function create_form(key, value) {

}


async function checkCustomerVerification(phone_number, callback) {
    console.log('checkCustomerVerification')
    get_User_Country(phone_number, function(error, response) {
        if (error) {
            throw error;
        }

        country_code = response;


        const options = {
            'method': 'POST',
            'url': 'https://selfservice.uapoldmutual.com/apps/checkCustomerVerification',
            'headers': {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            form: {
                'phone_number': phone_number,
                'country_code': country_code

            }
        };
        request(options, function(error, response) {
            if (error) {
                callback(true, 'Error occurred')
            } else {


                callback(false, response.body);
            }
        });


    });


}


async function addCustomerVerification(ticket_no, callback) {
    console.log('addCustomerVerification')
    SessionService.getWhatsAppSessionData(ticket_no, function(err, resp) {
        if (err) {

            throw err;
        }


        const string = JSON.stringify(resp);
        const objectValue = JSON.parse(string);


        if (isEmptyObject(objectValue)) {

            ticket_no = '0';
            callback(false, ticket_no);
        } else {

            let source_channel = config.whatsApp.source_channel;

            let line_of_business;
            let id_number;
            let name;
            let phone_number;

            objectValue.forEach(element => {


                if (element.key == "name") {
                    name = element.session_data;
                }

                if (element.key == "line_of_business") {
                    line_of_business = element.session_data;
                }

                if (element.key == "id_number") {
                    id_number = element.session_data;
                }
                if (element.key == "customer_verification_phone") {
                    phone_number = element.session_data;
                }


                if (typeof name !== 'undefined' && typeof phone_number !== 'undefined' &&
                    typeof line_of_business !== 'undefined' && typeof id_number !== 'undefined' && typeof phone_number !== 'undefined') {


                    get_User_Country(phone_number, function(error, response) {
                        if (error) {
                            throw error;
                        }

                        country_code = response;


                        const options = {
                            'method': 'POST',
                            'url': 'https://selfservice.uapoldmutual.com/apps/addCustomerVerification',
                            'headers': {
                                'Content-Type': 'application/x-www-form-urlencoded'
                            },
                            form: {
                                'phone_number': phone_number,
                                'name': name,
                                'id_number': id_number,
                                'line_of_business': line_of_business,
                                'ticket_no': ticket_no,
                                'country_code': country_code

                            }
                        };
                        request(options, function(error, response) {
                            if (error) {
                                callback(true, error);
                            } else {


                                const json_response = JSON.parse(response.body);


                                callback(false, response.body);
                            }

                        });

                    });


                }


            });


            //callback(false, resp);
        }


    })


}


async function download_reg_media(file_url, phone_no, caption, callback) {
    const file_path = "./public/images/whatsapp/";
    const timestamp = Date.now();


    const options = {
        url: file_url,
        dest: file_path,
        headers: {
            'Authorization': 'App 8f2d9a954bdd5d6d75ff154542177e3b-b77c29c2-2575-4d43-8c1d-03c0d63bcbda'
        }
    }

    try {


        let filename;


        await download.image(options)
            .then(({ filename, image }) => {


                let type;
                (async() => {
                    const buffer = readChunk.sync(filename, 0, 4100);


                    type = await FileType.fromBuffer(buffer);


                    const file_type = type.ext;
                    const new_filename = file_path + "/" + phone_no + "_" + timestamp + "." + file_type;


                    rename_file(filename, new_filename, function(err, response) {
                        if (err) {

                            throw err;
                        } else {


                            save_media(phone_no, phone_no, new_filename, caption, file_url, function(err, resp) {
                                if (err) {

                                    throw err;
                                }

                                callback(false, resp);
                            });

                        }

                        // callback(false, response);

                    });


                })();


            })
            .catch((err) => console.error(err))


    } catch (e) {
        console.error(e);
    }


}

async function download_file(file_url, phone_no, ticket_no, caption, callback) {


    const file_path = "./public/images/whatsapp/";
    const timestamp = Date.now();


    const options = {
        url: file_url,
        dest: file_path,
        headers: {
            'Authorization': 'App 8f2d9a954bdd5d6d75ff154542177e3b-b77c29c2-2575-4d43-8c1d-03c0d63bcbda'
        }
    }

    try {


        let filename;
        //
        // console.log(options)
        await download.image(options)
            .then(({ filename, image }) => {


                let type;
                (async() => {
                    const buffer = readChunk.sync(filename, 0, 4100);


                    type = await FileType.fromBuffer(buffer);


                    const file_type = type.ext;
                    const new_filename = file_path + "/" + phone_no + "_" + ticket_no + "_" + timestamp + "." + file_type;


                    rename_file(filename, new_filename, function(err, response) {
                        if (err) {

                            throw err;
                        } else {

                            save_media(ticket_no, phone_no, new_filename, caption, file_url, function(err, resp) {
                                if (err) {

                                    throw err;
                                }

                                callback(false, resp);
                            });

                        }

                        // callback(false, response);

                    });


                })();


            })
            .catch((err) => console.error(err))


    } catch (e) {
        console.error(e);
    }


}


async function send_file(file_name, file_url) {


    var options = {
        'method': 'POST',
        'url': 'https://selfservice.uapoldmutual.com/apps/files',
        'headers': {
            'Cookie': 'ci_session=3j7adbie0g46mc5ivgflid7pve1i58cm'
        },
        formData: {
            'files': {
                'value': fs.createReadStream(file_url),
                'options': {
                    'filename': file_name,
                    'contentType': null
                }
            }
        }
    };
    request(options, function(error, response) {
        if (error) throw new Error(error);

    });


}


async function rename_file(old_file_name, new_file_name, callback) {


    fs.rename(old_file_name, new_file_name, function(err, response) {
        if (err) {

            throw err;
        } else {
            // send_file(new_file_name, new_file_name);


            callback(false, response);
        }

    });


}


function form_builder(phone_number, line_of_business, request_category, source_channel, policy_reg_no, bank_subcategory, is_healthworker, claim_type) {


    const form = {}; // or "const valueToPush = new Object();" which is the same
    form["phone_number"] = phone_number;
    form["request_category"] = request_category;
    form["source_channel"] = source_channel;
    form["is_healthworker"] = is_healthworker;
    form["claim_type"] = claim_type;


    if (line_of_business) {
        form["line_of_business"] = line_of_business;

    }


    if (bank_subcategory) {
        form["bank_subcategory"] = bank_subcategory;

    }


    if (policy_reg_no) {
        form["policy_reg_no"] = policy_reg_no;


    }


    return form;

}

function process_request(options) {
    try {
        request(options, function(error, response) {

            if (error) {
                process.on('uncaughtException', handleErrors);

            }
            return response.body;

        });
    } catch (e) {

    }

}


function handleErrors(e) {
    if (!e.errorCode) {

        process.exit(1)
    }


    process.exit(e.errorCode)
}


async function save_media(ticket_no, phone_no, file_path, caption, file_url, callback) {
    await SessionService.saveWhatsAppFileSession(phone_no, file_path, ticket_no, caption, file_url, function(err, response) {
        if (err) {
            handleErrors(err);
        }

        callback(false, response);

    });
}

async function ticket_media(ticket_no, callback) {
    try {
        SessionService.getWhatsAppSessionFiles(ticket_no, function(err, resp) {
            if (err) {
                handleErrors(err);
            } else
                callback(false, resp);
        });
    } catch (e) {

    }


}

async function registration_media(phone_no, callback) {
    try {
        await SessionService.getWhatsAppSessionFiles(phone_no, function(err, resp) {
            if (err) {
                handleErrors(err);
            }
            callback(false, resp);
        });
    } catch (e) {

    }


}

async function reghealthworkers(phone_number, callback) {
    try {


        SessionService.getWhatsAppHealthWorkerData(phone_number, function(error, resp) {


            if (error) {

                throw error;
            }


            const string = JSON.stringify(resp);
            const objectValue = JSON.parse(string);


            if (isEmptyObject(objectValue)) {

                ticket_no = '0';
                callback(false, ticket_no);
            } else {

                var source_channel = config.whatsApp.source_channel;
                let name;
                let id_number;
                let license_number;
                let profession;
                let beneficiary_phone_number;
                let email;
                let beneficiary_name;
                let facility;
                let sector = '';
                let profession_public = '';
                let response_message;


                objectValue.forEach(element => {


                    if (element.key === "name") {
                        name = element.value;
                    } else if (element.key === "id_number") {
                        id_number = element.value;
                    } else if (element.key === "license_number") {
                        license_number = element.value;
                    } else if (element.key === "profession") {
                        profession = element.value;
                    } else if (element.key === "beneficiary_phone_number") {
                        beneficiary_phone_number = element.value;
                    } else if (element.key === "email") {
                        email = element.value;
                    } else if (element.key === "beneficiary_name") {
                        beneficiary_name = element.value;
                    } else if (element.key === "facility") {
                        facility = element.value;
                    } else if (element.key === "profession_public") {
                        profession_public = element.value;
                    } else if (element.key === "sector") {
                        sector = element.value;
                    }


                });

                if (typeof name != 'undefined' && typeof phone_number != 'undefined' && typeof id_number != 'undefined' &&
                    typeof license_number != 'undefined' && typeof profession != 'undefined' && typeof beneficiary_name != 'undefined' && typeof facility != 'undefined' &&
                    typeof beneficiary_phone_number != 'undefined') {
                    let form = {
                        'name': name,
                        'phone_number': phone_number,
                        'id_number': id_number,
                        'license_number': license_number,
                        'profession': profession,
                        'beneficiary_phone_number': beneficiary_phone_number,
                        'beneficiary_name': beneficiary_name,
                        'facility': facility,
                        'sector': sector,
                        'profession_public': profession_public,
                        'source_channel': source_channel,
                        'country_code': country_code


                    }

                    reghealthworkers_api(form, function(error, response) {
                        if (error) {
                            callback(true, response);
                        } else {

                            callback(false, response);
                        }
                    })

                }


            }

        });


    } catch (e) {

        throw e;
    }

}


async function reghealthworkers_api(form, callback) {

    form.source_channel = config.whatsApp.source_channel;


    await get_User_Country(form.phone_number, function(error, response) {
        if (error) {
            callback(true, error)
        } else {
            form.country_code = response;

            // console.log("@ reghealthworkers_api FORM => " + JSON.stringify(form));
            console.log("@ reghealthworkers_api FORM => requesting " + form.phone_number);
            const options = {
                'method': 'POST',
                'url': 'https://selfservice.uapoldmutual.com/apps/reghealthworkers',
                'headers': {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                form: form
            };
            request(options, function(error, response) {
                try {
                    // console.log("@ reghealthworkers_api result => " + JSON.stringify(response));
                    console.log("@ reghealthworkers_api result => received " + form.phone_number);
                } catch (e) {}

                if (error) {
                    console.log("@ reghealthworkers_api result => " + response + " Error: " + error);
                    callback(true, error)
                } else {
                    var obj = JSON.parse(response.body);
                    let response_message = obj.message;


                    callback(false, response_message);
                }


            });
        }
    });


}


async function sendOTP(phone_number, callback) {


    get_User_Country(phone_number, function(error, response) {
        if (error) {
            throw error;
        }

        country_code = response;


        const options = {
            'method': 'POST',
            'url': 'https://selfservice.uapoldmutual.com/apps/sendOTP',
            'headers': {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            form: {
                'phone_number': phone_number,
                'country_code': country_code

            }
        };
        request(options, function(error, response) {
            if (error) {
                callback(true, 'An error occurred sending SMS');

            } else {

                callback(false, JSON.parse(response.body));
            }

        });

    });


}


async function validateOTP(phone_number, otp, callback) {


    get_User_Country(phone_number, function(error, response) {
        if (error) {
            throw error;
        }

        country_code = response;


        const options = {
            'method': 'POST',
            'url': 'https://selfservice.uapoldmutual.com/apps/validateOTP',
            'headers': {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            form: {
                'phone_number': phone_number,
                'otp': otp,
                'country_code': country_code

            }
        };
        request(options, function(error, response) {
            if (error)
                callback(true, 'Error occurred processing your request.');
            else if (response.statusCode === 400) {
                callback(true, 'Invalid OTP, please enter again.');
            } else {

                const json_response = JSON.stringify(response.body);


                callback(false, response.body);
            }
        });


    });


}


async function SMS_Update(phone_number, option, callback) {

    try {


        let source_channel = config.whatsApp.source_channel;

        const options = {
            'method': 'POST',
            'url': 'https://selfservice.uapoldmutual.com/apps/sms',
            'headers': {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            form: {
                'phone_number': phone_number,
                'source_channel': source_channel,
                'option': option

            }
        };
        request(options, function(error, response) {
            if (error) {
                callback(true, 'Error occured')
            } else {

                const json_response = JSON.parse(response.body);


                callback(false, response.body);
            }

        });


    } catch (e) {

        throw e;
    }


}


module.exports = {
    API_Function: API_Function,
    send_file: send_file,
    get_ticket_no: get_ticket_no,
    SMS_Function: SMS_Function,
    download_file: download_file,
    download_reg_media: download_reg_media,
    ticket_media: ticket_media,
    registration_media: registration_media,
    update_ticket: update_ticket,
    ticket_session: ticket_session,
    create_wa_user: create_wa_user,
    new_wa_user_session: new_wa_user_session,
    reghealthworkers: reghealthworkers,
    sendOTP: sendOTP,
    validateOTP: validateOTP,
    close_user_WhatsApp_Session: close_user_WhatsApp_Session,
    end_health_worker_session: end_health_worker_session,
    healthcare_worker_claim_request: healthcare_worker_claim_request,
    check_ticket_no: check_ticket_no,
    addCustomerVerification: addCustomerVerification,
    checkCustomerVerification: checkCustomerVerification,
    CheckIfHWUserExists: CheckIfHWUserExists,
    SMS_Update: SMS_Update,
    get_User_Country: get_User_Country
}