exports.getMenuContent = function () {
    return {
        "0": {
            "start": {
                "title": "Kindly Select your Country of Choice (respond with the corresponding number of your choice)\n1 Kenya\n2 Uganda\n3 Tanzania\n4 Rwanda\n5 South Sudan",
                "next": {
                    "1": "kenya",
                    "2": "uganda",
                    "3": "tanzania",
                    "4": "rwanda",
                    "5": "south_sudan"
                },
                "input_type": "SELECTION"
            }
        },
        "1": {
            "kenya": {
                "title": "How can we be of service today? (respond with the corresponding number of your choice)\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment \n5 New Customer Verification \n0 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    //"5": "invest_with_us",
                   // "6": "health_worker",
                   // "7": "bank_with_us",
                    "5": "new_customer_verification",
                    /*"9": "update_personal_details",*/
                    //"9": "covid",
                    "11": "start",
                },
                "input_type": "SELECTION"
            },
            "uganda": {
                "title": "How can we be of service today? (respond with the corresponding number of your choice)\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n6 New Customer Verification\n7 Wellness & COVID-19 Updates\n0 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    "5": "invest_with_us",
                    "6": "new_customer_verification",
                    /*"7": "update_personal_details",*/
                    "7": "covid",
                    "11": "start",
                },
                "input_type": "SELECTION"
            },
            "tanzania": {
                "title": "How can we be of service today? (respond with the corresponding number of your choice)\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n5 New Customer Verification\n0 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    "5": "new_customer_verification",
                    /*"6": "update_personal_details",*/
                    //"6": "covid",
                    "11": "start",
                },
                "input_type": "SELECTION"
            },
            "rwanda": {
                "title": "How can we be of service today? (respond with the corresponding number of your choice)\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n5 New Customer Verification\n6 Wellness & COVID-19 Updates\n0 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    "5": "new_customer_verification",
                    /*"6": "update_personal_details",*/
                    "6": "covid",
                    "11": "start",
                },
                "input_type": "SELECTION"
            },
            "south_sudan": {
                "title": "How can we be of service today? (respond with the corresponding number of your choice)\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n5 New Customer Verification\n0 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    "5": "new_customer_verification",
                    /*"6": "update_personal_details",*/
                    //"6": "covid",
                    "11": "start",
                },
                "input_type": "SELECTION"
            }
        },
        "2": {
            "register_claim": {
                "title": "Which insurance would you like to register claim for? (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Motor Insurance \n3 Life Insurance \n4. General Insurance type\n0 Main Menu",
                "next": {
                    "1": "claim_health",
                    "2": "claim_car",
                    "3": "claim_life",
                    "4": "claim_other",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status": {
                "title": "Policy status for? (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Motor Insurance \n3 Life Insurance \n4. General Insurance type\n0 Main Menu",
                "next": {
                    "1": "policy_status_health",
                    "2": "policy_status_car",
                    "3": "policy_status_life",
                    "4": "policy_status_other",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "buy_insurance": {
                "title": "Buy For? (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Motor Insurance \n3 Life Insurance \n4. General Insurance type\n0 Main Menu",
                "next": {
                    "1": "buy_insurance_health_success_end_message",
                    "2": "buy_insurance_car_success_end_message",
                    "3": "buy_insurance_life_success_end_message",
                    "4": "buy_insurance_other_success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment": {
                "title": "Make payment to? (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Motor Insurance\n3. Life Insurance\n4. General Insurance\n0 Main Menu",
                "next": {
                    "1": "make_insurance_payment_health",
                    "2": "make_insurance_payment_car",
                    "3": "make_insurance_payment_life",
                    "4": "make_insurance_payment_other",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "new_customer_verification": {
                "title": "Which Insurance class have you bought: (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Motor Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "verify_enter_name",
                    "2": "verify_enter_name",
                    "3": "verify_enter_name",
                    "4": "verify_enter_name",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "update_personal_details": {
                "title": "To update your personal details kindly click on this link https://bit.ly/2LazEcA#",
                "next": {
                    "1": "end"
                },
                "input_type": "END"
            },

        },
        "3": {
            "claim_health": {
                "title": "Select how to start the Health Insurance claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_health",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "claim_car": {
                "title": "Select how to start the Motor Insurance claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_car",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "claim_life": {
                "title": "Select how to start the Life Insurance claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_life",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "claim_other": {
                "title": "Select how to start the Insurance claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_other",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_health": {
                "title": "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_health",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_car": {
                "title": "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_car",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_life": {
                "title": "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_life",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_other": {
                "title": "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_other",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "buy_insurance_health_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "buy_insurance_car_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "buy_insurance_life_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "buy_insurance_other_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "make_insurance_payment_health": {
                "title": "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_health",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment_car": {
                "title": "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_car",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment_life": {
                "title": "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_life",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment_other": {
                "title": "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_other",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "verify_enter_name": {
                "title": "Kindly enter your full name?\n0 Main Menu",
                "next": {
                    "1": "verify_id_passport",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },

        },
        "4": {
            "request_callback": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "provide_details_policy_status_health": {
                "title": "What\'s Your Health Insurance Policy Number? eg. UK032344-01 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_policy_status_car": {
                "title": "What\'s Your Car Registration Number? e.g SAS123AAA \n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_policy_status_other": {
                "title": "What\'s Your Insurance Policy Number?\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_policy_status_life": {
                "title": "What\'s your Life Insurance policy number? e.g. OMS001429206 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            }, "provide_details_payment_health": {
                "title": "What\'s Your Health Insurance Policy Number? eg. US032344-01 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_payment_car": {
                "title": "What\'s Your Car Registration Number? e.g SAA123A\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_payment_other": {
                "title": "What\'s Your Insurance Policy Number?\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_payment_life": {
                "title": "What\'s Your Life Insurance Policy Number? e.g. OMS001429206 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_car": {
                "title": "What\'s Your Car Registration Number? e.g SAA123A\n \n0 Main Menu",
                "next": {
                    "1": "provide_details_car_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },

            "provide_details_other": {
                "title": "What\'s Your Insurance policy number?\n \n0 Main Menu",
                "next": {
                    "1": "provide_details_other_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_life": {
                "title": "What\'s your Life Insurance policy number? e.g. OMS001429206 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "provide_details_life_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health": {
                "title": "What\'s your Health Insurance policy number? e.g. US032344-01 or National ID Number\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "verify_id_passport": {
                "title": "What\'s your National ID e.g. 1234567 or Passport Number e.g. ********\n\n0 Main Menu",
                "next": {
                    "1": "verify_id_photo",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
        
        },
        "5": {
            "provide_details_health_photos": {
                "title": "Kindly share photos of your insurance claim supporting documents listed below.\n- Duly filled outpatient/reimbursement claim form \n- Copy of drug prescription form\n- Original receipts\n- Copy of lab request form\n\nType 1 when done uploading.\n0 Main Menu",
                "next": {
                    "1": "provide_details_photos_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_car_photos": {
                "title": "Share photos of your car and any other photos of supporting documents listed below\n- Completed Claim Form \n- Copy of the log book \n- Police Abstract Report \n- Motor Vehicle Inspection report (for third party claims)\n- Copy of the Driver’s Driving licence\n- Notice of intention to prosecute(if any)\n- Applicable excess\n\nType 1 to complete your submission\n0 Main Menu",
                "next": {
                    "1": "provide_details_photos_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_life_photos": {
                "title": "Share a Photo of Your Insurance Supporting Documents e.g. Death Certificate.\n\nType 1 to complete your submission\n0 Main Menu",
                "next": {
                    "1": "provide_details_photos_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },

            "provide_details_other_photos": {
                "title": "Share photo of your Insurance supporting Documents? e.g. Medical receipts, Death certificate, Police abstract, Damaged property\n\nType 1 to complete your submission\n0 Main Menu",
                "next": {
                    "1": "provide_details_photos_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },

            "provide_details": {
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "verify_id_photo": {
                "title": "Kindly Take & Upload a Photo of Your National ID or Passport \n\n0 Main Menu",
                "next": {
                    "1": "selfie_passport_photo",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "request_callback": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
        },
        "6": {
            "provide_details_car_additional_photos": {
                "title": "Share a photo of police abstract or any other supportive photos\n\n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_other_additional_photos": {
                "title": "Share a photo of additional supportive documents\n 1. Complete Submission \n0. Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_photos_end": {
                "title": "Thank you for the details provided, one of our Call Center representatives will contact you within 24 Hours.\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "provide_details_life_additional_photos": {
                "title": "Share a photo of additional supportive documents\n 1. Complete Submission \n0. Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "selfie_passport_photo": {
                "title": "Kindly take & upload a Selfie or Passport Photo\n0. Main Menu",
                "next": {
                    "1": "signup_phone_no",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
           
        },
        "7": {
            "success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "signup_phone_no": {
                "title": "Kindly enter the phone number you used to sign-up for the new product? e.g. 0721******\n0. Main Menu",
                "next": {
                    "1": "otp_verification",
                    "2": "end"
                },
                "input_type": "TEXT"
            },
        },
        "8": {
            "end": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                "input_type": "END"
            },
            "back_to_main": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                "input_type": "END"
            },
            "otp_verification": {
                "title": "Kindly Insert the 4 Digit Code Sent To You Via SMS\n0 Main Menu\n00. Resend OTP",
                "next": {
                    "1": "customer_verification_success_end_message",
                    "0": "back_to_main",
                    "00": "otp_verification",
                },
                "input_type": "TEXT"
            },
        },
        "9": {
            "customer_verification_success_end_message": {
                "title": "We have successfully received your details. Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                "input_type": "END"
            },
            "end": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                "input_type": "END"
            },
        },
        "10": {
            "end": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                "input_type": "END"
            },
        },

        "generic": {
            "response_options": {
                "next": {
                    "1": "provide_details",
                    "2": "request_callback",
                    "0": "back_to_main"
                }
            },
            "provide_details": {
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                }
            },
            "success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "exit": {
                "title": "Thank You For the Details Provided, One of Our Call Center Agents Will Call You Within 24 Hours."
            },
 
            "insurance_class": [
                "Health Insurance",
                "Motor Insurance",
                "Life Insurance",
                "General Insurance"
            ],
            "end": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                "input_type": "END"
            }
        }
    }
}
