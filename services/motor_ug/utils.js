const PdfPrinter = require("pdfmake/src/printer");
const path = require("path");
const fs = require("fs");

const fonts = {
  Times: {
    normal: "Times-Roman",
    bold: "Times-Bold",
    italics: "Times-Italic",
    bolditalics: "Times-BoldItalic",
  }
};

const printer = new PdfPrinter(fonts);

const createPdf = async(docDefinition) =>
    new Promise((resolve, reject) => {
        const pdfDoc = printer.createPdfKitDocument(docDefinition);
        const chunks = [];
        pdfDoc.on("data", (chunk) => chunks.push(chunk));
        pdfDoc.on("end", () => {
        const pdfBuffer = Buffer.concat(chunks);
        resolve(pdfBuffer);
        });
        pdfDoc.on("error", (err) => reject(err));
        pdfDoc.end();
});

function formatCurrency(amount, currencyCode = "USD", locale = "en-US") {
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: currencyCode,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

const getMotorQuoteDefinition = async (data) => {
      const templatesPath = path.join(__dirname, "..", "..", "templates");
      const imagePath = path.join(templatesPath, "topbrand.png");
    
      // Read and convert images to base64
      const headerImageBytes = await fs.promises.readFile(imagePath);
      const headerImageBase64 = headerImageBytes.toString("base64");
      const headerImage = `data:image/png;base64,${headerImageBase64}`;
    
      let geographicalScope = "UGANDA";
      if (data.withinEAFee > 0) {
        geographicalScope = "EAST AFRICA";
      } else if (data.outsideEAFee > 0) {
        geographicalScope = "OUTSIDE EAST AFRICA";
      }
    
      let excessCoverage;
      if (data.excessDiscount >= 1000000 && data.excessDiscount <= 3000000) {
        excessCoverage =
          "10% of the claim minimum UGX.1,000,000 to UGX.3,000,000\n";
      } else if (data.excessDiscount >= 3000001 && data.excessDiscount <= 4000000) {
        excessCoverage =
          "10% of the claim minimum UGX.3,000,001 to UGX.4,000,000\n";
      } else if (data.excessDiscount >= 4000001 && data.excessDiscount <= 5000000) {
        excessCoverage =
          "10% of the claim minimum UGX.4,000,001 to UGX.5,000,000\n";
      } else {
        excessCoverage = "10% of the claim minimum UGX.100,000\n";
      }
    
      let inExperiencedDrivers = "";
    
      if (!(data.threeYearsWithLicense)) {
        inExperiencedDrivers =
          "Young & Inexperienced drivers i.e. below 3 Years: Additional 10% of each claim, Min UGX 100,000/=\n\n";
      }
    
      // Define the PDF document
  const docDefinition = {
    pageMargins: [40, 90, 40, 60],

    header: [
      {
        image: headerImage,
        width: 600,
      },

      {
        text: [
          { text: " OLD MUTUAL INSURANCE UGANDA LIMITED \n", bold: true },
          "Nakawa Business Park, Plot 3-5 New Port Bell Road, P.O. Box 7185 Kampala, Uganda \n",
          "Tel: +256414332700, **********, Toll Free: **********\n",
          "Email: ",
          {
            text: "<EMAIL>",
            link: "mailto:<EMAIL>",
            color: "#0070c0",
          },
          ", Website: ",
          {
            text: "www.uapoldmutual.com",
            link: "www.uapoldmutual.com",
            color: "#0070c0",
          },
        ],
        alignment: "start",
        margin: [90, 2, 0, 20],
        fontSize: 8,
      },
    ],

    content: [
      {
        text: "KEY FEATURE STATEMENT / QUOTATION-MOTOR INSURANCE",
        style: "mainTitle",
      },
      {
        table: {
          widths: ["*"],
          body: [
            [{
              text: "THIS KEY FEATURE STATEMENT IS IMPORTANT TO YOU. IT SUMMARIZES THE TRANSACTION YOU ARE CONSIDERING. PLEASE ONLY SIGN AFTER YOU HAVE READ, UNDERSTOOD AND AGREED TO THE CONTENT OF THIS DOCUMENT.",
              style: "tableHeader",
              alignment: "center",
              bold: true,
            }]
          ]
        },
        margin: [0, 0, 0, 10] // Add margin below the table
      },
      {
        style: "mainTable",
        table: {
          widths: ["*", "*"],
          body: [
            [
              {
                text: "1. INSURANCE TYPE ",
                style: "tableHeader",
              },
              {
                text: "NON-LIFE",
                style: "tableContent",
              },
            ],
            [
              {
                text: "2. NAME OF THE INSURED / POLICY HOLDER",
                style: "tableHeader",
              },
              {
                text: data.fullname ? data.fullname : "",
                style: "tableContent",
              },
            ],
            [
              {
                text: "3. CLASS OF INSURANCE",
                style: "tableHeader",
              },
              {
                text: "MOTOR PRIVATE COMPREHENSIVE INSURANCE",
                style: "tableContent",
              },
            ],

            [
              {
                text: "4. DURATION OF THE POLICY",
                style: "tableHeader",
              },
              {
                text: "12 MONTHS",
                style: "tableContent",
              },
            ],

            [
              {
                text: "5. SCOPE OF COVER",
                style: "tableHeader",
              },
              {
                layout: "noBorders",
                table: {
                  body: [
                    [
                      {
                        text: "Part 1: Own Damage/Theft",
                        style: "tableHeader",
                      },
                    ],
                    [
                      {
                        text: "Against accidental/loss of or damage to the motor vehicle and its accessories and spare parts whilst thereon due to any cause other than those causes specifically excluded by the policy",
                        style: "tableContent",
                      },
                    ],
                    [
                      {
                        text: "\nPart 2: Liability to Third parties",
                        style: "tableHeader",
                      },
                    ],
                    [
                      {
                        text: "Any accident caused by or through or in connection with any vehicle described in the schedule or in connection with loading and/or unloading of which the Insured and/or any Passenger becomes legally liable to pay all sums including claimants’ costs and expenses in respect of: ",
                        style: "tableContent",
                      },
                    ],

                    [
                      {
                        text: "a)   Death of or damage to any person (excluding employees or family members) ",
                        style: "tableContent",
                      },
                    ],

                    [
                      {
                        text: "b)   Damage to property other than property belonging to the Insured or in care, custody or control of Insured, or being conveyed by, loaded on to or unloaded from such vehicle ",
                        style: "tableContent",
                      },
                    ],
                  ],
                },
              },
            ],

            [
              {
                text: "6. VEHICLE DETAILS / SUMS INSURED",
                style: "tableHeader",
                colSpan: 2,
              },
              "",
            ],
            [
              {
                text: "MAKE",
                style: "tableHeader",
              },
              {
                text: data.make ? data.make : "",
                style: "tableContent",
              },
            ],

            [
              {
                text: "MODEL",
                style: "tableHeader",
              },
              {
                text: data.model ? data.model : "",
                style: "tableContent",
              },
            ],

            [
              {
                text: "VEHICLE VALUE ",
                style: "tableHeader",
              },
              {
                text: `${formatCurrency(data.sumInsured, "UGX")}`,
                style: "tableContent",
              },
            ],

            [
              { text: "6.1 DISCLAIMER ", style: "tableHeader" },
              { text: "Please note that a valuation of your vehicle will be conducted to determine its insurance value.\n Based on the findings of this valuation, premium adjustments will be made, and a debit or credit note will be issued accordingly. ", style: "tableContent" },
            ],
            [
              {
                text: "7. PREMIUM ",
                style: "tableHeader",
              },
              {
                text: `${formatCurrency(data.premium, "UGX")}`,
                bold: true,
                style: "tableContent",
              },
            ],
            [
              {
                text: "8. EXCESS",
                style: "tableHeader",
                colSpan: 2,
              },
              "",
            ],

            [
              {
                text: [
                  `\n\nOwn Damage: ${excessCoverage} \n`,
                  "Windscreen : 20% of each claim, Minimum UGX 50,000/= \n\n",
                  `Theft Cases: ${excessCoverage} \n`,
                  `${inExperiencedDrivers}`,
                ],
                style: "tableContent",
                colSpan: 2,
              },
              "",
            ],

            // [
            //   {
            //     text: "9. POLICY BENEFITS/EXTENSIONS",
            //     style: "tableHeader",
            //     colSpan: 2,
            //   },
            //   "",
            // ],

            // ...data.benefits.map((benefit) => [
            //   { text: benefit.name, style: "tableContent" },
            //   {
            //     text: benefit.value,
            //     style: "tableContent",
            //   },
            // ]),
            [
              {
                text: "10. POLICY EXCLUSIONS",
                style: "tableHeader",
                colSpan: 2,
              },
              "",
            ],

            [
              {
                text: "o  War and related perils and terrorism \no  Faults or defects known to the insured or his responsible employees before the loss occurred\n"+ 
                "o  Willful neglect.\no  Year 2000 related problems and computer viruses.\no  Fines and damages.\no  Theft by own employees\no  Wear and tear or depreciation\n"+
                "o  Shortages due to clerical or accounting errors or omissions.\no  Loss of use and/or consequential loss of any kind\no  Damage to tyres by braking, punctures, cuts or bursts\n",
                style: "tableContent",
                colSpan: 2,
              },
              "",
            ],

            [
              {
                text: " ",
                style: "tableHeader",
                colSpan: 2,
              },
              "",
            ],

            [
              {
                text: "11. POLICYHOLDER’S CANCELLATION RIGHTS",
                style: "tableHeader",
              },
              {
                text: [
                  "\nThe Policy may be cancelled at any time by the Insured / policyholder by giving thirty days’ notice to Old Mutual Insurance (U) Ltd ",
                  { text: "(the Company) ", style: "tableHeader" },
                  "provided no claim has arisen during the current Period of Insurance and the current Certificate(s) of Insurance has been returned to the Company on or before the date of cancellation the Insured shall be entitled to the difference (if any) between the premium paid and the premium calculated by the Company. ",
                  {
                    text: "\n\nRefer to the section of further points to consider ",
                    style: "tableHeader",
                  },
                ],
                style: "tableContent",
              },
            ],

            [
              {
                text: "12. EARLY TERMINATION BY THE POLICY HOLDER",
                style: "tableHeader",
              },
              {
                text: [
                  "\nThe Policy may be terminated at any time by the Insured / policyholder by giving thirty days’ notice to Old Mutual Insurance (U) Ltd ",
                  { text: "(the Company)", style: "tableHeader" },
                  "provided no claim has arisen during the current Period of Insurance and the current Certificate(s) of Insurance has been returned to the Company on or before the date of termination the Insured shall be entitled to the difference (if any) between the premium paid and the premium calculated by the Company. ",
                  {
                    text: "\n\nRefer to the detailed policy wording /policy document for more detailed information.  ",
                    style: "tableHeader",
                  },
                ],
                style: "tableContent",
              },
            ],

            [
              { text: "13. CLAIM PROCEDURE", style: "tableHeader" },
              {
                text: [
                  "\nNo admission, offer, promise or payment shall be made by or on behalf of the Insured or any person claiming to be indemnified without the written consent of the Company which shall be entitled if it so desires to take over and conduct in the name of the Insured or such person the defence or settlement of any claim or to prosecute in the name of the Insured or such person for its own benefit any claim for indemnity or damages or otherwise and shall have full discretion in the conduct of any proceedings and in the settlement of any claim and the Insured and such person shall give all such information and assistance as the Company may require. ",
                  {
                    text: "\n\nRefer to the detailed policy wording /policy document for more detailed information.  ",
                    style: "tableHeader",
                  },
                ],
                style: "tableContent",
              },
            ],

            [
              { text: "14. CLAIM NOTIFICATION", style: "tableHeader" },
              {
                text: [
                  "\nIn the event of any occurrence which may rise to a claim under this Policy the Insured / policyholder shall as soon as possible give notice thereof to the Company with full particulars.",
                  {
                    text: "\n\nRefer to the section of further points to consider",
                    style: "tableHeader",
                  },
                ],
                style: "tableContent",
              },
            ],

            [
              { text: "15. DISCLOSURE OF MATERIAL FACTS", style: "tableHeader" },
              {
                text: "\n\nAs the insured / policy holder, you are supposed declare all material facts and signing this key feature statement will be interpreted to mean that there was full disclosure of material facts. ",
                style: "tableContent",
              },
            ],

            [
              { text: "16. POLICY UPDATES", style: "tableHeader" },
              {
                text: [
                  "\nThe policyholder / insured should notify the Company of any policy adjustments such as additions, deletions, change of location to mention but a few in a timely manner. ",
                  {
                    text: "\n\nRefer to the section of further points to consider",
                    style: "tableHeader",
                  },
                ],
                style: "tableContent",
              },
            ],
            [
              { text: "17. METHOD OF LODGING A COMPLAINT", style: "tableHeader" },
              {
                text: [
                  "The Company is committed to resolving all grievances, complaints and/or disputes in a quick, fair and timely manner. \n",
                  "\nThe insured / policyholder may lodge a formal grievance or complaint and the Company undertakes to review any such grievance or complaint and keep the Insured informed of the progress of the review within fifteen (15) days from the date of receipt of the grievance or complaint. \n",

                  "\n Policyholders may submit complaints regarding their motor insurance by initiating the process through email / telephone call. To lodge a complaint, please send a written description of the issue, along with any supporting documentation, to ",
                  {
                    text: "<EMAIL>",
                    link: "mailto:<EMAIL>",
                    color: "#0070c0",
                  },
                  " / toll free no. **********.  ",
                  {
                    text: "\n\nRefer to the detailed policy wording /policy document for more detailed information. ",
                    style: "tableHeader",
                  },
                ],
                style: "tableContent",
              },
            ],
          ],
        },
      },

      {
        text: "18. FURTHER POINTS TO CONSIDER",
        style: "listHeader",
      },
      {
        ul: [
          "Under the scope of cover section, please refer to the detailed policy wording for more information on what is covered.",
          "The duration of the policy usually differs depending on what is required by the client. Please refer to the detailed policy document to confirm if the period of insurance stated therein is in line with your expectations.",
          "The policy benefits and specific exclusion differ per client per subject matter insured thus refer to the detailed quotation, policy wording, policy document for more information.",
          "The insured / policyholder should refer to the policy wording, policy schedule for detailed information on cancellation rights. Please note that no refund will be allowed to the insured / policyholder in case they are involved in any form of fraud.",
          "The policyholder/insured should refer to the detailed policy wording and policy document for additional information on policy updates since the time in which the updates are communicated are customized per client.",
          "The insured / policyholder should refer to the policy wording, policy schedule for detailed information on claim notification. The Company will advise the policyholder / insured on the required claim documents.",
          "Failure to pay premium as per agreed payment plan will result into policy cancellation. Please note that Old Mutual Insurance (U) Ltd does not accept cash payment thus the only acceptable payment mode is through the bank or mobile money as per the details below; -"
        ],
        style: "listItems",
      },
      // insert table for bank details
      {
        table: {
          widths: ["*", "*", "*", "*"],
          body: [
            [
              {
                text: "Bank Name",
                style: "tableHeader",
              },
              {
                text: "A/C No",
                style: "tableHeader",
              },
              {
                text: "Swift Code",
                style: "tableHeader",
              },
              {
                text: "Currency",
                style: "tableHeader",
              },
            ],
            [
              {
                text: "Standard Chartered bank UG Ltd",
                style: "tableContent",
              },
              {
                text: "*************",
                style: "tableContent",
              },
              {
                text: "SCBLUGKA",
                style: "tableContent",
              },
              {
                text: "UGX",
                style: "tableContent",
              },
            ],
            [
              {
                text: "Stanbic Bank Uganda Ltd",
                style: "tableContent",
              },
              {
                text: "*************",
                style: "tableContent",
              },
              {
                text: "SBICUGKX",
                style: "tableContent",
              },
              {
                text: "UGX",
                style: "tableContent",
              },
            ],
            [
              {
                text: "KCB(U)Ltd",
                style: "tableContent",
              },
              {
                text: "**********",
                style: "tableContent",
              },
              {
                text: "",
                style: "tableContent",
              },
              {
                text: "UGX",
                style: "tableContent",
              },
            ],
            [
              {
                text: "ABSA Bank Uganda Ltd",
                style: "tableContent",
              },
              {
                text: "**********",
                style: "tableContent",
              },
              {
                text: "BRCUSD",
                style: "tableContent",
              },
              {
                text: "DOLLAR",
                style: "tableContent",
              },
            ],
            [
              {
                text: "ABSA Bank Uganda Ltd",
                style: "tableContent",
              },
              {
                text: "**********",
                style: "tableContent",
              },
              {
                text: "BRCUGX",
                style: "tableContent",
              },
              {
                text: "UGX",
                style: "tableContent",
              },
            ],
            [
              {
                text: "Citi Bank",
                style: "tableContent",
              },
              {
                text: "**********",
                style: "tableContent",
              },
              {
                text: "CITIUGKA",
                style: "tableContent",
              },
              {
                text: "DOLLAR",
                style: "tableContent",
              },
            ],
            [
              {
                text: "Citi Bank",
                style: "tableContent",
              },
              {
                text: "**********",
                style: "tableContent",
              },
              {
                text: "CITIUGKA",
                style: "tableContent",
              },
              {
                text: "EUR",
                style: "tableContent",
              },
            ],
            [
              {
                text: "KCB(U)Ltd",
                style: "tableContent",
              },
              {
                text: "**********",
                style: "tableContent",
              },
              {
                text: "",
                style: "tableContent",
              },
              {
                text: "KESH",
                style: "tableContent",
              },
            ],
          ],
        },
      },
      {
        text:["MTN Mobile Money: Go to Paybill-> Goods and Services -> Code: UAP ->Reference:",
          {text:"(your name)", background: "yellow"}],
        bold: true,
        fontSize: 10, 
        margin: [0, 5, 0, 10]
      },
      {
        text: "19. KEY FEATURE STATEMENT DISCLAIMER",
        style: "listHeader",
      },

      {
        text: [
          "This Key Feature Statement is a summary and does not encompass the complete terms of this motor insurance product. Elaborate and comprehensive terms can be found in the policy document. Policyholders are advised to refer to the complete insurance policy document for a detailed understanding of terms and conditions. \n\n",
          "For more in-depth information and inquiries, please do not hesitate to contact us either via email at ",
          {
            text: "<EMAIL>",
            link: "mailto:<EMAIL>",
            color: "#0070c0",
          },
          " or through our toll-free lines at 0800 132 700 and 0800 135135. ",
        ],
        style: "normalStatement",
      },
      {
        text: "20. ADDITIONAL INFORMATION / SUBJECTIVITIES",
        style: "listHeader",
      },
      {
        ol: [
          "Quotation valid for 30 days",
          "Quotation does not constitute an offer in itself but an invitation to offer",
          "Subject to our standard policy terms and conditions"
        ],
        style: "listItems",
      },
      {
        text: "21. CONSENT FOR PROCESSING PERSONAL DATA OUTSIDE UGANDA",
        style: "listHeader",
        margin: [0, 10, 0, 0],
      },
      {
        text: "Personal data refers to identifiable data of an individual relating nationality, age, marital status, occupation, identification numbers and any other information necessitating an individual’s opinion. Special Personal data includes data revealing sexual life, financial information, health status or medical records of an individual. It may be necessary that we process your personal data outside Uganda in connection of which we are required to obtain your consent. By signing below, you agree that you have read and understood the above Privacy Notice and our Privacy Policy, and you hereby authorize us to process your sensitive personal data outside Uganda.",
        style: "normalStatement",
        margin: [0, 10, 0, 0],
      },
       {
        text: "22. CONSENT FOR PROCESSING PERSONAL DATA RELATING TO A CHILD",
        style: "listHeader",
        margin: [0, 10, 0, 0],
      },
      {
        text: "You may be required to provide personal data relating to a child; for instance, where providing details of your beneficiaries/next of kin. Please note that a child is a person under the age of 18 years. For us to process any personal data relating to a child, we require your consent as the child’s parent or legal guardian and proof of the child’s age. By signing below, you confirm that you are the parent or legal guardian of the child whose personal data is being provided to us and that you have read and understood the Privacy Notice above and our Privacy Policy and you hereby consent to our processing of the child’s personal data in or outside Uganda.",
        style: "normalStatement",
        margin: [0, 10, 0, 0],
      },
      {
        text: "23. CONSENT FOR MARKETING PURPOSES/ COMMERCIAL USE OF DATA",
        style: "listHeader",
        margin: [0, 10, 0, 0],
      },
      {
        text: "We would like to use your details to carry out analytical and market research about our and our affiliates’ products and services and to provide you with information about these insurance and financial products, services and special offers from us or our affiliates. Please note that if you do not want to receive our marketing information you may opt-out by contacting us at any time.",
        style: "normalStatement",
        margin: [0, 10, 0, 10],
      },
      {
        text: "24. CUSTOMER DECLARATION:",
        style: "listHeader",
      },
      {
        text: "I desire to insure with Old Mutual Insurance Uganda Limited. the motor vehicle specified. I hereby warrant the truth of the answers and particulars given and declare that I have not"+
              "withheld, misstated or misrepresented any fact which might be considered material to this Insurance. I further declare that the car is in a good state of repair and will be so maintained and"+
              "agree that this declaration shall be the basis of the contract between me and Old Mutual Insurance Uganda Limited. Also, I hereby declare that I have read and understood the quotation"+
              "terms and conditions to the best of my knowledge.",
        style: "normalStatement",
      },

      {
        table: {
          widths: ["*"], // Single column
          body: [
            [
              {
                text: [
                  "\nFor more in-depth information and inquiries, please do not hesitate to contact us either via\n",
                  "email at ",
                  {
                    text: "<EMAIL>",
                    link: "mailto:<EMAIL>",
                    color: "#0070c0",
                  },
                  " or through our toll-free lines at 0800 132 700 and\n 0800 135135. ",
                ],
                style: "bodyContactStatement",
                alignment: "center", // Center the text
                margin: [20, 25, 20, 10], // Margin above the text
                fillColor: "#222a35", // Background color of the cell
                color: "white", // Text color
              },
            ],
          ],
        },
        layout: "noBorders", // No borders for the table
        margin: [0, 20, 0, 0], // Margin above the box
      },
    ],
    defaultStyle: {
      font: "Times",
    },
    styles: {
      mainTitle: {
        fontSize: 11,
        bold: true,
        alignment: "center",
        margin: [10, 20, 10, 20],
      },

      mainTable: {
        margin: [0, 0, 0, 30],
      },

      tableHeader: {
        fontSize: 10,
        bold: true,
      },

      tableContent: {
        fontSize: 10,
      },

      listHeader: {
        fontSize: 10,
        bold: true,
      },

      listItems: {
        fontSize: 10,
        margin: [10, 10, 10, 10],
      },

      normalStatement: {
        fontSize: 10,
        margin: [0, 10, 0, 10],
      },

      bodyContactStatement: {
        fontSize: 10,
        bold: true,
        margin: [10, 10, 10, 10],
        alignment: "center",
      },
    },
  };
  return docDefinition;
}

module.exports = {
    getMotorQuoteDefinition: getMotorQuoteDefinition,
    createPdf: createPdf
}