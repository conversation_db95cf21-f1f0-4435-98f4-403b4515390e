const path = require("path");
const ejs = require("ejs");
const fs = require("fs");
const utils = require("./utils");

const DOWNLOAD_FOLDER = path.join(__dirname,"..", "..", 'downloads');
const FILE_EXPIRY_MINUTES = 10; // Auto-delete after 10 minutes
// Ensure download folder exists
fs.mkdirSync(DOWNLOAD_FOLDER, { recursive: true });

// saves temporary files to public/downloads folder
const saveTempFile = async (base64, filename) =>{

  if (!base64 || !filename) {
    return { error: 'Missing base64 or filename' };
  }

  const filePath = path.join(DOWNLOAD_FOLDER, filename);
  const buffer = Buffer.from(base64, 'base64');

  fs.writeFile(filePath, buffer, (err) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to save file' });
    }

    // Schedule file deletion after expiry
    setTimeout(() => {
      fs.unlink(filePath, (err) => {
        if (err) {
          console.error(`Failed to delete ${filename}:`, err);
        } else {
          console.log(`Deleted expired file: ${filename}`);
        }
      });
    }, FILE_EXPIRY_MINUTES * 60 * 1000);
  });

    // Use environment variable for server URL, fallback to localhost for development
    const baseUrl = process.env.SERVER_BASE_URL || 'http://localhost:3031/ug';
    const publicUrl = `${baseUrl}/downloads/${filename}`;
    return publicUrl;
}

const generateQuote = async (data) =>{
  const result = {};

  const docDefinition = await utils.getMotorQuoteDefinition(data);

  try {
    const pdfBuffer = await utils.createPdf(docDefinition);
    // result.base64 = pdfBuffer.toString("base64");
    //save temp file
    const filename = `motor_quote_${Date.now()}.pdf`;
    const fileUrl = await saveTempFile(pdfBuffer.toString("base64"), filename);
    result.fileUrl = fileUrl;
    // const pdfName = data.fullname ? data.fullname : "MotorQuotation";

    // if (data.email) {
    //   // Send email with the Base64 string
    //   await sendEmailSmtp(
    //     data.email,
    //     "Motor Quotation",
    //     `${
    //       appConfig.baseUrl
    //     }/claims/motorquote/email?user_name=${encodeURIComponent("Customer")}`,
    //     result.base64,
    //     pdfName
    //   );
    // }

    result.status = "200";
  } catch (error) {
    result.status = "500";
    result.message = "Failed to generate PDF.";
    console.error("Error generating PDF:", error);
  }

  return result;
}

module.exports = {
    generateQuote: generateQuote
}