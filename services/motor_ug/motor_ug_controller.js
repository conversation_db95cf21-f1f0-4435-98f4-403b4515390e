const express = require("express");
const routes = express.Router();
const motorService = require("./motor_ug_service"); 

routes.get("/ping", (req, res) => {
    res.json("Hello from motor ug controller");
});

routes.post("/generateQuoteDoc", async (req, res) => {
    try {
        const data = req.body;
        const quote = await motorService.generateQuote(data);
        res.json(quote);
    } catch (error) {
        console.log(error);
        res.status(500).json({ error: "Failed to generate quote" });
    }
});

module.exports = routes;