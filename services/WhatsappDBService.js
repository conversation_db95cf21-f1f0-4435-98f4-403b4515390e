'use strict';
let db = require('../models');
let utils = require('./Utils');

async function updateUserSession(params, callback) {
    db.whatsapp_user_session.upsert(params).then(result => {
        callback(false, result)
        /*if (result)
            callback(false, result)
        else {
            callback(true,'Update failed')
        }*/
    });
}

async function updateCountry(params, callback) {
    db.whatsapp_user_session.update(params, {where: {id: params.id}}).then(result => {
        callback(false, result)
        /*if (result)
            callback(false, result)
        else {
            callback(true,'Update failed')
        }*/
    });
}

async function isRequestExisting(params, callback) {
    db.wa_requests.findOne({where: {phone_no: params.phone_no, message_id: params.message_id}})
        .then(result => {
            if (result) {
                params.request_count = result.request_count + 1
                console.log(utils.getDateTime() + " | DUPLICATE REQ: " + JSON.stringify(params))
                // console.log('To update: ' + JSON.stringify(params))
                db.wa_requests.update(params,
                    {where: {id: result.id}}
                ).then(r => {
                    callback(true)
                })
            } else {
                db.wa_requests.create(params).then(result => {
                    // console.log('WA request created? ' + result)
                    callback(false)
                }).catch(error =>{
                   try {
                       console.log(error.errors)
                   } catch (e) {

                   }
                })
            }

        })
}

async function updateWaUser(params, callback) {
    db.wa_user.upsert(params).then(result => {
        callback(false, result)
    })
}

async function getPostbackMenu(unique_identifier, callback) {
    db.ng_fb_menu.findOne({
            where: {unique_identifier: unique_identifier}
        }
    ).then(
        result => {
            if (result) {
                callback(false, result)
            }
        });
}

async function getUserLevel(phone_no, callback) {
    db.whatsapp_user_session.findOne({
            where: {phone_no: phone_no}
        }
    ).then(
        result => {
            if (result) {
                callback(false, result)
            } else {
                callback(true, 'User does not exit');
            }
        });
}


module.exports = {
    updateUserSession: updateUserSession,
    getPostbackMenu: getPostbackMenu,
    isRequestExisting: isRequestExisting,
    updateCountry: updateCountry,
    getUserLevel: getUserLevel
}
