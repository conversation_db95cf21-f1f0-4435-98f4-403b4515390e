exports.getMenuContent = function () {
    return {
        "1": {
            "start": {
                "title": "Welcome to UAP Old Mutual Services & Faulu Microfinance Bank.\n1 Register a Claim \n2 Check Policy Status\n3 Buy insurance \n4 Invest with Us\n5 Bank with Us\n6 Insurance Payment\n7 Wellness & COVID-19 Updates",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "invest_with_us",
                    "5": "bank_with_us",
                    "6": "make_insurance_payment",
                    "7": "covid"
                }
            }
        },
        "2": {
            "register_claim": {
                "title": "Which insurance would you like to register claim for?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance Type\n00 Back",
                "next": {
                    "1": "claim_options",
                    "2": "claim_options",
                    "3": "claim_options",
                    "4": "claim_options",
                    "0": "back_to_main"
                }
            },
            "policy_status": {
                "title": "Policy status for?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance Type\n00 Back",
                "next": {
                    "1": "policy_status_options",
                    "2": "policy_status_options",
                    "3": "policy_status_options",
                    "4": "policy_status_options",
                    "0": "back_to_main"
                }
            },
            "buy_insurance": {
                "title": "Buy for?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance Type\n00 Back",
                "next": {
                    "1": "buy_insurance_options",
                    "2": "buy_insurance_options",
                    "3": "buy_insurance_options",
                    "4": "buy_insurance_options",
                    "0": "back_to_main"
                }
            },
            "make_insurance_payment": {
                "title": "Make payment to?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance Type\n00 Back",
                "next": {
                    "1": "make_insurance_payment_options",
                    "2": "make_insurance_payment_options",
                    "3": "make_insurance_payment_options",
                    "4": "make_insurance_payment_options",
                    "0": "back_to_main"
                }
            },
            "invest_with_us": {
                "title": "We have a fully dedicated investment platform i-invest\nDial *480# or chat Arifa on Facebook Messenger about your investments\n\n00 Back ",
                "next": {
                    "0": "back_to_main"
                }
            },
            "bank_with_us": {
                "title": "Which Faulu Microfinance Bank product are you interested in:\n1. Loan\n2. Current account\n3. Savings Account\n4. Fixed Deposit Account\n00 Back",
                "next": {
                    "1": "bank_with_us_option",
                    "2": "bank_with_us_option",
                    "3": "bank_with_us_option",
                    "4": "bank_with_us_option",
                    "0": "back_to_main"
                }
            },
            "health_worker": {
                "title": "How we can we help you:\n1. New user registration\n2. Register a claim \n0 Main menu",
                "next": {
                    "1": "health_worker_profession",
                    "2": "health_worker_register_claim_type",
                    "0": "back_to_main"
                }
            },
            "covid": {
                "title": "Preferred update on:\n1. Keeping healthy\n2. Feeling unwell\n3. Working from home\n4. Studying from home\n5. Unwinding from home\n6. Ministry of health updates\n00 Back",
                "next": {
                    "1": "covid_update_message",
                    "2": "covid_update_message",
                    "3": "covid_update_message",
                    "4": "covid_update_message",
                    "5": "covid_update_message",
                    "6": "covid_update_message",
                    "0": "back_to_main"
                }
            }
        },
        "3": {
            "claim_options": {
                "title": "Select how to start the Insurance claim \n1. Provide claim details \n2. Request a call back\n00 Back \n0 Main menu",
                "next": {
                    "1": "provide_details",
                    "2": "request_callback",
                    "0": "register_claim"
                }
            },
            "policy_status_options": {
                "title": "Select how to start your policy status inquiry\n1. Provide claim details \n2. Request a call back\n00 Back \n0 Main menu",
                "next": {
                    "1": "provide_details",
                    "2": "request_callback",
                    "0": "policy_status"
                }
            },
            "buy_insurance_options": {
                "title": "Select how to start buying Insurance\n1. Provide claim details \n2. Request a call back \n00 Back \n0 Main menu",
                "next": {
                    "1": "provide_details",
                    "2": "request_callback",
                    "0": "buy_insurance"
                }
            },
            "make_insurance_payment_options": {
                "title": "Select How to start the Insurance payment  \n1. Provide claim details \n2. Request a call back \n00 Back \n0 Main menu",
                "next": {
                    "1": "provide_details",
                    "2": "request_callback",
                    "0": "make_insurance_payment"
                }
            },
            "health_worker_profession": {
                "title": "Kindly select your profession:\n1. Doctor\n2. Nurse\n3. Clinical Officer\n4. Laboratory Technician\n5. Public Health Officer or Technician\n0 Main menu",
                "next": {
                    "1": "health_worker_name",
                    "2": "health_worker_name",
                    "3": "health_worker_name",
                    "4": "health_worker_name",
                    "5": "health_worker_name",
                    "0": "back_to_main"
                }
            },
            "health_worker_register_claim_type": {
                "title": "Kindly select the type of claim\n1. Hospital Benefit \n2. Last Expense Benefit\n0 Main Menu",
                "next": {
                    "1": "health_worker_register_claim_hosp",
                    "2": "health_worker_register_claim_last",
                    "0": "back_to_main"
                }
            }
        },
        "4": {
            "provide_details_car": {
                "title": "What\'s your Car registration number? e.g. KAA123A \n\n00 Back \n0 Main menu",
                "next": {
                    "*\\w+": "provide_details.policy_no",
                    "0": "back_to_main"
                }
            },
            "provide_details_other": {
                "title": "What\'s your insurance policy number?\n\n00 Back \n0 Main menu",
                "next": {
                    "*\\w+": "provide_details.policy_no",
                    "0": "back_to_main"
                }
            },
            "provide_details_life": {
                "title": "What\'s your Life Insurance policy number? e.g. OMK001429206 or National ID Number\n\n00 Back \n0 Main menu",
                "next": {
                    "*\\w+": "provide_details.policy_no",
                    "0": "back_to_main"
                }
            },
            "provide_details_health": {
                "title": "What\'s your Health Insurance policy number? e.g. UK032344-01 or National ID Number \n\n00 Back \n0 Main menu",
                "next": {
                    "*\\w+": "provide_details.policy_no",
                    "0": "back_to_main"
                }
            },
            "health_worker_name": {
                "title": "Kindly enter your first name and surname\n\n0 Main menu",
                "next": {
                    "*\\w+": "health_worker.name",
                    "0": "back_to_main"
                }
            },
            "health_worker_register_claim_hosp": {
                "title": "Select how to start your Hospital Benefit claim \n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_id",
                    "2": "request_callback",
                    "0": "back_to_main"
                }
            },
            "health_worker_register_claim_last": {
                "title": "Select how to start the Last Expense Benefit claim \n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_last_claim_id",
                    "2": "request_callback",
                    "0": "back_to_main"
                }
            }
        },
        "5": {
            "health_worker_national_id": {
                "title": "Kindly enter your National ID number\n\n0 Main menu",
                "next": {
                    "*\\w+": "health_worker.national_id",
                    "0": "back_to_main"
                }
            },
            "provide_details_health_worker_hosp_claim_id": {
                "title": "Kindly enter your National ID number? e.g. 1234567\n\n0 Main menu",
                "next": {
                    "*\\w+": "provide_details_health_worker_hosp.national_id",
                    "0": "back_to_main"
                }
            },
            "provide_details_health_worker_last_claim_id": {
                "title": "Kindly enter your National ID number? e.g. 1234567\n\n0 Main menu",
                "next": {
                    "*\\w+": "provide_details_health_worker_last.national_id",
                    "0": "back_to_main"
                }
            }
        },
        "6": {
            "health_worker_facility": {
                "title": "Kindly enter your primary health facility of operation?\n\n0 Main menu",
                "next": {
                    "*\\w+": "health_worker.facility",
                    "0": "back_to_main"
                }
            }
        },
        "7": {
            "health_worker_reg_no": {
                "title": "Kindly enter your professional registration / licence no.\n\n0 Main menu",
                "next": {
                    "*\\w+": "health_worker.reg_no",
                    "0": "back_to_main"
                }
            }
        },
        "8": {
            "health_worker_beneficiary_name": {
                "title": "Beneficiary information:\n\nKindly enter your beneficiary'\s name (first name & surname)\n\n0 Main menu",
                "next": {
                    "*[a-zA-Z]+": "health_worker.beneficiary_name",
                    "0": "back_to_main"
                }
            }
        },
        "9": {
            "health_worker_beneficiary_phone_no": {
                "title": "Kindly enter your beneficiary\'s phone number\n\n0 Main menu",
                "next": {
                    "*\\w+": "health_worker.beneficiary_phone_no",
                    "0": "back_to_main"
                }
            }
        },

        "generic": {
            "response_options": {
                "next": {
                    "1": "provide_details",
                    "2": "request_callback",
                    "0": "back_to_main"
                }
            },
            "provide_details": {
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                }
            },
            "success_end_message": {
                "title": "Thank you for the details provided. One of our Call center representatives will call you within 24 hours.\n\n0 Main menu",
                "next": {
                    "0": "back_to_main"
                }
            },
            "success_end_message_hw_claim": {
                "title": "We have successfully received your claim notification.\n One of our call center representatives will contact you within 24 Hours.\n\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                }
            },
            "success_end_message_hw_registration": {
                "title": "Your registration has been completed successfully..\n\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                }
            },
            "exit": {
                "title": "Thank you for the details provided, one of our call center agents will call you within 24 hours."
            },
            "covid_end_message": {
                "title": "A link has sent to you via sms on"
            },
            "covid_update_message": {
                "title": "A link has been sent to you via sms on \n00 Back \n0 Main menu",
                "next": {
                    "0": "back_to_main",
                    "11": "exit"
                }
            },
            "covid_menu": {
                "title": "A link has sent to you via sms on",
                "1": "how to keep healthy\n00 Back\n0 Main menu",
                "2": "what to do if you feel unwell\n00 Back\n0 Main menu",
                "3": "how to work from home\n00 Back\n0 Main menu",
                "4": "how to study from home\n00 Back\n0 Main menu",
                "5": "how to unwind from home\n00 Back\n0 Main menu",
                "6": "ministry of health updates\n00 Back\n0 Main menu",
                "0": "back to menu"
            },
            "insurance_class": [
                "Health Insurance",
                "Car Insurance",
                "Life Insurance",
                "other insurance insurance"
            ]
        }
    }
}