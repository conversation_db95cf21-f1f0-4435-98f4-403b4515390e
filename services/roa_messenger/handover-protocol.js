/**
 * Copyright 2017-present, Facebook, Inc. All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';

//import API helper
const messengerService = require('./MessengerService');

function passThreadControl (userPsid, targetAppId, text,country_code) {
    console.log('PASSING THREAD CONTROL')
    let payload = {
        recipient: {
            id: userPsid
        },
        target_app_id: targetAppId,
        metadata:text
    };

    messengerService.call('/pass_thread_control', payload,country_code, () => {});
    // messengerService.sendTextMessage(userPsid,"I am transferring you to a customer service agent, please type you query below.")

}

function takeThreadControl (userPsid) {
    // console.log('TAKING THREAD CONTROL')
    let payload = {
        recipient: {
            id: userPsid
        }
    };

    messengerService.call('/take_thread_control', payload, () => {});
}

module.exports = {
    passThreadControl,
    takeThreadControl
};