let messengerService = require('./MessengerService.js')
let validateInputService = require('../ValidateInputService');
let mailService = require('../MailService'),
HandoverProtocol = require('../roa_messenger/handover-protocol');

// var dbService = require('../sadc/DbService');
var dbService = require('./DbService');

let salutation_trigger = [
    'hi chatbot', 'hello chatbot', 'helo chatbot', 'halo chatbot', 'hallo chatbot', 'hey chatbot', 'hi', 'menu', 'help', 'hello', 'hallo'];
let salutation_response = ['Hi', 'Hello', 'Hello', 'Hey', 'Hey', 'Hey', 'Hi', 'Hi', 'Hi', 'Hello', 'Hi'];

exports.webhookMessages = function (request, res, country_code) {
    let req = request;
    let body = req.body;
    // country_code = "te"// te for test

    if (body.object === 'page') {

        // Iterates over each entry - there may be multiple if batched
        body.entry.forEach(function (entry) {
            // Gets the message. entry.messaging is an array, but
            // will only ever contain one message, so we get index 0
            try {
                if (entry.hasOwnProperty('messaging')) {
                    let webhook_event = entry.messaging[0];

                    let sender_psid = webhook_event.sender.id;
                    let user_data = {psid: sender_psid, country_code: country_code}
                    // Check if the event is a message or postback and
                    // pass the event to the appropriate handler function
                    if (webhook_event.message) {
                        if (webhook_event.message.quick_reply) {
                            console.log('Sender: ' + sender_psid + ' | Postback: ' + webhook_event.message.quick_reply.payload)
                            let unique_identifier = webhook_event.message.quick_reply.payload;
                            if (unique_identifier === 'ARIFA') {
                                    // HandoverProtocol.passThreadControl(sender_psid,'575521323257436','Hi Arifa',country_code)
                            }
                            else {
                                if (unique_identifier === 'GET_STARTED') {
                                    user_data.service_request_selected = null;
                                    user_data.service_request = null;
                                }
                                else if (unique_identifier === 'SKIP_EMAIL') {
                                    unique_identifier = 'SUCCESS_END_MSG'
                                } else {
                                    user_data.service_request = unique_identifier;
                                    user_data.service_request_selected = webhook_event.message.text;
                                }
                                menuHandler(user_data, unique_identifier)
                            }
                        } else if (webhook_event.message.text) {

                            console.log('Sender: ' + sender_psid + ' | Text: ' + webhook_event.message.text)
                            let unique_identifier = webhook_event.message.text

                            let sal_index = salutation_trigger.indexOf(unique_identifier.toLocaleLowerCase());

                            // console.log('Index: ' + sal_index + ' | Text: ' + webhook_event.message.text)
                            if (sal_index > -1) {

                                // messengerService.sendTextMessage(sender_psid,)
                                // messengerService.sendResponse(messengerService.templateService(sender_psid, 'generic'), sender_psid)
                                // console.log(sender_psid + ' used trigger word')
                                unique_identifier = 'GET_STARTED';
                                dbService.getUserLevel(sender_psid, function (err, user_response) {
                                    if (err) {
                                        initialWelcome(sender_psid, salutation_response[sal_index], country_code)
                                    }
                                    else {
                                        console.log('Data: ' + JSON.stringify(user_response))

                                        let user_data = {
                                            psid: sender_psid,
                                            country_code: country_code,
                                            service_request_selected: null,
                                            service_request: null
                                        }

                                        messengerService.sendTextMessage(sender_psid, getRandomWelcomePrefix() + ' ' + user_response.preferred_name + ' :-)', country_code)
                                        dbService.updateUserData(user_data, function (err, update_response) {
                                            if (err) {
                                            } else {
                                                setTimeout(() => {
                                                    getMenu(unique_identifier, update_response, sender_psid, country_code)
                                                }, 1500);
                                            }
                                        })
                                    }
                                })
                            }
                            else {
                                handleUserInput(webhook_event, user_data)
                            }

                        }

                    }
                    else if (webhook_event.postback) {
                        console.log('Payload: ' + JSON.stringify(webhook_event.postback))
                        console.log('Sender: ' + sender_psid + ' | Postback: ' + webhook_event.postback.payload + ' | Country code: ' + country_code)
                        let unique_identifier = webhook_event.postback.payload;
                        user_data.service_request = unique_identifier;
                        user_data.service_request_selected = webhook_event.postback.title;
                        menuHandler(user_data, unique_identifier)
                    }
                }
            } catch (e) {
                console.error('Webhook parse: ' + e)
            }

        });

        // Returns a '200 OK' response to all requests
        res.status(200).send('EVENT_RECEIVED');
    } else {
        // Returns a '404 Not Found' if event is not from a page subscription
        res.sendStatus(404);
    }


};

function getRandomWelcomePrefix() {
    return ['Good to see you again', 'Welcome back', 'Glad to have you here', 'Hope you are doing well'].sample()
}

Array.prototype.sample = function () {
    return this[Math.floor(Math.random() * this.length)];
};

function handleUserInput(webhook_event, user_data) {
    let sender_psid = webhook_event.sender.id;
    let user_input = webhook_event.message.text
    let country_code = user_data.country_code
    let valid_request = true;
    let invalid_msg = '';
    let unique_identifier = webhook_event.message.text
    // console.log(sender_psid + ' no trigger word')
    dbService.getUserLevel(sender_psid, function (err, user_response) {
        if (err) {
            /*let index = salutation_trigger.indexOf(webhook_event.message.text.toLocaleLowerCase());
            initialWelcome(sender_psid,salutation_response[index])*/
            console.log(user_response + ' - ' + sender_psid + ' and has not used trigger keyword')
        } else {
            console.log('Sender: ' + sender_psid + ' | RESPONSE TO: ' + user_response.unique_identifier + ' | Text: ' + webhook_event.message.text)
            switch (user_response.unique_identifier) {

                case 'MOTOR_COVER':
                case 'INSURANCE_CLAIM_TYPE':
                    user_data.service_request = unique_identifier;
                    user_data.service_request_selected = webhook_event.message.text;
                    user_response.unique_identifier = 'FULL_NAME'
                    break;
                case 'FULL_NAME':
                    user_data.full_name = unique_identifier;
                    user_response.unique_identifier = 'ID_NUMBER'
                    break;
                case 'ID_NUMBER':
                    if (!validateInputService.validateIdNumber(user_input, country_code)) {
                        valid_request = false
                        invalid_msg = 'Invalid ID number input'
                    } else {
                        user_data.id_number = unique_identifier;
                        user_response.unique_identifier = 'PHONE_NO'
                    }
                    break;
                case 'PHONE_NO':
                    if (!validateInputService.validatePhoneNo(user_input)) {
                        valid_request = false
                        invalid_msg = 'Invalid phone number input'
                    } else {
                        user_data.phone_no = user_input;
                        user_response.unique_identifier = 'EMAIL'
                    }
                    break;
                case 'EMAIL':
                    if (!validateInputService.validateEmail(user_input)) {
                        valid_request = false
                        invalid_msg = 'Invalid email input'
                    } else {
                        user_data.email_address = user_input;
                        user_response.unique_identifier = 'SUCCESS_END_MSG'
                        break;
                    }

            }
            // console.log('Valid? : ' + valid_request)
            /**
             * Check if the user input was valid then proceed with processing
             * */
            if (valid_request) {
                dbService.updateUserData(user_data, function (err, update_response) {
                    if (err) {
                        console.log('Error Response: ' + update_response)
                    } else {
                        // console.log('Response: ' + JSON.stringify(update_response))
                        console.log('sd ' + user_response.unique_identifier)
                        dbService.getPostbackMenu(user_response.unique_identifier, country_code, function (err, menu_resp) {
                            if (err) {
                                console.log('Error ' + menu_resp)
                            }
                            else {

                                if (user_response.unique_identifier === 'SUCCESS_END_MSG') { // Process the request as the end of the data collection hence send email.

                                    initiateSendEmail(menu_resp, update_response, country_code)
                                } else { // Process the request as a normal request
                                    console.log('sd ' + user_response.unique_identifier)
                                    if (user_response.unique_identifier === 'GET_STARTED') {
                                        messengerService.sendTextMessage(sender_psid, 'Great! Lovely meeting you ' + user_input + '. What would you like to do today?', country_code)
                                        setTimeout(() => {
                                            messengerService.sendResponse(JSON.parse(menu_resp.response), sender_psid, country_code)
                                        }, 1500);
                                        dbService.updateUserSession({
                                            psid: sender_psid,
                                            preferred_name: user_input,
                                            unique_identifier: menu_resp.unique_identifier
                                        }, function (err, msg) {

                                        });
                                    } else {
                                        messengerService.sendResponse(JSON.parse(menu_resp.response), sender_psid, country_code)
                                        dbService.updateUserSession({
                                            psid: sender_psid,
                                            unique_identifier: menu_resp.unique_identifier
                                        }, function (err, msg) {

                                        });
                                    }

                                }


                            }
                        })
                    }
                })
            }
            else { // User entered an Invalid input
                dbService.getPostbackMenu(user_response.unique_identifier, country_code, function (err, menu_resp) {
                    if (err) {

                    }
                    else { // Process the request as a normal request
                        /* let _menu_resp = JSON.stringify(JSON.parse(menu_resp.response))
                         let message = _menu_resp.response.message.text
                         _menu_resp.response.message.text = invalid_msg + '\n' + message*/
                        let parsed = JSON.parse(menu_resp.response)
                        parsed.message.text = invalid_msg + '\n' + parsed.message.text
                        console.log('Menu data: ' + JSON.stringify(parsed))

                        messengerService.sendResponse(parsed, sender_psid, country_code)
                        dbService.updateUserSession({
                            psid: sender_psid,
                            unique_identifier: menu_resp.unique_identifier
                        }, function (err, msg) {

                        });
                    }
                })
            }
        }
    })


}


function menuHandler(user_data, unique_identifier) {
    dbService.updateUserData(user_data, function (err, update_response) {
        if (err) {
            console.log('Error @updateUserData: ' + update_response)
        } else {
            // console.log('No Error @updateUserData: ' + update_response)
            getMenu(unique_identifier, update_response, user_data.psid, user_data.country_code)
        }
    })
}

function getMenu(unique_identifier, update_response, sender_psid, country_code) {
    dbService.getPostbackMenu(unique_identifier, country_code, function (err, menu_resp) {
            if (err) {

            }
            else {
                // console.log('---> menu_resp: ' + JSON.parse(menu_resp))
                if (unique_identifier === 'SUCCESS_END_MSG') {
                    initiateSendEmail(menu_resp, update_response, country_code)
                } else if (menu_resp.triggers_kyc) {
                    unique_identifier = 'FULL_NAME'
                    dbService.getPostbackMenu(unique_identifier, country_code, function (err, menu_kyc_resp) {
                        if (err) {

                        }
                        else {
                            if (menu_resp.response.message !== '') {
                                messengerService.sendResponse(JSON.parse(menu_resp.response), sender_psid, country_code)
                                setTimeout(() => {
                                    messengerService.sendResponse(JSON.parse(menu_kyc_resp.response), sender_psid, country_code)
                                }, 1500);
                            }

                            else
                                messengerService.sendResponse(JSON.parse(menu_kyc_resp.response), sender_psid, country_code)


                            dbService.updateUserSession({
                                psid: sender_psid,
                                unique_identifier: unique_identifier
                            }, function (err, msg) {
                                if (err) {

                                }
                            })
                        }
                    });
                }
                else {

                    messengerService.sendResponse(JSON.parse(menu_resp.response), sender_psid, country_code)
                    dbService.updateUserSession({
                        psid: sender_psid,
                        unique_identifier: menu_resp.unique_identifier
                    }, function (err, msg) {
                        if (err) {

                        }
                    })
                }

            }
        }
    )
}


function initialWelcome(sender_psid, salutation, country_code) {
    let salute = salutation + ' %s1, ' + getWelcomeMessage(country_code);
    messengerService.getUserFBDetails(sender_psid, country_code, function (err, response) {
        if (err) {
            console.log(err);
            console.error('Error getting FB details: ' + err)

        } else {
            dbService.updateUserSession({
                psid: sender_psid,
                fb_name: response.first_name + " " + response.last_name,
                fb_profile_pic_url: response.profile_pic,
                country_code: country_code,
                unique_identifier: 'GET_STARTED'
            }, function (err, msg) {
                if (err) {
                }
                else {
                    messengerService.sendTextMessage(sender_psid, parameterizedString(salute, response.first_name + " " + response.last_name), country_code)
                }
            })

        }


    });

}

function initiateSendEmail(menu_resp, update_response, country_code) {
    let cc_email = update_response.email_address;
    if (update_response.email_address == null) {
        update_response.email_address = "Not provided"
        cc_email = ""

    }
    let email_body = '\nName: ' + update_response.full_name + '\n' +
        'Phone Number: ' + update_response.phone_no + '\n' +
        'ID Number: ' + update_response.id_number + '\n' +
        'Email: ' + update_response.email_address + '\n' +
        'Service Requested: ' + update_response.service_request_selected + '\n' +
        'Service Tags: ' + update_response.service_request + '\n' +
        'Source Channel: Facebook Messenger' + '\n\n'
    console.log(update_response.psid + ' | Sending Email')
    messengerService.sendTextMessage(update_response.psid, 'Please wait as I process your request.')
    mailService.sendMail(email_body, cc_email, country_code, function (email_err, email_response) {
        if (email_err) {
            console.log(update_response.psid + ' | Error Sending Email: ' + email_response)
            messengerService.sendResponse(JSON.parse(menu_resp.response), update_response.psid, country_code)
            dbService.updateUserData({
                psid: update_response.psid,
                status: 'ERROR_SENDING_MAIL'
            }, function (err, resp) {
                if (err)
                    console.error(update_response.psid + ' |  Failed | Error updating email status: ' + resp)
                else
                    console.log(update_response.psid + ' |  Failed | Email sending failed and status updated!')
            })
            dbService.updateUserSession({
                psid: update_response.psid,
                unique_identifier: menu_resp.unique_identifier
            }, function (err, msg) {

            });
            // todo Queue this failure to be retried again.
        } else {
            messengerService.sendResponse(JSON.parse(menu_resp.response), update_response.psid, country_code)
            dbService.updateUserData({
                psid: update_response.psid,
                status: 'SENT'
            }, function (err, resp) {
                if (err)
                    console.error(update_response.psid + ' |  Sent | Error updating email status: ' + resp)
                else
                    console.log(update_response.psid + ' | Sent | Email status updated!')
            })
            dbService.updateUserSession({
                psid: update_response.psid,
                unique_identifier: menu_resp.unique_identifier
            }, function (err, msg) {

            });
        }

    })
}

function getWelcomeMessage(country_code) {

    switch (country_code) {
        case 'ke':
            return 'welcome to UAP Old Mutual! I am here to assist you with your insurance, banking and investment needs. What would you like me to call you going forward'
        case 'mw':
            return 'welcome to Old Mutual Malawi! I am here to assist you with your insurance and investment needs. What would you like me to call you going forward'
        case 'bw':
            return 'welcome to Old Mutual Botswana! I am here to assist you with your insurance and investment needs. What would you like me to call you going forward'
        case 'na':
            return 'welcome to Old Mutual Namibia! I am here to assist you with your insurance and investment needs. What would you like me to call you going forward'
        case 'zw':
            return 'welcome to Old Mutual Zimbabwe! I am here to assist you with your insurance, banking and investment needs. What would you like me to call you going forward'
        case 'te':
            return 'welcome to Old Mutual Test! I am here to assist you with your insurance, banking and investment needs. What would you like me to call you going forward'
    }
}

/***
 * @example parameterizedString("my name is %s1 and surname is %s2", "John", "Doe");
 * @return "my name is John and surname is Doe"
 *
 * @firstArgument {String} like "my name is %s1 and surname is %s2"
 * @otherArguments {String | Number}
 * @returns {String}
 */
const parameterizedString = (...args) => {
    const str = args[0];
    const params = args.filter((arg, index) => index !== 0);
    if (!str) return "";
    return str.replace(/%s[0-9]+/g, matchedStr => {
        const variableIndex = matchedStr.replace("%s", "") - 1;
        return params[variableIndex];
    });
}