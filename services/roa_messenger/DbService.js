'use strict';
let db = require('../../models');


async function updateSession(params, callback) {
    db.roa_fb_user_session.upsert(params).then(result => {
        if (result)
            callback(false, result)
        else {
            callback(true)
        }
    })
}

async function getPostbackMenu(unique_identifier, country_code, callback) {
    let where_params = {unique_identifier: unique_identifier}

    /**
     * Different countries have different Main Menus but they are sharing same unique_identifier
     * */
    if (unique_identifier === 'GET_STARTED' || unique_identifier === 'INSURANCE_CLAIM_TYPE' || unique_identifier === 'MY_PROFILE') {
        where_params = {unique_identifier: unique_identifier, country_code: country_code}
    }

    switch (country_code) {
        case 'te':
            db['roa_fb_menu'].findOne({
                    where: where_params
                }
            ).then(
                result => {
                    if (result) {
                        callback(false, result)
                    } else
                        callback(true, 'An error occurred, please try again.')
                }).catch(function (err) {
                callback(true, 'An error occurred, please try again.' + err)
            });
            break;
        default:
            db['roa_fb_menu_' + country_code].findOne({
                    where: where_params
                }
            ).then(
                result => {
                    if (result) {
                        callback(false, result)
                    } else
                        callback(true, 'An error occurred, please try again.')
                });
            break;


    }


}

async function updateUserData(params, callback) {
    db.roa_fb_user_data.findOne({
            where: {psid: params.psid, status: 'ACTIVE'}
        }
    ).then(
        result => {
            // console.log('Data to UPDATE: '+JSON.stringify(params))
            if (result) {
                if (result.service_request !== null && params.service_request !== undefined && params.service_request !== null) {
                    params.service_request = result.service_request + ',' + params.service_request
                    params.service_request_selected = result.service_request_selected + ' -> ' + params.service_request_selected
                }
                db.roa_fb_user_data.update(params, {where: {id: result.id}}).then(update_result => {

                    if (update_result) {
                        db.roa_fb_user_data.findOne({where: {id: result.id}}).then(updated_user_data => {
                                if (updated_user_data) {
                                    callback(false, updated_user_data)
                                } else {
                                    callback(true, 'Error updating record')
                                }
                            }
                        )
                    } else {
                        callback(true, 'Error updating record')
                    }
                })
            } else {
                db.roa_fb_user_data.create(params).then(update_result => {
                    if (update_result) {
                        callback(false, result)
                    } else {
                        callback(true, 'Error creating record')
                    }
                })
            }
        });
}

async function getUserLevel(psid, callback) {
    db.roa_fb_user_session.findOne({
            where: {psid: psid}
        }
    ).then(
        result => {
            if (result) {
                callback(false, result)
            } else {
                callback(true, 'User does not exit');
            }
        });
}

function getMenuTableName(country_code) {
    switch (country_code) {
        case 'zw':

    }
}

module.exports = {
    updateUserSession: updateSession,
    getPostbackMenu: getPostbackMenu,
    getUserLevel: getUserLevel,
    updateUserData: updateUserData
}