const request = require('request'),
    config = require('config'),
    _this = this;

// Test page
let access_token = "EAAhAPHAsUUYBADsrnXORwON01ZAZAhVZCHgCt7kNAAJ2kY73vfp0L1zynQCtUJxv3frP1skWPAPLjys7W7Jod0H2kX0RVFNrzajeVqoW79vjbmh2ZBPYdL8zVbcxb9PnL5yQhGlUeBf4j5s0JanSVk1DmDrIeySjMg77e4jWtwZDZD";

// NG Chatbot page
// let access_token = "EAAOPDLF8ZAOYBAExYsW0V1IfA50hR8ZAZAnCIDKuLzdOo3ZCFAx5dClTR3nPbD8D4OSyhpBZCQEoaaNYo6ZALhbyLDn0a5vKL7d30To8mecrD30qJGsrVZA16TbUwdspnHKFdL1VtNlHn7mlA4duYsvcXrCOxhq1eD9wM8kVZB2HrwZDZD";
let session_level_id, sender_psid, unique_identifier;

let TEMPLATE_GENERIC = 'generic', TEMPLATE_BUTTON = 'button', TEMPLATE_RECEIPT = 'receipt',
    TEMPLATE_AIRLINE = 'airline',
    TEMPLATE_MEDIA = 'media', TEMPLATE_PRODUCT = 'product';

function templateService(sender_psid, template) {
    switch (template) {
        case TEMPLATE_GENERIC:
            // let image_url = "../..public/images/roa_messenger/glacier.jpg"

            console.log('Dir: '+__dirname)
            return {
                "message": {
                    "attachment": {
                        "type": "template",
                        "payload": {
                            "template_type": "generic",
                            "elements": [
                                {
                                    "title": "We got you covered",
                                    "image_url": "https://68998853b4bb.ngrok.io/images/roa_messenger/profile.png",
                                    "subtitle": "Brief description",
                                    "buttons": [
                                        {
                                            "type": "postback",
                                            "title": "Insure With Us",
                                            "payload": "INSURE_WITH_US"
                                        }
                                    ]
                                }, {
                                    "title": "Enjoy our banking services",
                                    "image_url": "https://68998853b4bb.ngrok.io/images/roa_messenger/cabsneww.png",
                                    "subtitle": "Brief description",
                                    "buttons": [
                                        {
                                            "type": "postback",
                                            "title": "Bank With Us",
                                            "payload": "BANK_WITH_US"
                                        }
                                    ]
                                }, {
                                    "title": "Safe guard you future investments",
                                    "image_url": "https://picsum.photos/210/100",
                                    "subtitle": "Brief description",
                                    "buttons": [
                                        {
                                            "type": "postback",
                                            "title": "Invest & Save with Us",
                                            "payload": "INVEST_SAVE_WITH_US"
                                        }
                                    ]
                                }, {
                                    "title": "Get it from the experts",
                                    "image_url": "https://picsum.photos/203/200",
                                    "subtitle": "Brief description",
                                    "buttons": [
                                        {
                                            "type": "postback",
                                            "title": "Old Mutual Properties",
                                            "payload": "OM_PROPERTIES"
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                }, "type": "postback"
            }
        case TEMPLATE_BUTTON:

        case TEMPLATE_RECEIPT:
        case TEMPLATE_AIRLINE:
        case TEMPLATE_MEDIA:
        case TEMPLATE_PRODUCT:


    }

}

function sendResponse(responseData, sender_psid,country_code) {

    // console.log("==> Messenger service: responseData: " + responseData.type + " responseMsg: Sent. PSID: " + sender_psid);
    if (responseData.type === 'postback') {
        callSendAPI(sender_psid, responseData.message,country_code);
    } else if (responseData.type === 'message') {
        this.sendTextMessage(sender_psid, responseData.message,country_code);
    } else if (responseData.type === 'end') {
        this.sendTextMessage(sender_psid, responseData.message,country_code);
    }
};

function callSendAPI(sender_psid, response,country_code, cb = null) {
    // Construct the message body
    let request_body = {
        recipient: {
            id: sender_psid
        },
        message: response
    };

    // Send the HTTP request to the Messenger Platform
    request({
        "uri": "https://graph.facebook.com/v5.0/me/messages",
        "qs": {"access_token": getAccessToken(country_code)},
        "method": "POST",
        "json": request_body
    }, (err, res, body) => {
        if (!err) {
            if (cb) {
                cb();
            }
        } else {
            console.error("Unable to send message: - Error: Suspect postback formation e.g. type Error: " + err);
        }
    });
}

function callSendAPI2(messageData,country_code) {
    request({
        uri: 'https://graph.facebook.com/v5.0/me/messages',
        qs: {access_token: getAccessToken(country_code)},
        method: 'POST',
        json: messageData

    }, function (error, response, body) {
        if (!error && response.statusCode === 200) {
        } else {
            console.error("Unable to send message.");
            console.error("Error: Suspect message formation e.g. type");
        }
    });
}


function sendTextMessage(recipientId, messageText,country_code) {
    var messageData = {
        recipient: {
            id: recipientId
        },
        message: {
            text: messageText
        }
    };
    // call the send API
    callSendAPI2(messageData,country_code);
};

function sendTextMessageFunc(recipientId, messageText) {
    var messageData = {
        recipient: {
            id: recipientId
        },
        message: {
            text: messageText
        }
    };
    // call the send API
    callSendAPI2(messageData);
};

function getFBDetails(psid, country_code, callback) {
    try {
        request.get({
            url: 'https://graph.facebook.com/' + psid + '?fields=first_name,last_name,profile_pic&access_token=' + getAccessToken(country_code),
            json: true
        }, function (error, response, body) {
            if (!error && response.statusCode === 200) {
                callback(false, body);
            }
            else
                callback(true, error);
        });
    } catch (e) {
        console.log('Error: ' + e);
        callback(true, error);
    }
}

function getAccessToken(country_code) {

    if (country_code === 'na')
        return 'EAAQh0fIdZAw8BAFmzs5cvp7ZAD0r3GFQHSufKD2iNflqdj05F5TGXkA8188OtP5pfIgAubOHm7akCOhLxDv189oOZBXPwqDpEdioxLtGqW1uVpruqrjZB4hHeczLDtsubpjc4Y6DFznLJGJR936wVfWcOTANUJZAm1OdZCDu1FpAZDZD'
    else if (country_code === 'sz')
        return 'EAAIKLZCskcZBYBANYg8yEOfy9TfhQHR7VCFdp1MUNqscmGVwQlPTxVipmCmGxi7Ep2CKNfLPZAEmbakOFpHRaU8UufXcauPYZC8qHpDnnmnqTISmbVUvNO5STORd0ZCUnOutSCzFqDuVOszWFJSApJY2zQL6klBMUlq8WQzJEXgZDZD'
    else if (country_code === 'bw')
        return 'EAAmsftZAHcKYBAIqzq22tJZCdKLmd8gZBimQgakvwZCnYFQ863bFaqeQFF6jST00ZBZCd4VKyGZBC0fWw8vIPLXgW59FZAqpuCCLFgWDjA9ChswV6KgzwzDOew7jl8Dalh1mrlg3k163yyiAWbsO6PGTkPlGJe53ZCq0cDYZAvgiIOfQZDZD'
    else if (country_code === 'mw')
        return 'EAAIgurZAJ2gcBAAhIuJRU29PfBHZAaGB9UWJBeWGZAYyTnBgZCz4zCtPpT7k99eZCZCxZA3IP0OYTu0se9xquUirg3dzAZCCUuwUspI2yalpYAmfAbnWgzPTho3AbXO1YCVCAKRZBp0SF3ZAeZCDyiYnMXefblr3ZBYtclLYr1al8U1vwAZDZD'
     else if (country_code === 'zw')
        return 'EAAER7t3NuAkBAPBhfovUZACJp0y1DIGV2sy1AuDnsVEswXE0JjSoCueEa5ZBpLA7r1UsXWsdZAzZAq4i2sR0wBdsAh1RDIAVApDqYMcWqmconteT8G9xKLw2gZCXcCPNFLZCb632QC5ysaM9ZAfbib8LVZCsTRA0ZAhiO81GNRhBQgwZDZD'
      else if (country_code === 'ke')
        return 'EAAupe8ZBJaZBoBAEARMPq0u6Sg6pNUorZC7humyjOMP0SVxNvkpn8foZCVPsXv9rdCuxc1aCrRZBAFzjwYDdITROEqUzSTisqQ6lpJtvrN0dQb2vududdYshW4z26tSRsr36pkSzVeKo7afZC2JOZCD5156pQVpZCUFToaAOZBZAJHDAZDZD'
    else // Test page
        return 'EAAhAPHAsUUYBADsrnXORwON01ZAZAhVZCHgCt7kNAAJ2kY73vfp0L1zynQCtUJxv3frP1skWPAPLjys7W7Jod0H2kX0RVFNrzajeVqoW79vjbmh2ZBPYdL8zVbcxb9PnL5yQhGlUeBf4j5s0JanSVk1DmDrIeySjMg77e4jWtwZDZD'
}

function call (path, payload,country_code, callback) {
    const access_token = getAccessToken(country_code);
    const graph_url = 'https://graph.facebook.com/v5.0/me';

    if (!path) {
        console.error('No endpoint specified on Messenger send!');
        return;
    } else if (!access_token || !graph_url) {
        console.error('No Page access token or graph API url configured!');
        return;
    }

    request({
        uri: graph_url + path,
        qs: {'access_token': access_token},
        method: 'POST',
        json: payload,
    }, (error, response, body) => {
        console.log(body)
        if (!error && response.statusCode == 200) {
            // console.log('Message sent succesfully');
        } else {
            console.error('Error: ' + error);
        }
        callback(body);
    });
};

module.exports = {
    call
};

module.exports = {
    callSendAPI: callSendAPI,
    sendTextMessageFunc: sendTextMessageFunc,
    sendTextMessage: sendTextMessage,
    sendResponse: sendResponse,
    getUserFBDetails: getFBDetails,
    templateService: templateService
};