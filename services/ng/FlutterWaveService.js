'use strict';

let dbService = require('./DbService');
const Flutterwave = require('flutterwave-node-v3');

// const flw = new Flutterwave('FLWPUBK-f4076cf6f5e7c2f865a756f35b63d004-X', process.env.FLUTTERWAVE_SECRET_KEY  );
const flw = new Flutterwave('FLWPUBK-f4076cf6f5e7c2f865a756f35b63d004-X',"2222234"  );
async function receiveSaleData(params, callback) {
    try {

        let data = await dbService.updateTransactionData(params)
        console.log('receiveSaleData Data->: ' + JSON.stringify(data))
        if (data) {
            callback(false, {
                system_error: false,
                user_exists: false,
                user_message: {error: false, message: "Success"},
                system_message: 'User not found on db'
            })
        }
        else
            callback(false, {
                system_error: false,
                user_exists: false,
                user_message: 'User account does not exist',
                system_message: 'User not found on db'
            })
    } catch (e) {
        console.log('receiveSaleData Error->: ' + JSON.stringify(e))
        callback(true, {
            system_error: true,
            user_exists: false,
            user_message: 'An error occurred processing your request. Please try again',
            system_message: e
        })
    }

}
const ussd = async () => {


    try {

        const payload = {
            "tx_ref": "MC-15852309v5050e8", //This is a unique reference, unique to the particular transaction being carried out. It is generated when it is not provided by the merchant for every transaction.
            "account_bank": "058", //This is the Bank numeric code e.g 058
            "amount": "1500",
            "currency": "NGN",
            "email": "<EMAIL>",
            "phone_number": "***********",
            "fullname": "Yemi Desola"
        }

        const response = await flw.Charge.ussd(payload)
        console.log(response);
    } catch (error) {
        console.log(error)
    }

}

const resendHooks = async () => {

    try {


        const payload = {
            "tx_ref": "rave-123wsvgfwefcwsfc456"
        }
        const response = await flw.Transaction.resend_hooks(payload)
        console.log(response);
    } catch (error) {
        console.log(error)
    }

}
const processFlutterWaveCallback = async (req,res) => {

    try {

        /* It is a good idea to log all events received. Add code *
        * here to log the signature and body to db or file       */

        /*
         {
  "event": "transfer.completed",
  "event.type": "Transfer",
  "data": {
    "id": 33286,
    "account_number": "**********",
    "bank_name": "ACCESS BANK NIGERIA",
    "bank_code": "044",
    "fullname": "Bale Gary",
    "created_at": "2020-04-14T16:39:17.000Z",
    "currency": "NGN",
    "debit_currency": "NGN",
    "amount": 30020,
    "fee": 26.875,
    "status": "SUCCESSFUL",
    "reference": "a0a827b1eca65311_PMCKDU_5",
    "meta": null,
    "narration": "lolololo",
    "approver": null,
    "complete_message": "Successful",
    "requires_approval": 0,
    "is_approved": 1
  }
}*/

        // retrieve the signature from the header
        var hash = req.headers["verif-hash"];

        if(!hash) {
            // discard the request,only a post with the right Flutterwave signature header gets our attention
        }

        // Get signature stored as env variable on your server
        const secret_hash = process.env.MY_HASH;

        // check if signatures match

        if(hash !== secret_hash) {
            // silently exit, or check that you are passing the right hash on your server.
        }

        // Retrieve the request's body
        var request_json = JSON.parse(req.body);

        console.log('Receive FLW data: '+JSON.stringify(request.body))

        res.send(200);
    } catch (error) {
        console.log(error)
    }

}

module.exports = {
    receiveSaleData,resendHooks, processFlutterWaveCallback
}