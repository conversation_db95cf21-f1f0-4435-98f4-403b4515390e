'use strict';
let dbService = require('./DbService');

async function saveSession(params, callback) {
    try {
        let data = await dbService.createSession(params)
        console.log('Session Data->: ' + JSON.stringify(data))
        if (data)
            callback(false, data)
        else
            callback(true, {
                system_error: false,
                user_exists: false,
                user_message: 'An error occurred initiating the product purchase\nError Code:P1S01',
                system_message: 'An issue initiating the sale transaction on the db'
            })
    } catch (e) {
        callback(true, {
            system_error: true,
            user_exists: false,
            user_message: 'An error occurred processing your request. Please try again',
            system_message: e
        })
    }
}
async function updateSession(params, callback) {
    try {
        let data = await dbService.updateUserSession(params)
        console.log('Session Data->: ' + JSON.stringify(data))
        if (data)
            callback(false, data)
        else
            callback(true, {
                system_error: false,
                user_exists: false,
                user_message: 'An error occurred initiating the product purchase\nError Code:P1S01',
                system_message: 'An issue initiating the sale transaction on the db'
            })
    } catch (e) {
        callback(true, {
            system_error: true,
            user_exists: false,
            user_message: 'An error occurred processing your request. Please try again',
            system_message: e
        })
    }
}

module.exports = {
    saveSession: saveSession,
    updateSession: updateSession,
}