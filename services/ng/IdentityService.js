'use strict';
let dbService = require('./DbService');
const bcrypt = require('bcrypt');

async function getUserDetails(phone_no, callback) {
    try {
        let data = await dbService.getUserData(phone_no)
        console.log('Data->: ' + JSON.stringify(data))
        if (data)
            callback(false, data)
        else
            callback(true, {
                system_error: false,
                user_exists: false,
                user_message: 'User account does not exist',
                system_message: 'User not found on db'
            })
    } catch (e) {
        callback(true, {
            system_error: true,
            user_exists: false,
            user_message: 'An error occurred processing your request. Please try again',
            system_message: e
        })
    }
}

async function createUser(params, callback) {
    try {
        let data = await dbService.createUserData(params)
        console.log('Create User->: ' + JSON.stringify(data))
        if (data)
            callback(false, data)
        else
            callback(true, {
                system_error: false,
                user_exists: false,
                user_message: 'Error creating your record.',
                system_message: 'Error creating user on DB'
            })
    } catch (e) {
        callback(true, {
            system_error: true,
            user_exists: false,
            user_message: 'An error occurred processing your request. Please try again',
            system_message: e
        })
    }
}

async function updateUser(params, callback) {
    try {
        let data = await dbService.updateUserData(params)
        console.log('Update Data->: ' + JSON.stringify(data))
        if (data)
            callback(false, data)
        else
            callback(true, {
                system_error: false,
                user_exists: false,
                user_message: 'Error creating your record.',
                system_message: 'Error creating user on DB'
            })
    } catch (e) {
        callback(true, {
            system_error: true,
            user_exists: false,
            user_message: 'An error occurred processing your request. Please try again',
            system_message: e
        })
    }
}

async function loginUser(user_id,pin, callback) {
    try {
        let data = await dbService.getUserAuthDetail(user_id)
        console.log('Update Data->: ' + JSON.stringify(data))
        if (data){
            bcrypt.compare(pin, data.password_hash).then(function (result) {
                if (result) {
                    console.log('Login ' + result)
                    callback(false)
                } else {
                    console.log('Error Login: Wrong pass')

                    callback(true)
                }


            });
        }
        else
            callback(true)
    } catch (e) {
        callback(true, {
            system_error: true,
            user_exists: false,
            user_message: 'An error occurred processing your request. Please try again',
            system_message: e
        })
    }
}

 function createUserPIN(params,pin, callback) {

    bcrypt.hash(pin, 10,  async function (err, hash) {

        let auth_params = {
            password_hash: hash,
            user_id: params.user_id
        }
        console.log('AUTH REQ: ' + JSON.stringify(auth_params)+' - '+hash)
        let data =   await dbService.createUserCredentialsData(auth_params)
        console.log('AUTH: ' + JSON.stringify(data))

        if (data)
            callback(false, data)
        else
            callback(true, {
                system_error: false,
                user_exists: false,
                user_message: 'Error creating your PIN.',
                system_message: 'Error creating PIN on DB'
            })


    });


}


module.exports = {
    getUserDetails: getUserDetails,
    createUser: createUser,
    updateUser: updateUser,
    loginUser: loginUser,
    createUserPIN: createUserPIN,
}