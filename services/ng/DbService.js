'use strict';
let db = require('../../models');

// var config = require("../../config");

var Sequelize = require("sequelize")
var env = process.env.NODE_ENV || 'development';
var config = require(__dirname + '/../../config/config.json')[env];

let initModels = require('../../models/init-models').initModels;
var sequelize = new Sequelize(config.database, config.username, config.password, config.options);
var models = initModels(sequelize);
var User = models.ng_user;
var UserAuth = models.ng_user_auth;
var Session = models.ng_session;
var ProductTransaction = models.ng_product_transaction;
var Currency = models.ng_currency;
var Bank = models.ng_bank;
var PaymentStatus = models.ng_payment_status;
var PaymentVendor = models.ng_payment_vendor;
var RegistrationDcumentType = models.ng_registration_document_type;
var Products = models.ng_products;
var AccessChannel = models.access_channel;


async function updateSession(params, callback) {
    db.ng_fb_user_session.upsert(params).then(result => {
        if (result)
            callback(false, result)
        else {
            callback(true)
        }
    })
}

async function getUserData(params) {
    return await db.ng_user.findOne({
            where: {phone_no: params, registration_status: 'COMPLETE'}
        }
    )
}

async function createUserData(params) {
    return await User.create(params)
}

async function getProductTransactionData(params) {
    return await ProductTransaction.findAll({
        where: params,
        include: [
            {
                model: User,
                attributes: ['user_id', 'first_name', 'last_name', 'full_name', 'gender', 'registration_no'],

                include: [{
                    model: RegistrationDcumentType,
                    attributes: ['name']
                }]
            },
            {
                model: Currency,
                attributes: ['code']
            },
            {
                model: PaymentStatus,
                attributes: ['status']
            },
            {
                model: AccessChannel,
                attributes: ['name']
            },
            {
                model: PaymentVendor,
                attributes: ['name']
            },
            {
                model: Products,
                attributes: ['name']
            },
            {
                model: Bank,
                attributes: ['name', 'code']
            }

        ]
    })
}
async function updateTransactionData(params) {
    return await ProductTransaction.update(params, {where:{reference_no_internal: params.reference_no_internal}})
}
async function updateUserData(params) {
    return await db.ng_user.update(params, {user_id: params.user_id})
}

async function createUserCredentialsData(params) {
    return await db.ng_user_auth.create(params)
}

async function createProductTransaction(params) {
    return await ProductTransaction.create(params)
}

async function createUserSession(params) {
    return await Session.create(params)
}

async function updateUserSession(params) {
    return await Session.create(params)
}

async function getUserAuthDetail(user_id) {
    return UserAuth.findOne({where: {user_id: user_id}, include: [User]})
}

async function getBanks(params = null) {
    if (params)
        return Bank.findAll(params)
    else
        return Bank.findAll()

}

async function getPostbackMenu(unique_identifier, callback) {
    db.ng_fb_menu.findOne({
            where: {unique_identifier: unique_identifier}
        }
    ).then(
        result => {
            if (result) {
                callback(false, result)
            }
        });
}

async function getUserLevel(psid, callback) {
    db.ng_fb_user_session.findOne({
            where: {psid: psid}
        }
    ).then(
        result => {
            if (result) {
                callback(false, result)
            } else {
                callback(true, 'User does not exit');
            }
        });
}

async function getAnswer(unique_identifier, callback) {
    db.ng_fb_faq.findAll({
            where: {unique_identifier: unique_identifier}
        }
    ).then(
        result => {
            if (result) {
                callback(false, result)
            }
        });
}

module.exports = {
    updateUserSession: updateSession,
    getPostbackMenu: getPostbackMenu,
    getUserLevel: getUserLevel,
    getAnswer: getAnswer,
    getUserData: getUserData,
    createUserData: createUserData,
    updateUserData: updateUserData,
    createUserCredentialsData: createUserCredentialsData,
    getUserAuthDetail: getUserAuthDetail,
    createProductTransaction: createProductTransaction,
    updateSession: updateUserSession,
    createSession: createUserSession,
    getProductTransactionData: getProductTransactionData,
    getBanks: getBanks,
    updateTransactionData: updateTransactionData,
}