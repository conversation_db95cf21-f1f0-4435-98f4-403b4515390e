exports.webhookVerify = function (request, res) {
    /*res.json({
        error: false,
        msg: 'success'
    })*/
    req = request;
    // Your verify token. Should be a random string.
    let VERIFY_TOKEN = "ng-chatbot";

    console.log('WEBHOOK_VERIFIED');
    // console.log(' ==> req: ' + (req.body).toString());
    // Parse the query params
    let mode = req.query['hub.mode'];
    let token = req.query['hub.verify_token'];
    let challenge = req.query['hub.challenge'];
    // Checks if a token and mode is in the query string of the request
    if (mode && token) {

        // Checks the mode and token sent is correct
        if (mode === 'subscribe' && token === VERIFY_TOKEN) {

            // Responds with the challenge token from the request
            console.log('WEBHOOK_VERIFIED: ' + challenge);
            // res.json(challenge);
            return challenge;
        } else {
            // Responds with '403 Forbidden' if verify tokens do not match
            res.sendStatus(403);
        }
    }
};