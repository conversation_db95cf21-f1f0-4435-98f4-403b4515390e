'use strict';
let menuContent = require('./NgUssdMenuContent');
let dbService = require('../../../services/ng/DbService');
const ERR_GET_MENU = 'EGM';

function getMenu(unique_identifier, level) {
    try {
        /*let rawdata = fs.readFileSync(path.join(__dirname, '../config/menus.json'));
        // console.log('---- menu ' + JSON.stringify(JSON.parse(rawdata)[level][unique_identifier].title));
        return JSON.parse(rawdata)[level][unique_identifier].title;*/
        let rawdata = menuContent.getMenuContent();
        return rawdata[level][unique_identifier].title;
    }
    catch
        (e) {
        console.log('Error getMenu: ' + e)
    }
}
function getMenuData(unique_identifier, level) {
    try {
        let rawdata = menuContent.getMenuContent();
        return rawdata[level][unique_identifier];
    }
    catch
        (e) {
        console.log('Error getMenu: ' + e)
    }
}

async function getMenuFromDb(callback) {
    try {
        let data = await dbService.getBanks()
        console.log('Menu Data->: ' + JSON.stringify(data))
        if (data) {
            let menu = "Please select your bank\n"
            await data.forEach(function (item, index) {
                menu += index + 1 + ". " + item.name + "\n"
            })

            callback(false, menu)
        }
        else
            callback(true, {
                system_error: false,
                user_exists: false,
                user_message: 'An error occurred initiating the product purchase\nError Code:P1S01',
                system_message: 'An issue initiating the sale transaction on the db'
            })
    } catch (e) {
        callback(true, {
            system_error: true,
            user_exists: false,
            user_message: 'An error occurred processing your request. Please try again',
            system_message: e
        })
    }
}

function getMenuByIndex(unique_identifier, level, index) {
    try {
        /*let rawdata = fs.readFileSync(path.join(__dirname, '../config/menus.json'));
        // console.log('---- menu ' + JSON.stringify(JSON.parse(rawdata)[level][unique_identifier].title));
        return JSON.parse(rawdata)[level][unique_identifier][index];*/
        let rawdata = menuContent.getMenuContent();
        return rawdata[level][unique_identifier][index];
    }
    catch
        (e) {
        console.log('Error getMenuByIndex: ' + e)
    }
}

function getMenuTags(unique_identifier, level) {
    try {
        /*let rawdata = fs.readFileSync(path.join(__dirname, '../config/menus.json'));
        // console.log('---- next ' + JSON.stringify(JSON.parse(rawdata)[level][unique_identifier].next));
        return JSON.parse(rawdata)[level][unique_identifier].next;*/
        let rawdata = menuContent.getMenuContent();
        return rawdata[level][unique_identifier].next;
    } catch (e) {
        console.log('Error getMenuTags: ' + e)

    }

}


function getErrorMsg(location) {
    return 'An error occurred please try again.\n Error code:' + ERR_GET_MENU + '_' + location;

}


module.exports = {
    getMenu: getMenu,
    getMenuTags: getMenuTags,
    getMenuByIndex: getMenuByIndex,
    getMenuFromDb: getMenuFromDb,
    getMenuData: getMenuData
};