exports.getMenuContent = function () {
    return {
        "1": {
            "user_new": {
                "title": "Welcome to Old Mutual Nigeria. You are currently not registered on this platform. Please enter your BVN or National ID number. t's & c's apply",
                "next": {
                    "*\\w+": "welcome.national_id",
                }
            },
            "user_exists": {
                "title": "Welcome to Old Mutual Nigeria %s1, please enter your secret 4 digit PIN code.",
                "next": {
                    "*\\d+": "entry.pin",
                }
            },
            "first_name": {
                "title": "Please enter your First name e.g. <PERSON>",
                "next": {
                    "*\\w+": "entry.first_name",
                }
            },
            "last_name": {
                "title": "Please enter your last name e.g. <PERSON><PERSON>",
                "next": {
                    "*\\w+": "entry.last_name",
                }
            }
        },
        "2": {
            "welcome": {
                "title": "%s1.\nPlease confirm that the KYC details above are yours.\n1. Yes\n2. No\n00 Back",
                "next": {
                    "1": "confirm_details_yes",
                    "2": "confirm_details_no",
                    "0": "back_to_main"
                }
            },
            "choose_service": {
                "title": "1. Insure with us\n2. Invest with us",
                "next": {
                    "1": "insure",
                    "2": "invest",
                    "3": "my_portfolio",
                    "0": "back_to_main"
                }
            },
            "wrong_pin": {
                "title": "Wrong PIN, please try again.",
                "next": {
                    "0": "back_to_main"
                }
            },
            "update_kyc": {
                "title": "Dear Customer, Please update your KYC from your Telco provider.\nThank you!\n00 Back",
                "next": {
                    "1": "policy_status_options",
                    "2": "policy_status_options",
                    "3": "policy_status_options",
                    "4": "policy_status_options",
                    "0": "back_to_main"
                }
            }
        },
        "3": {
            "confirm_details_yes": {
                "title": "Please set your secret four digit PIN code",
                "next": {
                    "*\\d+": "enter_pin.code",
                }
            },
            "re_enter_pin": {
                "title": "Re-enter your secret four digit PIN code\n00 Back",
                "next": {
                    "*\\d+": "re_enter_pin.code",
                }
            },
            "confirm_details_no": {
                "title": "Dear Customer, Please update your KYC from your Telco provider.\nThank you!",
                "next": {
                    "1": "end",
                }
            },

            "insure": {
                "title": "1. Third Party Cover\n2. TravelSure\n\n00. Back\n0. Main Menu",
                "product_data": [
                    {
                        "name": "Third Party Cover",
                    },
                    {
                        "name": "TravelSure",
                    },
                ],
                "next": {
                    "1": "third_party_cover",
                    "2": "travel_sure",
                    "0": "back_to_main"
                }
            }, "invest": {
                "title": "1. Family Risk Plan\n\n00. Back\n0. Main Menu",
                "product_data": [
                    {
                        "name": "Family Risk Plan",
                    },
                ],"next": {
                    "1": "family_risk_plan",
                    "0": "back_to_main"
                }
            }, "my_portfolio": {
                "title": "1. Renewals\n2. Make a claim\n3. Manage beneficiaries\n4. Make a Complaint\n\n00. Back\n0. Main Menu",
                "next": {
                    "1": "cover_renewals",
                    "2": "make_claim",
                    "3": "beneficiary_management",
                    "4": "make_complaint",
                    "0": "back_to_main"
                }
            },
        },
        "4": {
            "registration_success": {
                "title": "Your registration has been successful. You will receive an SMS shortly with instructions on how to proceed.",
                "next": {
                    "1": "user_exists",
                    "0": "back_to_main"
                }
            }, "third_party_cover": {
                "title": "Enter Car Registration Number\n\n00. Back\n0. Main Menu",
                "next": {
                    "*\\w+": "entry.car_reg_no",
                    "0": "back_to_main"
                }
            }, "travel_sure": {
                "title": "Select a preferred location category:\n1. Shengen\n2. Worldwide excl. USA/Canada\n3. Worldwide\n\n00. Back\n0. Main Menu",
                "product_data": [
                    {
                        "name": "Shengen",
                    },
                    {
                        "name": "Worldwide excl. USA/Canada",
                    },
                    {
                        "name": "Worldwide",
                    },
                ], "next": {
                    "1": "travel_duration",
                    "2": "travel_duration",
                    "3": "travel_duration",
                    "0": "back_to_main"
                }
            }, "car_licence_no": {
                "title": "Enter License Number\n\n00. Back\n0. Main Menu",
                "next": {
                    "*\\w+": "entry.car_reg_no",
                    "0": "back_to_main"
                }
            }, "vehicle_type": {
                "title": "Please select your vehicle type:\n1. Saloon/SUV -5,000\n2. Bus -7,500\n3. Mini-truck/Tippers -10,000\n4. Trucks -25,000\n\n00. Back\n0. Main Menu",
                "product_data": [
                    {
                        "name": "Saloon/SUV",
                        "amount": 5000,
                    },
                    {
                        "name": "Bus",
                        "amount": 7500,
                    },
                    {
                        "name": "Mini-truck/Tippers",
                        "amount": 10000,
                    },
                    {
                        "name": "Trucks",
                        "amount": 25000,
                    },
                ],
                "next": {
                    "1": "vehicle_type_selection",
                    "2": "vehicle_type_selection",
                    "3": "vehicle_type_selection",
                    "4": "vehicle_type_selection",
                    "0": "back_to_main"
                }
            }, "family_risk_plan": {
                "title": "How much would you like to cover for:\n1. >600,000: 1,500\n2. >1,200,000: 3,000\n3. >1,200,000: 3,000\n4. >3,000,000: 7,500\n5. >4,000,000: 10,000\n5. >5,000,000: 12,500\n\n00. Back\n0. Main Menu",
                "product_data": [
                    {
                        "name": ">600,000",
                        "amount": 1500,
                    },
                    {
                        "name": ">1,200,000",
                        "amount": 3000,
                    },
                    {
                        "name": " >3,000,000",
                        "amount": 7500,
                    },
                    {
                        "name": " >4,000,000",
                        "amount": 10000,
                    },
                    {
                        "name": ">5,000,000 ",
                        "amount": 12500,
                    },
                ],"next": {
                    "1": "invest_beneficiary_name",
                    "2": "invest_beneficiary_name",
                    "3": "invest_beneficiary_name",
                    "4": "invest_beneficiary_name",
                    "0": "back_to_main"
                }
            }, "make_claim": {
                "title": "1. Third Party Cover\n 2. TravelSure\n 3. Family Risk Plan\n\n00. Back\n0. Main Menu",
                "next": {
                    "1": "enter_claim_policy_no",
                    "2": "enter_claim_policy_no",
                    "3": "enter_claim_policy_no",
                    "0": "back_to_main"
                }
            }, "cover_renewals": {
                "title": "1. Third Party Cover\n2. TravelSure\n 3. Family Risk Plan\n\n00. Back\n0. Main Menu",
                "next": {
                    "1": "enter_renewal_policy_no",
                    "2": "enter_renewal_policy_no",
                    "3": "enter_renewal_policy_no",
                    "0": "back_to_main"
                }
            }, "beneficiary_management": {
                "title": "1. TravelSure\n2. Family Plan\n\n00. Back\n0. Main Menu",
                "next": {
                    "1": "beneficiary_action",
                    "2": "beneficiary_action",
                    "0": "back_to_main"
                }
            }, "make_complaint": {
                "title": "Select a complaint type: \n1. Not receiving SMS/Email\n2. Change of Beneficiary\n 3. Claims- Declined\n 4. Debit Issues\n 5. Payment Issues\n 6. Call Me Back\n\n00. Back\n0. Main Menu",
                "next": {
                    "1": "submit_complaint",
                    "2": "submit_complaint",
                    "3": "submit_complaint",
                    "4": "submit_complaint",
                    "5": "submit_complaint",
                    "6": "submit_complaint",
                    "0": "back_to_main"
                }
            },
        },
        "5": {
            "enter_claim_policy_no": {
                "title": "Enter your policy number e.g. TPO-1234\n\n00. Back\n0. Main Menu",
                "next": {
                    "*\\w+": "entry.claim_policy_no",
                    "0": "back_to_main"
                }
            },
            "enter_renewal_policy_no": {
                "title": "Enter your policy number e.g. TPO-1234\n\n00. Back\n0. Main Menu",
                "next": {
                    "*\\w+": "entry.renewal_policy_no",
                    "0": "back_to_main"
                }
            },
            "enter_policy_no_beneficiary": {
                "title": "Enter your policy number e.g. TPO-1234\n\n00. Back\n0. Main Menu",
                "next": {
                    "*\\w+": "entry.policy_no_beneficiary",
                    "0": "back_to_main"
                }
            },
            "invest_beneficiary_name": {
                "title": "Enter beneficiary's full name and relationship\n (e.g. John Doe- Father)\n\n00. Back\n0. Main Menu",
                "next": {
                    "*\\w+": "entry.invest_beneficiary_name",
                    "0": "back_to_main"
                }
            },
            "beneficiary_action": {
                "title": "1. Add Beneficiary\n2. Remove Beneficiary\n\n00. Back\n0. Main Menu",
                "next": {
                    "1": "add_beneficiary",
                    "2": "remove_beneficiary",
                    "0": "back_to_main"
                }
            },
            "claim_policy_no": {
                "title": "Total Premium: NGN5,000.00\n Car Registration Number: IKJ345DN\n Car Make: BMW 1 Series\n BVN: FJ12313523\n\n1. Make Claim\n2. Request a call back\n0. Main Menu\n00. Back",
                "next": {
                    "1": "complete_make_claim",
                    "2": "request_callback",
                    "0": "back_to_main"
                }
            },
            "renewal_policy_no": {
                "title": "Total Premium: NGN5,000.00\n Car Registration Number: IKJ345DN\n Car Make: BMW 1 Series\n BVN: FJ12313523\n\n1. Make Payment\n2. Request a call back\n0. Main Menu\n00. Back",
                "next": {
                    "1": "make_payment",
                    "2": "request_callback",
                    "0": "back_to_main"
                }
            },
            "third_party_cover_claim": {
                "title": "Enter beneficiary's full name and relationship\n (e.g. John Doe- Father)\n\n00. Back\n0. Main Menu",
                "next": {
                    "*\\w+": "entry.invest_beneficiary_name",
                    "0": "back_to_main"
                }
            },
            "invest_pay_option": {
                "title": "Cover Benefit Limit\nFamily Risk Cover: %s1\n Monthly Premium: NG%s2\n1. Make payment\n2. Request a call back\n0. Main Menu\n00. Back",
                "next": {
                    "1": "make_payment",
                    "2": "request_callback",
                    "0": "back_to_main"
                }
            },
            "vehicle_type_selection": {
                "title": "Total Premium: %s1 Car Registration Number: %s2\n1. Make payment\n2. Request a call back\n\n0. Main Menu\n00. Back",
                "title_": "Total Premium: NGN5,000.00 Car Registration Number: %s2 \n1. Make payment\n2. Request a call back\n\n0. Main Menu\n00. Back",
                "next": {
                    "1": "make_payment",
                    "2": "request_callback",
                    "0": "back_to_main"
                }
            },
            "travel_duration": {
                "title": "1. Up to 7 days: 3,360.71\n2. Up to 10 days: 4,170.47\n3. Up to 15 days: 5,336.79\n4. Up to 21 days: 6,821.20\n5. Up to 30 days: 7,952.18\n6. Up to 60 days: 10,167.42\n7. Up to 90 days: 13,726.02\n8. Up to 180 days: 19,216.42\n9. Annual multi trip: 6,902.99\n00. Back",
                "product_data": [
                    {
                        "name": "Up to 7 days: 3,360.71",
                        "amount": 3360.71,
                    },
                    {
                        "name": "Up to 10 days: 4,170.47",
                        "amount": 4170.47,
                    },
                    {
                        "name": "Up to 15 days: 5,336.79",
                        "amount": 5336.79,
                    },
                    {
                        "name": "Up to 21 days: 6,821.20",
                        "amount": 6821.20,
                    },
                    {
                        "name": "Up to 30 days: 7,952.18",
                        "amount": 7952.18,
                    },
                    {
                        "name": "Up to 60 days: 10,167.42",
                        "amount": 10167.42,
                    },
                    {
                        "name": "Up to 90 days: 13,726.02",
                        "amount": 13726.02,
                    },
                    {
                        "name": "Up to 180 days: 19,216.42",
                        "amount": 19216.42,
                    },
                    {
                        "name": "Annual multi trip: 6,902.99",
                        "amount": 6902.99,
                    }
                ],
                "next": {
                    "1": "travel_beneficiary_name",
                    "2": "travel_beneficiary_name",
                    "3": "travel_beneficiary_name",
                    "4": "travel_beneficiary_name",
                    "5": "travel_beneficiary_name",
                    "6": "travel_beneficiary_name",
                    "7": "travel_beneficiary_name",
                    "8": "travel_beneficiary_name",
                    "9": "travel_beneficiary_name",
                    "0": "back_to_main"
                }
            }
        },
        "6": {
            "add_beneficiary": {
                "title": "Enter beneficiary's name\ne.g. John Doe\n\n00. Back\n0. Main Menu",
                "next": {
                    "*\\w+": "entry.beneficiary_national_name",
                    "0": "back_to_main"
                }
            },
            "beneficiary_national_id": {
                "title": "Enter beneficiary's national ID\n\n00. Back\n0. Main Menu",
                "next": {
                    "*\\w+": "entry.beneficiary_national_id",
                    "0": "back_to_main"
                }
            },
            "request_beneficiary_addition": {
                "title": "Confirm you are about to add beneficiary: John Doe to policy number 56***7566\n1. Confirm\n2. Cancel\n\n00. Back\n0. Main Menu",
                "next": {
                    "1": "confirm_beneficiary_addition",
                    "2": "cancel_beneficiary_addition",
                    "0": "back_to_main"
                }
            },
            "remove_beneficiary": {
                "title": "Select beneficiary you would like to remove \n\n1. John Doe\n2. Jane Doe\n\n00. Back\n0. Main Menu",
                "next": {
                    "1": "request_beneficiary_removal",
                    "2": "request_beneficiary_removal",
                    "0": "back_to_main"
                }
            },
            "travel_beneficiary_name": {
                "title": "Enter beneficiary\'s full name and relationship (e.g. John Doe - Father)\n\n0. Main Menu\n00. Back",
                "next": {
                    "*[a-zA-Z]+": "entry.beneficiary_name",
                    "0": "back_to_main"
                }
            },
            "confirm_travel_cover": {
                "title": "Cover Benefit Limit\nTravel Sure: %s2\nMonthly Premium: NG%s2\n1. Make payment\n2. Request a call back\n\n0. Main Menu\n00. Back",
                "next": {
                    "1": "make_payment",
                    "2": "request_callback",
                    "0": "back_to_main"
                }
            },
        },
        "7": {
            "request_beneficiary_removal": {
                "title": "Confirm you are about to add beneficiary: John Doe to policy number 56***7566\n1. Confirm\n2. Cancel\n\n00. Back\n0. Main Menu",
                "next": {
                    "1": "confirm_beneficiary_removal",
                    "2": "cancel_beneficiary_removal",
                    "0": "back_to_main"
                }
            },
        },
        "generic": {
            "registration_success": {
                "title": "Your registration has been successful. You will receive an SMS shortly with instructions on how to proceed.",
                "next": {
                    "0": "back_to_main"
                }
            }, "pin_mismatch": {
                "title": "Your PIN doesn\'t match, please enter again.",
                "next": {
                    "0": "back_to_main"
                }
            }, "complete_make_claim": {
                "title": "Your claim has been received and a claim request form has been sent to your email. A contact centre agent will get in touch with you shortly.",
                "next": {
                    "0": "back_to_main"
                }
            }, "submit_complaint_success": {
                "title": "Thank you for your patience. One of our contact centre agents will get in touch with you shortly",
                "next": {
                    "0": "back_to_main"
                }
            }, "make_payment": {
                "title": "Please select your bank\n 1. Zenith\n 2. Stanbic\n 3. First Bank\n\n00. Back\n0. Main Menu",
                "next": {
                    "*\\d+": "entry.bank_option"
                }
            }, "payment_confirmation": {
                "title": "Confirm you are about to make a payment of %s1 \nSelect mobile banking number option for your bank.\n1. Use this number \n2. Other number\n\n00. Back\n0. Main Menu",
                "next": {
                    "1": "payment_current_no",
                    "2": "payment_other_no",
                    "3": "payment_success",
                    "0": "back_to_main"
                }
            }, "payment_current_no":

                {}, "payment_other_no": {
                "title": "Enter your mobile banking number",
                "next": {
                    "*\\d+": "entry.payment_other_no",
                }
            },
            "payment_success": {
                "title": "Your payment for the Third Party Cover of NGN5,000.00 has been successful.\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                }
            }, "request_callback": {
                "title": "One of our contact centre agents will get in touch with you shortly.\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                }
            }, "confirm_beneficiary_addition": {
                "title": "Your add beneficiary notice has been sent to Old Mutual. You will receive an SMS once this has been completed.\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                }
            }, "confirm_beneficiary_removal": {
                "title": "Your remove beneficiary notice has been sent to Old Mutual. You will receive an SMS once this has been completed.\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                }
            }
        }
    }
}