"use strict";

const axios = require('axios');

const dotenv = require("dotenv");

dotenv.config();

const { MOBILEXTRA_URL, MOBILEXTRA_AUTHORIZATION_KEY } = process.env;

// sms integration function to mobile extra

async function sendPaymentSms(phone_number,message) {
    
     let params = {

        message: message,

        sender: 'OLDMUTUAL',

        recipients:phone_number ,
    }
    try{
        const response = await axios.post(MOBILEXTRA_URL, params, {

            headers: {
                'Authorization': `${MOBILEXTRA_AUTHORIZATION_KEY}`,

                'content-type': 'application/json'
            },
        });
        console.log(response.data.response,params.phone_number);

    }catch (err) {

        console.log('error', err.response);

      }
    
}

module.exports = {sendPaymentSms}