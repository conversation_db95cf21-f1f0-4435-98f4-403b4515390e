'use strict';
let dbService = require('./DbService');

async function saveSaleInitialRequest(params, callback) {
    try {
        let data = await dbService.createProductTransaction(params)
        console.log('Sales Data->: ' + JSON.stringify(data))
        if (data)
            callback(false, data)
        else
            callback(true, {
                system_error: false,
                user_exists: false,
                user_message: 'An error occurred initiating the product purchase\nError Code:P1S01',
                system_message: 'An issue initiating the sale transaction on the db'
            })
    } catch (e) {
        callback(true, {
            system_error: true,
            user_exists: false,
            user_message: 'An error occurred processing your request. Please try again',
            system_message: e
        })
    }
}
async function getUnsentSalesData(callback) {
    try {
        let params = {
            pas_sync_status:'PENDING',
            payment_status_id: 3
        }
        let data = await dbService.getProductTransactionData(params)
        console.log('Unsent Data->: ' + data.length)
        if (data)
            callback(false, data)
        else
            callback(true, {
                system_error: false,
                user_exists: false,
                user_message: 'An error occurred initiating the product purchase\nError Code:P1S01',
                system_message: 'An issue initiating the sale transaction on the db'
            })
    } catch (e) {
        callback(true, {
            system_error: true,
            user_exists: false,
            user_message: 'An error occurred processing your request. Please try again',
            system_message: e
        })
    }
}
async function verifySaleOnFlutterwave(callback) {
    try {
        let params = {
            pas_sync_status:'PENDING',
            payment_status_id: 3
        }
        let url = 'https://api.flutterwave.com/v3/transactions/id/verify'
        let data = await dbService.getProductTransactionData(params)
        console.log('Unsent Data->: ' + data.length)
        if (data)
            callback(false, data)
        else
            callback(true, {
                system_error: false,
                user_exists: false,
                user_message: 'An error occurred initiating the product purchase\nError Code:P1S01',
                system_message: 'An issue initiating the sale transaction on the db'
            })
    } catch (e) {
        callback(true, {
            system_error: true,
            user_exists: false,
            user_message: 'An error occurred processing your request. Please try again',
            system_message: e
        })
    }
}

module.exports = {
    saveSaleInitialRequest: saveSaleInitialRequest,
    getUnsentSalesData:getUnsentSalesData
}