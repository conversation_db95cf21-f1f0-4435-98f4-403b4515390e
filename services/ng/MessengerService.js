const request = require('request'),
    config = require('config'),
    _this = this;
// Test page
// let access_token = "EAAhAPHAsUUYBADsrnXORwON01ZAZAhVZCHgCt7kNAAJ2kY73vfp0L1zynQCtUJxv3frP1skWPAPLjys7W7Jod0H2kX0RVFNrzajeVqoW79vjbmh2ZBPYdL8zVbcxb9PnL5yQhGlUeBf4j5s0JanSVk1DmDrIeySjMg77e4jWtwZDZD";

// NG Chatbot page
let access_token = "EAAOPDLF8ZAOYBAExYsW0V1IfA50hR8ZAZAnCIDKuLzdOo3ZCFAx5dClTR3nPbD8D4OSyhpBZCQEoaaNYo6ZALhbyLDn0a5vKL7d30To8mecrD30qJGsrVZA16TbUwdspnHKFdL1VtNlHn7mlA4duYsvcXrCOxhq1eD9wM8kVZB2HrwZDZD";
let session_level_id, sender_psid, unique_identifier;

function sendResponse(responseData,sender_psid) {

    // console.log("==> Messenger service: responseData: " + responseData.type + " responseMsg: Sent. PSID: " + sender_psid);
    if (responseData.type === 'postback') {
        callSendAPI(sender_psid, responseData.message);
    } else if (responseData.type === 'message') {
        this.sendTextMessage(sender_psid, responseData.message);
    } else if (responseData.type === 'end') {
        this.sendTextMessage(sender_psid, responseData.message);
    }
};

function callSendAPI(sender_psid, response, cb = null) {
    // Construct the message body
    let request_body = {
        recipient: {
            id: sender_psid
        },
        message: response
    };

    // Send the HTTP request to the Messenger Platform
    request({
        "uri": "https://graph.facebook.com/v5.0/me/messages",
        "qs": {"access_token": access_token},
        "method": "POST",
        "json": request_body
    }, (err, res, body) => {
        if (!err) {
            if (cb) {
                cb();
            }
        } else {
            console.error("Unable to send message: - Error: Suspect postback formation e.g. type Error: " + err);
        }
    });
}

function callSendAPI2(messageData) {
    request({
        uri: 'https://graph.facebook.com/v5.0/me/messages',
        qs: {access_token: access_token},
        method: 'POST',
        json: messageData

    }, function (error, response, body) {
        if (!error && response.statusCode === 200) {
        } else {
            console.error("Unable to send message.");
            console.error("Error: Suspect message formation e.g. type");
        }
    });
}


function sendTextMessage(recipientId, messageText) {
    var messageData = {
        recipient: {
            id: recipientId
        },
        message: {
            text: messageText
        }
    };
    // call the send API
    callSendAPI2(messageData);
};

function sendTextMessageFunc (recipientId, messageText) {
    var messageData = {
        recipient: {
            id: recipientId
        },
        message: {
            text: messageText
        }
    };
    // call the send API
    callSendAPI2(messageData);
};

function getFBDetails(psid,callback){
    try {
        request.get({
            url: 'https://graph.facebook.com/' + psid + '?fields=first_name,last_name,profile_pic&access_token=' + access_token,
            json: true
        }, function (error, response, body) {
            if (!error && response.statusCode === 200) {
              callback(false,body);
            }
            else
                callback(true,error);
        });
    } catch (e) {
        console.log('Error: ' + e);
        callback(true,error);
    }
}


module.exports = {
    callSendAPI:callSendAPI,
    sendTextMessageFunc:sendTextMessageFunc,
    sendTextMessage:sendTextMessage,
    sendResponse:sendResponse,
    getUserFBDetails:getFBDetails
};