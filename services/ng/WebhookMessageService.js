let messengerService = require('./MessengerService.js')
var dbService = require('./DbService');

let salutation_trigger = [
    'hi chatbot', 'hello chatbot', 'helo chatbot', 'halo chatbot', 'hallo chatbot', 'hey chatbot','hi','menu','help','hello','hallo'];
let salutation_response = ['Hi', 'Hello', 'Hello', 'Hey', 'Hey', 'Hey','Hi','Hi','Hi','Hello','Hi'];

exports.webhookMessages = function (request, res) {
    let req = request;
    let body = req.body;

    if (body.object === 'page') {

        // Iterates over each entry - there may be multiple if batched
        body.entry.forEach(function (entry) {
            // Gets the message. entry.messaging is an array, but
            // will only ever contain one message, so we get index 0
            try {
                if (entry.hasOwnProperty('messaging')) {
                    let webhook_event = entry.messaging[0];

                    let sender_psid = webhook_event.sender.id;
                    // Check if the event is a message or postback and
                    // pass the event to the appropriate handler function
                    if (webhook_event.message) {
                        if (webhook_event.message.quick_reply) {
                            console.log('Sender: ' + sender_psid + ' | Postback: ' + webhook_event.message.quick_reply.payload)
                            dbService.getPostbackMenu(webhook_event.message.quick_reply.payload, function (err, menu_resp) {
                                if (err) {

                                }
                                else {
                                    // console.log('Response: ' + menu_resp.response)
                                    // messengerService.sendTextMessageFunc(sender_psid, resp.response);
                                    messengerService.sendResponse(JSON.parse(menu_resp.response), sender_psid)
                                    dbService.updateUserSession({
                                        psid: sender_psid,
                                        unique_identifier: menu_resp.unique_identifier
                                    }, function (err, msg) {

                                    })

                                }
                            })


                        } else if (webhook_event.message.text) {

                            console.log('Sender: ' + sender_psid + ' | Text: ' + webhook_event.message.text)
                            let unique_identifier = webhook_event.message.text

                            let sal_index = salutation_trigger.indexOf(unique_identifier.toLocaleLowerCase());

                            // console.log('Index: ' + sal_index + ' | Text: ' + webhook_event.message.text)
                            if (sal_index > -1) {
                                // console.log(sender_psid + ' used trigger word')
                                unique_identifier = 'LOB_MENU';
                                dbService.getUserLevel(sender_psid, function (err, user_response) {
                                    if (err) {
                                        initialWelcome(sender_psid, salutation_response[sal_index])
                                    }
                                    else {
                                        getMenu(unique_identifier, sender_psid)
                                    }
                                })
                            }
                            else {
                                let user_input = webhook_event.message.text
                                // console.log(sender_psid + ' no trigger word')
                                dbService.getUserLevel(sender_psid, function (err, user_response) {
                                    if (err) {
                                        /*let index = salutation_trigger.indexOf(webhook_event.message.text.toLocaleLowerCase());
                                        initialWelcome(sender_psid,salutation_response[index])*/
                                        console.log(user_response + ' - ' + sender_psid + ' and has not used trigger keyword')
                                    } else {

                                        // console.log('---> user_response: ' + JSON.stringify(user_response))
                                        dbService.getPostbackMenu(user_response.unique_identifier, function (err, menu_resp) {
                                            if (err) {

                                            }
                                            else {
                                                // console.log('---> menu_resp: ' + JSON.stringify(menu_resp))
                                                if (menu_resp.has_array) {
                                                    dbService.getAnswer(user_response.unique_identifier, function (err, faq_response) {
                                                        if (err) {

                                                        } else {

                                                            // console.log('---> faq_response: ' + faq_response[user_input - 1].question)
                                                            messengerService.sendTextMessage(sender_psid, 'Q: ' + faq_response[user_input - 1].question + '\nA: ' + faq_response[user_input - 1].answer)
                                                            setTimeout(() => {
                                                                getOtherServices('Input another option to learn more e.g 3', user_response.unique_identifier, sender_psid);
                                                            }, 2000);

                                                        }
                                                    })
                                                } else {
                                                    messengerService.sendResponse(JSON.parse(menu_resp.response), sender_psid)
                                                    dbService.updateUserSession({
                                                        psid: sender_psid,
                                                        unique_identifier: menu_resp.unique_identifier
                                                    }, function (err, msg) {

                                                    })

                                                }
                                            }
                                        })

                                    }
                                })
                            }


                        }

                    }
                    else if (webhook_event.postback) {

                    }
                }
            } catch (e) {
                console.error('Webhook parse: ' + e)
            }

        });

        // Returns a '200 OK' response to all requests
        res.status(200).send('EVENT_RECEIVED');
    } else {
        // Returns a '404 Not Found' if event is not from a page subscription
        res.sendStatus(404);
    }


};

function getMenu(unique_identifier, sender_psid) {
    dbService.getPostbackMenu(unique_identifier, function (err, menu_resp) {
        if (err) {

        }
        else {

            messengerService.sendResponse(JSON.parse(menu_resp.response), sender_psid)
            dbService.updateUserSession({
                psid: sender_psid,
                unique_identifier: menu_resp.unique_identifier
            }, function (err, msg) {
                if (err) {

                }
            })

        }
    })
}

function initialWelcome(sender_psid, salutation) {
    let salute = salutation + ' %s1\nI will be you virtual assistant to assist you on Frequently Asked Questions (FAQs)';
    messengerService.getUserFBDetails(sender_psid, function (err, response) {
        if (err) {
            console.log(err);
            console.error('Error getting FB details: ' + err)

        } else {
            dbService.updateUserSession({
                psid: sender_psid,
                fb_name: response.first_name + " " + response.last_name,
                fb_profile_pic_url: response.profile_pic,
                unique_identifier: 'LOB_MENU'
            }, function (err, msg) {
                if (err) {
                }
                else {
                    messengerService.sendTextMessage(sender_psid, parameterizedString(salute, response.first_name))
                    setTimeout(() => {
                        getMenu('LOB_MENU', sender_psid);
                    }, 2000);
                }
            })

        }


    });

}

function getOtherServices(title, go_back, sender_psid) {
    dbService.getPostbackMenu('OTHER_SERVICES', function (err, menu_resp) {
        if (err) {

        }
        else {
            let reply = parameterizedString(menu_resp.response, title, go_back)
            // console.log('---> getOtherServices: ' + reply)
            messengerService.sendResponse(JSON.parse(reply), sender_psid)
        }
    })
}

/***
 * @example parameterizedString("my name is %s1 and surname is %s2", "John", "Doe");
 * @return "my name is John and surname is Doe"
 *
 * @firstArgument {String} like "my name is %s1 and surname is %s2"
 * @otherArguments {String | Number}
 * @returns {String}
 */
const parameterizedString = (...args) => {
    const str = args[0];
    const params = args.filter((arg, index) => index !== 0);
    if (!str) return "";
    return str.replace(/%s[0-9]+/g, matchedStr => {
        const variableIndex = matchedStr.replace("%s", "") - 1;
        return params[variableIndex];
    });
}