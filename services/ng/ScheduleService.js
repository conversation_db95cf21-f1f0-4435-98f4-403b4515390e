const fs = require('fs');
const Excel = require('exceljs');

var cron = require('node-cron');
const ProductSaleService = require('./ProductSaleService');

function initializeScheduledJob() {
    ProductSaleService.getUnsentSalesData(function (err, response) {
        if (err) {
            console.log('Unsent data: ' + response.system_message);
        }
        else {
            console.log('Records found: ' + response.length);
            exTest(response)

        }
    })
    console.log('----------> Starting');

    // cron.schedule('1-5 * * * * * *', () => {
    /*cron.schedule(' * * * * *', () => {


    });*/
}

async function exTest(customer_data) {
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet("My Sheet");

    await customer_data.forEach(function (item) {
        console.log('Item' + JSON.stringify(item));
        worksheet.columns = [
            {header: 'Internal Ref No', key: 'reference_no_internal', width: 32},
            {header: 'External Ref No', key: 'reference_no_external', width: 32},
            {header: 'Amount', key: 'amount', width: 15},
            {header: 'currency_id', key: 'currency_id', width: 32},
            {header: 'paying_phone_number', key: 'paying_phone_number', width: 32},
            {header: 'Bank', key: 'bank', width: 32},
            {header: 'Bank Code', key: 'bank_code', width: 32},
            {header: 'channel_id', key: 'channel_id', width: 32},
            {header: 'Last Name', key: 'last_name', width: 32},
            {header: 'First Name', key: 'first_name', width: 32},
            {header: 'Registration Document Type', key: 'registration_document_type', width: 32},
            {header: 'Registration No', key: 'registration_no', width: 32},
            {header: 'payment_status_id', key: 'payment_status_id', width: 32},
            {header: 'Product', key: 'product', width: 32},
            {header: 'payment_vendor_id', key: 'payment_vendor_id', width: 32},
            {header: 'Date', key: 'payment_response_time', width: 32},
        ];
        worksheet.addRow({
            reference_no_internal: item.reference_no_internal,
            reference_no_external: item.reference_no_internal,
            amount: item.amount,
            currency_id: item.ng_currency.code,
            paying_phone_number: item.paying_phone_number,
            bank: item.ng_bank.name,
            bank_code: item.ng_bank.code,
            channel_id: item.access_channel.name,
            last_name: item.ng_user.last_name,
            first_name: item.ng_user.first_name,
            registration_document_type: item.ng_user.ng_registration_document_type.name,
            registration_no: item.ng_user.registration_no,
            payment_status_id: item.ng_payment_status.status,
            product: item.product_option,
            payment_vendor_id: item.ng_payment_vendor.name,
            payment_response_time: item.payment_response_time,
        });
        // worksheet.addRow({reference_no_external: 2, reference_no_external: 'Jane Doe', dob: new Date(1965, 1, 7)});
    })


// save under export.xlsx
    await workbook.xlsx.writeFile('export.xlsx');
/*
//load a copy of export.xlsx
    const newWorkbook = new Excel.Workbook();
    await newWorkbook.xlsx.readFile('export.xlsx');

    const newworksheet = newWorkbook.getWorksheet('My Sheet');
    newworksheet.columns = [
        {header: 'Id', key: 'id', width: 10},
        {header: 'Name', key: 'name', width: 32},
        {header: 'D.O.B.', key: 'dob', width: 15,}
    ];
    await newworksheet.addRow({id: 3, name: 'New Guy', dob: new Date(2000, 1, 1)});

    await newWorkbook.xlsx.writeFile('export2.xlsx');*/

    console.log("File is written");

};
module.exports = {
    initializeScheduledJob: initializeScheduledJob,
}