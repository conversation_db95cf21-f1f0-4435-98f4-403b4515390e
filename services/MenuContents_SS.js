exports.getMenuContent = function () {
    return {
        "1": {
            "start": {
                "title": "Welcome to UAP Old Mutual Services \n1 Register a Claim \n2 Check Policy Status\n3 Buy insurance \n4 Insurance Payment",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                  
                }
            }
        },
        "2": {
            "register_claim": {
                "title": "Which insurance would you like to register claim for?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. General Insurance \n00 Back",
                "next": {
                    "1": "claim_options",
                    "2": "claim_options",
                    "3": "claim_options",
                    "4": "claim_options",
                    "0": "back_to_main"
                }
            },
            "policy_status": {
                "title": "Policy status for?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. General Insurance\n00 Back",
                "next": {
                    "1": "policy_status_options",
                    "2": "policy_status_options",
                    "3": "policy_status_options",
                    "4": "policy_status_options",
                    "0": "back_to_main"
                }
            },
            "buy_insurance": {
                "title": "Buy for?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. General Insurance\n00 Back",
                "next": {
                    "1": "buy_insurance_options",
                    "2": "buy_insurance_options",
                    "3": "buy_insurance_options",
                    "4": "buy_insurance_options",
                    "0": "back_to_main"
                }
            },
            "make_insurance_payment": {
                "title": "Make payment to?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. General Insurance\n00 Back",
                "next": {
                    "1": "make_insurance_payment_options",
                    "2": "make_insurance_payment_options",
                    "3": "make_insurance_payment_options",
                    "4": "make_insurance_payment_options",
                    "0": "back_to_main"
                }
            },

        },
        "3": {
            "claim_options": {
                "title": "Select how to start the Insurance claim \n1. Provide claim details \n2. Request a call back\n00 Back \n0 Main menu",
                "next": {
                    "1": "provide_details",
                    "2": "request_callback",
                    "0": "register_claim"
                }
            },
            "policy_status_options": {
                "title": "Select how to start your policy status inquiry\n1. Provide claim details \n2. Request a call back\n00 Back \n0 Main menu",
                "next": {
                    "1": "provide_details",
                    "2": "request_callback",
                    "0": "policy_status"
                }
            },
            "buy_insurance_options": {
                "title": "Select how to start buying Insurance\n1. Provide claim details \n2. Request a call back \n00 Back \n0 Main menu",
                "next": {
                    "1": "provide_details",
                    "2": "request_callback",
                    "0": "buy_insurance"
                }
            },
            "make_insurance_payment_options": {
                "title": "Select How to start the Insurance payment  \n1. Provide claim details \n2. Request a call back \n00 Back \n0 Main menu",
                "next": {
                    "1": "provide_details",
                    "2": "request_callback",
                    "0": "make_insurance_payment"
                }
            },
        },
        "4": {
            "provide_details_car": {
                "title": "What\'s your Car registration number? e.g. SAE123A \n\n00 Back \n0 Main menu",
                "next": {
                    "*\\w+": "provide_details.policy_no",
                    "0": "back_to_main"
                }
            },
            "provide_details_other": {
                "title": "What\'s your insurance policy number?\n\n00 Back \n0 Main menu",
                "next": {
                    "*\\w+": "provide_details.policy_no",
                    "0": "back_to_main"
                }
            },
            "provide_details_life": {
                "title": "What\'s your Life Insurance policy number? e.g. OMS001429206 or National ID Number\n\n00 Back \n0 Main menu",
                "next": {
                    "*\\w+": "provide_details.policy_no",
                    "0": "back_to_main"
                }
            },
            "provide_details_health": {
                "title": "What\'s your Health Insurance policy number? e.g. US032344-01 or National ID Number \n\n00 Back \n0 Main menu",
                "next": {
                    "*\\w+": "provide_details.policy_no",
                    "0": "back_to_main"
                }
            },
        },
       
        "generic": {
            "response_options": {
                "next": {
                    "1": "provide_details",
                    "2": "request_callback",
                    "0": "back_to_main"
                }
            },
            "provide_details": {
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                }
            },
            "success_end_message": {
                "title": "Thank you for the details provided. One of our Call center representatives will call you within 24 hours.\n\n0 Main menu",
                "next": {
                    "0": "back_to_main"
                }
            },
            "success_end_message_hw_claim": {
                "title": "We have successfully received your claim notification.\n One of our call center representatives will contact you within 24 Hours.\n\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                }
            },
            "success_end_message_hw_registration": {
                "title": "Your registration has been completed successfully..\n\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                }
            },
            "exit": {
                "title": "Thank you for the details provided, one of our call center agents will call you within 24 hours."
            },
            
            "insurance_class": [
                "Health Insurance",
                "Car Insurance",
                "Life Insurance",
                "General Insurance"
            ]
        }
    }
}