'use strict';
const UssdMenu = require('ussd-menu-builder');
let menu = new UssdMenu();

async function startSession(session_id, key) {
    menu.session.start(session_id,key)
}

async function setSession(session_id, key, value) {
    menu.session.set(value)
}

async function getSession(session_id, key) {
    return menu.session.get(key)
}

async function endSession(session_id) {
    return menu.session.end(session_id)
}

let sessions = {};
menu.sessionConfig({
    start: function (sessionId, key) {
        return new Promise((resolve, reject) => {
            if (!(sessionId in sessions)) sessions[sessionId] = {};
            resolve();
        });
    },
    set: (sessionId, key, value, callback) => {
        // store key-value pair in current session
        return new Promise((resolve, reject) => {
            sessions[sessionId][key] = value;
            callback();
        });
    },
    get: function (sessionId, key) {
        return new Promise((resolve, reject) => {
            let value = sessions[sessionId][key];
            resolve(value);
        });
    },
    end: function (sessionId, callback) {
        // clear current session
        // this is called by menu.end()
        return new Promise((resolve, reject) => {
            delete sessions[sessionId];
            callback();
        });
    }
});

module.exports = {
    startSession:startSession,
    setSession:setSession,
    getSession:getSession,
    endSession:endSession
}