exports.getMenuContent = function () {
    return {
        "0": {
            "start": {
                "title": "Kindly Select your Country of Choice (respond with the corresponding number of your choice)\n1 Kenya\n2 Uganda\n3 Tanzania\n4 Rwanda\n5 South Sudan",
                "next": {
                    "1": "kenya",
                    "2": "uganda",
                    "3": "tanzania",
                    "4": "rwanda",
                    "5": "south_sudan"
                },
                "input_type": "SELECTION"
            }
        },
        "1": {
            "kenya": {
                "title": "How can we be of service today? (respond with the corresponding number of your choice)\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n5 Invest With Us\n6 Bank With Us\n7 New Customer Verification\n8 Wellness & COVID-19 Updates\n0 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    "5": "invest_with_us",
                    /*"6": "health_worker",*/
                    "6": "bank_with_us",
                    "7": "new_customer_verification",
                    /*"9": "update_personal_details",*/
                    "8": "covid",
                    "0": "start",
                },
                "input_type": "SELECTION"
            },
            "uganda": {
                "title": "How can we be of service today? (respond with the corresponding number of your choice)\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n5 Invest With Us\n6 New Customer Verification\n7 Wellness & COVID-19 Updates\n0 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    "5": "invest_with_us",
                    "6": "new_customer_verification",
                    /*"7": "update_personal_details",*/
                    "7": "covid",
                    "11": "start",
                },
                "input_type": "SELECTION"
            },
            "tanzania": {
                "title": "How can we be of service today? (respond with the corresponding number of your choice)\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n5 New Customer Verification\n6 Wellness & COVID-19 Updates\n0 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    "5": "new_customer_verification",
                    /*"6": "update_personal_details",*/
                    "6": "covid",
                    "11": "start",
                },
                "input_type": "SELECTION"
            },
            "rwanda": {
                "title": "How can we be of service today? (respond with the corresponding number of your choice)\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n5 New Customer Verification\n6 Wellness & COVID-19 Updates\n0 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    "5": "new_customer_verification",
                    /*"6": "update_personal_details",*/
                    "6": "covid",
                    "11": "start",
                },
                "input_type": "SELECTION"
            },
            "south_sudan": {
                "title": "How can we be of service today? (respond with the corresponding number of your choice)\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n5 New Customer Verification\n6 Wellness & COVID-19 Updates\n0 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    "5": "new_customer_verification",
                    /*"6": "update_personal_details",*/
                    "6": "covid",
                    "11": "start",
                },
                "input_type": "SELECTION"
            }
        },
        "2": {
            "register_claim": {
                "title": "Which insurance would you like to register claim for? (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "claim_health",
                    "2": "claim_car",
                    "3": "claim_life",
                    "4": "claim_other",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status": {
                "title": "Policy status for? (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "policy_status_health",
                    "2": "policy_status_car",
                    "3": "policy_status_life",
                    "4": "policy_status_other",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "buy_insurance": {
                "title": "Buy For? (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "buy_insurance_health_success_end_message",
                    "2": "buy_insurance_car_success_end_message",
                    "3": "buy_insurance_life_success_end_message",
                    "4": "buy_insurance_other_success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment": {
                "title": "Make payment to? (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "make_insurance_payment_health",
                    "2": "make_insurance_payment_car",
                    "3": "make_insurance_payment_life",
                    "4": "make_insurance_payment_other",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "invest_with_us": {
                "title": "Are you an existing customer? \n1. Yes \n2. No",
                "next": {
                    "1": "invest_confirm_phone_no",
                    "2": "invest_new_user",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },

            "new_customer_verification": {
                "title": "Which Insurance class have you bought? (respond with the corresponding number of your choice)\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "verify_enter_name",
                    "2": "verify_enter_name",
                    "3": "verify_enter_name",
                    "4": "verify_enter_name",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "update_personal_details": {
                "title": "To update your personal details kindly click on this link https://bit.ly/2LazEcA#",
                "next": {
                    "1": "end"
                },
                "input_type": "END"
            },
            "bank_with_us": {
                "title": "Which Faulu Microfinance Bank product are you interested in? (respond with the corresponding number of your choice)\n1. Loan\n2. Current Account\n3. Savings Account\n4. Fixed Deposit Account\n0 Main Menu",
                "next": {
                    "1": "bank_with_us_option_success_end_message",
                    "2": "bank_with_us_option_success_end_message",
                    "3": "bank_with_us_option_success_end_message",
                    "4": "bank_with_us_option_success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "health_worker": {
                "title": "What would you like to do? (respond with the corresponding number of your choice)\n1. New User Registration\n2. Register a Claim \n0 Main Menu",
                "next": {
                    "1": "health_worker_sector",
                    "2": "health_worker_register_claim_sector",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "covid": {
                "title": "Which option would you prefer an update on?(respond with the corresponding number of your choice)\n1. Keeping Healthy\n2. Feeling Unwell\n3. Working From Home\n4. Studying From Home\n5. Unwinding From Home\n6. Ministry of Health Updates\n0 Main Menu",
                "next": {
                    "1": "covid_update_message",
                    "2": "covid_update_message",
                    "3": "covid_update_message",
                    "4": "covid_update_message",
                    "5": "covid_update_message",
                    "6": "covid_update_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            }
        },
        "3": {
            "invest_confirm_phone_no": {
                "title": "Is the phone number %s1 the one you use to transact i-INVEST phone number?\n1. Yes\n2. No\n3. Not sure",
                "next": {
                    "1": "invest_verify_otp",
                    "2": "invest_enter_another_phone_number",
                    "3": "invest_enter_another_phone_number",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },

            "invest_existing_user":
                {
                    "title":
                        "What would you like to do today? \n1. Top up Investment\n2. Check Balance\n3. View Statement\n4. Withdraw Funds\n5. Change PIN \n6. Contact Us \n7. Information on Fund\n0. Main Menu",
                    "next":
                        {
                            "1":
                                "make_an_investment",
                            "2":
                                "check_balance",
                            "3":
                                "view_statement",
                            "4":
                                "withdraw_funds",
                            "5":
                                "change_pin",
                            "6":
                                "contact_us",
                            "7":
                                "information_on_funds",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "invest_new_user":
                {
                    "title":
                        "Please select your preferred input \n1. Register \n2. Contact us \n3. Information on funds",
                    "next":
                        {
                            "1":
                                "register_new_invest_user",
                            "2":
                                "register_new_invest_user",
                            "3":
                                "register_new_invest_user"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "claim_health":
                {
                    "title":
                        "Select how to start the Health Insurance claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                    "next":
                        {
                            "1":
                                "provide_details_health",
                            "2":
                                "request_callback",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "claim_car":
                {
                    "title":
                        "Select how to start the Car Insurance claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                    "next":
                        {
                            "1":
                                "provide_details_car",
                            "2":
                                "request_callback",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "claim_life":
                {
                    "title":
                        "Select how to start the Life Insurance claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                    "next":
                        {
                            "1":
                                "provide_details_life",
                            "2":
                                "request_callback",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "claim_other":
                {
                    "title":
                        "Select how to start the Insurance claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                    "next":
                        {
                            "1":
                                "provide_details_other",
                            "2":
                                "request_callback",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "policy_status_health":
                {
                    "title":
                        "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                    "next":
                        {
                            "1":
                                "provide_details_policy_status_health",
                            "2":
                                "request_callback",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "policy_status_car":
                {
                    "title":
                        "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                    "next":
                        {
                            "1":
                                "provide_details_policy_status_car",
                            "2":
                                "request_callback",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "policy_status_life":
                {
                    "title":
                        "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                    "next":
                        {
                            "1":
                                "provide_details_policy_status_life",
                            "2":
                                "request_callback",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "policy_status_other":
                {
                    "title":
                        "Select how to start your policy status inquiry (respond with the corresponding number of your choice)\n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                    "next":
                        {
                            "1":
                                "provide_details_policy_status_other",
                            "2":
                                "request_callback",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "buy_insurance_health_success_end_message":
                {
                    "title":
                        "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                    "next":
                        {
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "END"
                }
            ,
            "buy_insurance_car_success_end_message":
                {
                    "title":
                        "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                    "next":
                        {
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "END"
                }
            ,
            "buy_insurance_life_success_end_message":
                {
                    "title":
                        "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                    "next":
                        {
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "END"
                }
            ,
            "buy_insurance_other_success_end_message":
                {
                    "title":
                        "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                    "next":
                        {
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "END"
                }
            ,
            "make_insurance_payment_health":
                {
                    "title":
                        "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                    "next":
                        {
                            "1":
                                "provide_details_payment_health",
                            "2":
                                "request_callback",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "make_insurance_payment_car":
                {
                    "title":
                        "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                    "next":
                        {
                            "1":
                                "provide_details_payment_car",
                            "2":
                                "request_callback",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "make_insurance_payment_life":
                {
                    "title":
                        "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                    "next":
                        {
                            "1":
                                "provide_details_payment_life",
                            "2":
                                "request_callback",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "make_insurance_payment_other":
                {
                    "title":
                        "Select How to Start the Insurance Payment (respond with the corresponding number of your choice)\n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                    "next":
                        {
                            "1":
                                "provide_details_payment_other",
                            "2":
                                "request_callback",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "verify_enter_name":
                {
                    "title":
                        "Kindly enter your full name?\n0 Main Menu",
                    "next":
                        {
                            "1":
                                "verify_id_passport",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "TEXT"
                }
            ,
            "bank_with_us_option_success_end_message":
                {
                    "title":
                        "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                    "next":
                        {
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "END"
                }
            ,
            "covid_update_message":
                {
                    "title":
                        "Dear customer, please check health tips here <>  \n0 Main Menu",
                    "next":
                        {
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "END"
                }
            ,
            "health_worker_sector":
                {
                    "title":
                        "Please select your primary work sector (respond with the corresponding number of your choice)\nTo view our terms and conditions, click here: https://bit.ly/3crRpzv \n1. Public Sector\n2. Private Sector\n0 Main Menu",
                    "next":
                        {
                            "1":
                                "health_worker_profession_public",
                            "2":
                                "health_worker_profession_private",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }
            ,
            "health_worker_register_claim_sector":
                {
                    "title":
                        "Please select your work sector (respond with the corresponding number of your choice) \n1. Public Sector\n2. Private Sector\n0 Main Menu",
                    "next":
                        {
                            "1":
                                "health_worker_register_claim_type_public",
                            "2":
                                "health_worker_register_claim_type_private",
                            "0":
                                "back_to_main"
                        }
                    ,
                    "input_type":
                        "SELECTION"
                }

        }
        ,
        "4":
            {
                "invest_verify_otp": {
                    "title": "Please enter the OTP code we sent you via SMS to %s1",
                    "next": {
                        "1": "invest_existing_user",
                        "2": "invest_resend_otp",
                        "0": "back_to_main"
                    },
                    "input_type": "TEXT"
                },
                "invest_enter_another_phone_number": {
                    "title": "Please enter another phone number registered under your name you wish to transact i-INVEST?\n0 Main Menu",
                    "next": {
                        "1": "invest_verify_otp",
                        "0": "back_to_main"
                    },
                    "input_type": "TEXT"
                },
                "withdraw_funds":
                    {
                        "title":
                            "Select the fund you would like to withdraw from: \n1. Equity Fund\n.2 Bond Fund\n3. Balanced Fund\n4. Money Market Fund",
                        "next":
                            {
                                "1":
                                    "enter_fund_number",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
                ,
                "change_pin":
                    {
                        "title":
                            "Kindly enter your current PIN",
                        "next":
                            {
                                "1":
                                    "enter_current_pin",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "contact_us":
                    {
                        "title":
                            "Select what you would like to do:\n1. Contact Us Details\n2. Request a Callback",
                        "next":
                            {
                                "1":
                                    "contact_us_address",
                                "2":
                                    "contacts_request_callback"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
                ,
                "information_on_funds":
                    {
                        "title":
                            "Select the fund you would like to get more information on: \n1. Equity Fund\n2. Bond Fund\n3. Balanced Fund\n4. Money Market Fund",
                        "next":
                            {
                                "1":
                                    "information_equity_fund",
                                "2":
                                    "information_bond_fund",
                                "3":
                                    "information_balanced_fund",
                                "4":
                                    "information_moneymarket_fund"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
                ,
                "make_an_investment":
                    {
                        "title":
                            "Please enter the amount you would like to invest (Initial minimum KES 10 or KES 10 on top-up)",
                        "next":
                            {
                                "1":
                                    "mpesa_stk_push_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "register_new_invest_user":
                    {
                        "title":
                            "Please select your identification document? \n1. National ID number \n2. Valid passport",
                        "next":
                            {
                                "1":
                                    "invest_enter_national_id",
                                "2":
                                    "invest_enter_passport",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
                ,
                "request_callback":
                    {
                        "title":
                            "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"
                    }
                ,
                "provide_details_policy_status_health":
                    {
                        "title":
                            "What\'s Your Health Insurance Policy Number? eg. UK032344-01 or National ID Number\n \n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_policy_status_car":
                    {
                        "title":
                            "What\'s Your Car Registration Number? e.g KAA123A\n \n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_policy_status_other":
                    {
                        "title":
                            "What\'s Your Insurance Policy Number?\n \n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_policy_status_life":
                    {
                        "title":
                            "What\'s your Life Insurance policy number? e.g. OMK001429206 or National ID Number\n \n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_payment_health":
                    {
                        "title":
                            "What\'s Your Health Insurance Policy Number? eg. UK032344-01 or National ID Number\n \n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_payment_car":
                    {
                        "title":
                            "What\'s Your Car Registration Number? e.g KAA123A\n \n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_payment_other":
                    {
                        "title":
                            "What\'s Your Insurance Policy Number?\n \n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_payment_life":
                    {
                        "title":
                            "What\'s Your Life Insurance Policy Number? e.g. OMK001429206 or National ID Number\n \n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_car":
                    {
                        "title":
                            "What\'s Your Car Registration Number? e.g KAA123A\n \n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_car_photos",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,

                "provide_details_other":
                    {
                        "title":
                            "What\'s Your Insurance policy number?\n \n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_other_photos",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_life":
                    {
                        "title":
                            "What\'s your Life Insurance policy number? e.g. OMK001429206 or National ID Number\n \n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_life_photos",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_health":
                    {
                        "title":
                            "What\'s your Health Insurance policy number? e.g. UK032344-01 or National ID Number\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_claim_mpesa_no",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "verify_id_passport":
                    {
                        "title":
                            "What\'s your National ID e.g. 1234567 or Passport Number e.g. A1234567\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "verify_id_photo",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "health_worker_profession_public":
                    {
                        "title":
                            "Please enter your profession type e.g. Doctor, Nurse, Ambulance Driver\n(You will be covered for positive diagnosis and Funeral Cover)\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "health_worker_reg_no_public",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "health_worker_profession_private":
                    {
                        "title":
                            "Kindly select your profession (respond with the corresponding number of your choice)\nYou will ONLY be covered for HOSPITALIZATION on positive covid-19 diagnosis and Funeral Cover.\n1. Doctor\n2. Nurse\n3. Clinical Officer\n4. Laboratory Technician\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "health_worker_reg_no_private",
                                "2":
                                    "health_worker_reg_no_private",
                                "3":
                                    "health_worker_reg_no_private",
                                "4":
                                    "health_worker_reg_no_private",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
                ,
                "health_worker_register_claim_type_public":
                    {
                        "title":
                            "Kindly select the type of claim (respond with the corresponding number of your choice). Make sure to have test results from the laboratory facilities accredited by the Ministry of Healthy, which can be found here: https://bit.ly/3crRpzv \n1. Cash benefit for Positive COVID-19 Diagnosis\n2. Funeral Cover Benefit\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "health_worker_register_claim_hosp_public",
                                "2":
                                    "health_worker_register_claim_last",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
                ,
                "health_worker_register_claim_type_private":
                    {
                        "title":
                            "Kindly select the type of claim (respond with the corresponding number of your choice). Make sure to have test results from the laboratory facilities accredited by the Ministry of Healthy, which can be found here: https://bit.ly/3crRpzv \n1. Hospital Cash Benefit for POSITIVE COVID-19 Diagnosis\n2. Funeral Cover Benefit\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "health_worker_register_claim_hosp_private",
                                "2":
                                    "health_worker_register_claim_last",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }

            }
        ,
        "5":
            {
                "invest_verify_otp": {
                    "title": "Please enter the OTP sent to %s1",
                    "next": {
                        "1": "invest_existing_user",
                        "2": "invest_resend_otp",
                        "0": "back_to_main"
                    },
                    "input_type": "TEXT"
                },
                "enter_fund_number":
                    {
                        "title":
                            "Please enter your Client Account Fund Number",
                        "next":
                            {
                                "1":
                                    "enter_amount_withdraw",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"

                    }
                ,
                "enter_current_pin":
                    {
                        "title":
                            "Kindly enter your new PIN",
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"

                    }
                ,
                "contact_us_address":
                    {
                        "title":
                            "UAP Old Mutual Kenya \n Physical Address:Old Mutual main \n Branch: Kimathi Street \n Postal Address: P.O Box 30059-0100 \n Tel 0711010000 \n Website www.oldmutual.co.ke/i-INVEST",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"

                    }
                ,
                "contacts_request_callback":
                    {
                        "title":
                            "Kindy enter your mobile number that you would like use to get intouch with you.",
                        "next":
                            {
                                "1":
                                    "success_end_message"
                            }
                        ,
                        "input_type":
                            "TEXT"

                    }
                ,
                "contact_us_details":
                    {
                        "title":
                        "Thank you, please follow the mobile money steps that will pop up on your screen and once " +
                        "successful, you will receive an sms with details of your transaction\n\n0 Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"

                    }
                ,
                "mpesa_stk_push_message":
                    {
                        "title":
                        "Thank you, please follow the mobile money steps that will pop up on your screen and once " +
                        "successful, you will receive an sms with details of your transaction\n\n0 Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"

                    }
                ,
                "invest_enter_national_id":
                    {
                        "title":
                            "Please enter your National ID number (e.g 01011980)",
                        "next":
                            {
                                "0":"back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "invest_enter_passport":
                    {
                        "title":
                            "Please enter your passport number (e.g. JXXXXXXX)",
                        "next":
                            {
                                "1":"invest_enter_kra_pin"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_health_claim_mpesa_no":
                    {
                        "title":
                            "Kindly type your  Mpesa registered mobile number e.g. 07********\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_claim_kra_pin",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_car_photos":
                    {
                        "title":
                            "Share photos of your car and any other photos of supporting documents listed below\n- Completed Claim Form \n- Copy of the log book \n- Police Abstract Report \n- Motor Vehicle Inspection report (for third party claims)\n- Copy of the Driver’s Driving licence\n- Notice of intention to prosecute(if any)\n- Applicable excess\n\nType 1 to complete your submission\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_photos_end",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "provide_details_life_photos":
                    {
                        "title":
                            "Share a Photo of Your Insurance Supporting Documents e.g. Death Certificate.\n\nType 1 to complete your submission\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_photos_end",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,

                "provide_details_other_photos":
                    {
                        "title":
                            "Share photo of your Insurance supporting Documents? e.g. Medical receipts, Death certificate, Police abstract, Damaged property\n\nType 1 to complete your submission\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_photos_end",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,

                "provide_details":
                    {
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
                ,
                "verify_id_photo":
                    {
                        "title":
                            "Kindly Take & Upload a Photo of Your National ID or Passport \n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "selfie_passport_photo",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "success_end_message":
                    {
                        "title":
                            "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"
                    }
                ,
                "health_worker_reg_no_public":
                    {
                        "title":
                            "Please enter you public Personal Number e.g. **********\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "health_worker_name",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "health_worker_reg_no_private":
                    {
                        "title":
                            "Kindly enter your professional registration / licence number\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "health_worker_name",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_health_worker_id":
                    {
                        "title":
                            "Kindly enter your National ID Number? e.g. 12345678\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_hosp_claim_staff_card",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,

                "health_worker_register_claim_hosp_public":
                    {
                        "title":
                            "Select how to start your Cash Benefit claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_hosp_claim_id_public",
                                "2":
                                    "request_callback",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
                ,
                "health_worker_register_claim_hosp_private":
                    {
                        "title":
                            "Select how to start your Hospital Cash Benefit claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_hosp_claim_id_private",
                                "2":
                                    "request_callback",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
                ,
                "health_worker_register_claim_last":
                    {
                        "title":
                            "Select how to start the Funeral Cover Benefit claim (respond with the corresponding number of your choice)\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_last_claim_id",
                                "2":
                                    "request_callback",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
                ,
                "request_callback":
                    {
                        "title":
                            "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"
                    }
                ,
            }
        ,
        "6":
            {
                "enter_amount_withdraw":
                    {
                        "title":
                            "Please enter the amount you would like to withdraw(min KES480)",
                        "next":
                            {
                                "1":
                                    "select_withdraw_type",
                                "0":
                                    "back_to_main"

                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
                ,
                "provide_details_health_claim_kra_pin":
                    {
                        "title":
                            "Kindly enter your KRA PIN\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_claim_bank_name",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_car_additional_photos":
                    {
                        "title":
                            "Share a photo of police abstract or any other supportive photos\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "provide_details_other_additional_photos":
                    {
                        "title":
                            "Share a photo of additional supportive documents\n 1. Complete Submission \n0. Main Menu",
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "provide_details_photos_end":
                    {
                        "title":
                            "Thank you for the details provided, one of our Call Center representatives will contact you within 24 Hours.\n0 Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"
                    }
                ,
                "provide_details_life_additional_photos":
                    {
                        "title":
                            "Share a photo of additional supportive documents\n 1. Complete Submission \n0. Main Menu",
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "selfie_passport_photo":
                    {
                        "title":
                            "Kindly take & upload a Selfie or Passport Photo\n0. Main Menu",
                        "next":
                            {
                                "1":
                                    "signup_phone_no",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "success_end_message":
                    {
                        "title":
                            "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"
                    }
                ,
                "health_worker_name":
                    {
                        "title":
                            "Kindly enter your  first name and surname\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "health_worker_national_id",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_health_worker_supporting_docs":
                    {
                        "title":
                            "Kindly share photos of your insurance claim supporting documents e.g. Medical Receipts, Hospital Admission Form or Death Certificate\n\nMaximum 10 attachments.\n\nType 1 when done.\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_end",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "provide_details_health_worker_hosp_claim_id_public":
                    {
                        "title":
                            "Kindly share photo of your National ID Card\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_hosp_claim_staff_card_public",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "provide_details_health_worker_hosp_claim_id_private":
                    {
                        "title":
                            "Kindly share photo of your National ID Card\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_hosp_claim_staff_card_private",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "provide_details_health_worker_last_claim_id":
                    {
                        "title":
                            "Kindly share photo of your National ID Card\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_last_claim_id_hw",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "source_of_funds":
                    {
                        "title":
                            "Please enter your source of funds\n1. Salary\n2. Self Employed\n3. Own Savings\n4. Salary\n5. Other",
                        "next":
                            {
                                "1":
                                    "invest_enter_kra_pin",
                                "2":
                                    "invest_enter_kra_pin",
                                "3":
                                    "invest_enter_kra_pin",
                                "4":
                                    "invest_enter_kra_pin",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
            }
        ,
        "7":
            {
                "select_withdraw_type":
                    {
                        "title":
                            "Should Old Mutual send your withdraw fund to\1. M-PESA\n0. Back to main",
                        "next":
                            {
                                "1":
                                    "withdraw_funds_via_mpesa",
                                "0":
                                    "back_to_main"

                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
                ,
                "enter_mobile_number":
                    {
                        "title":
                            "What is your mobile number you will be using to make your investments? (e.g +************)",
                        "next":
                            {
                                "1":
                                    "alternative_number"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_health_claim_bank_name":
                    {
                        "title":
                            "Kindly enter your Bank name e.g. Faulu\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_claim_bank_branch",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "success_end_message":
                    {
                        "title":
                            "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"
                    }
                ,
                "signup_phone_no":
                    {
                        "title":
                            "Kindly enter the phone number you used to sign-up for the new product? e.g. 0721******\n0. Main Menu",
                        "next":
                            {
                                "1":
                                    "otp_verification",
                                "2":
                                    "end"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "health_worker_national_id":
                    {
                        "title":
                            "Kindly enter your National ID Number e.g. 12345678\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "health_worker_facility",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_health_worker_end":
                    {
                        "title":
                            "We have successfully received your claim notification.\n One of our call center representatives will contact you within 24 Hours.\n\n0. Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"
                    }
                ,
                "provide_details_health_worker_hosp_claim_staff_card_public":
                    {
                        "title":
                            "Kindly share a photo of staff card of employing health facility\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_hosp_claim_covid_report_public",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "provide_details_health_worker_hosp_claim_staff_card_private":
                    {
                        "title":
                            "Kindly share a photo of staff card of employing health facility\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_hosp_claim_covid_report_private",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "provide_details_health_worker_last_claim_id_hw":
                    {
                        "title":
                            "Kindly share a photo of the Health Worker\'s National ID Card\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_last_facility",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "invest_enter_kra_pin":
                    {
                        "title":
                            "Kindly enter KRA PIN(e.g A123456789B)",
                        "next":
                            {
                                "1":
                                    "invest_enter_email",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
            }
        ,
        "8":
            {
                "withdraw_funds_via_mpesa":
                    {
                        "title":
                            "Enter your Mobile Banking Number",
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "alternative_number":
                    {
                        "title":
                            "Please enter alternative number for communication (e.g. +************) \nType 'S' to skip",
                        "next":
                            {
                                "1":
                                    "enter_email_address"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_health_claim_bank_branch":
                    {
                        "title":
                            "Kindly enter your Bank Branch name e.g. Ngong Road Branch\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_claim_bank_account_name",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "end":
                    {
                        "title":
                            "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                        "input_type":
                            "END"
                    }
                ,
                "back_to_main":
                    {
                        "title":
                            "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                        "input_type":
                            "END"
                    }
                ,
                "otp_verification":
                    {
                        "title":
                            "Kindly Insert the 4 Digit Code Sent To You Via SMS\n0 Main Menu\n00. Resend OTP",
                        "next":
                            {
                                "1":
                                    "customer_verification_success_end_message",
                                "0":
                                    "back_to_main",
                                "00":
                                    "otp_verification",
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "health_worker_facility":
                    {
                        "title":
                            "Kindly enter your primary health facility of operation?\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "health_worker_national_id_photo",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_health_worker_hosp_claim_covid_report_public":
                    {
                        "title":
                            "Kindly share a photo of your positive COVID-19 diagnosis report\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "provide_details_health_worker_hosp_claim_covid_report_private":
                    {
                        "title":
                            "Kindly share a photo of your positive COVID-19 diagnosis report\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_hospital_discharge_report_private",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "provide_details_health_worker_last_facility":
                    {
                        "title":
                            "Kindly share a photo staff ID card of the facility where the Healthcare Worker worked.\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_last_death_letter",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "payment_method":
                    {
                        "title":
                        "Fund type: Money Market Fund\n Investment amount KES 1000n\n" +
                        "Units invested 6756\n\n" +
                        "Would you like to proceed to make a payment?" +
                        "\n1. MPESA \n2. Cancel \n\nBy proceeding you have accepted the terms and conditions\n" +
                        "https://uapoldmutual.com",
                        "next":
                            {
                                "1":
                                    "mpesa_selected",
                                "2":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
            }
        ,
        "9":
            {
                "success_end_message":
                    {
                        "title":
                            "Thank you, Please follow the mobile money steps that will pop-up on your screen and once successful you will receive an SMS with details of you transaction.",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"
                    }
                ,
                "invest_enter_email":
                    {
                        "title":
                            "Please enter your email address (e.g <EMAIL>)",
                        "next":
                            {
                                "1":
                                    "invest_enter_postal_address",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT "
                    }
                ,
                "enter_email_address":
                    {
                        "title":
                            "Please enter your email address (e.g <EMAIL>)",
                        "next":
                            {
                                "1":
                                    "enter_postal_address",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_health_claim_bank_account_name":
                    {
                        "title":
                            "Kindly enter your Bank Account name\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_claim_bank_account_number",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "customer_verification_success_end_message":
                    {
                        "title":
                            "We have successfully received your details. Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                        "input_type":
                            "END"
                    }
                ,
                "end":
                    {
                        "title":
                            "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                        "input_type":
                            "END"
                    }
                ,
                "health_worker_national_id_photo":
                    {
                        "title":
                            "Kindly share a clear photo of your National ID\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "health_worker_staff_id_photo",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "provide_details_health_worker_success_end_message":
                    {
                        "title":
                            "Kindly respond with: \n1. To complete Claim Submission\n\n0. Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"
                    }
                ,
                "provide_details_health_worker_hospital_discharge_report_private":
                    {
                        "title":
                            "Kindly share a photo of your hospitalization discharge summary indicating dates of admission and discharge.\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "provide_details_health_worker_last_death_letter":
                    {
                        "title":
                            "Kindly share a photo of Police or Hospital Death Notification Letter indicating details of death\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_worker_success_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "mpesa_selected":
                    {
                        "title":
                            "Thank you",
                        "input_type":
                            "END"
                    }
            }
        ,
        "10":
            {
                "invest_enter_postal_address":
                    {
                        "title":
                        "Almost done! Please enter your physical address in the following format (Please enter a comma after each address line as shown below)\n" +
                        "Address line 1 (House No.)\n" +
                        "Address line 2 (Apartment/Plot No/Estate name)\n" +
                        "Address line 3 (Road Name)\n" +
                        "Address line 4 (Location/Sub county)\n" +
                        "Address line 5 (County)\n",
                        "next":
                            {
                                "1":
                                    "invest_choose_occupation",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_health_claim_bank_account_number":
                    {
                        "title":
                            "Kindly enter your Bank Account number\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_health_photos",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
                "provide_details_health_worker_success_end_message":
                    {
                        "title":
                            "Kindly respond with: \n1. To complete Claim Submission\n\n0. Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"
                    }
                ,
                "end":
                    {
                        "title":
                            "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                        "input_type":
                            "END"
                    }
                ,
                "health_worker_staff_id_photo":
                    {
                        "title":
                            "Kindly share a clear photo of your Staff ID\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "health_worker_beneficiary_name",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
            }
        ,
        "11":
            {
                "invest_choose_occupation":
                    {
                        "title":
                        "Please choose your occupation from option below" +
                        "\n1. Employed" +
                        "\n2. Self Employed" +
                        "\n3. Student" +
                        "\n4. Other",
                        "next":
                            {
                                "1":
                                    "invest_terms_and_condition",
                                "2":
                                    "invest_terms_and_condition",
                                "3":
                                    "invest_terms_and_condition",
                                "4":
                                    "invest_terms_and_condition",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
                ,
                "provide_details_health_photos":
                    {
                        "title":
                            "Kindly share photos of your insurance claim supporting documents listed below.\n- Duly filled outpatient/reimbursement claim form \n- Copy of drug prescription form\n- Original receipts\n- Copy of lab request form\n\nType 1 when done uploading.\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "provide_details_photos_end",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "IMAGE"
                    }
                ,
                "health_worker_beneficiary_name":
                    {
                        "title":
                            "Kindly enter your beneficiary\'s name (first name & surname)\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "health_worker_beneficiary_phone_no",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
            }
        ,
        "12":
            {
                "invest_terms_and_condition":
                    {
                        "title":
                        "By proceeding you are agreeing to our i-invest terms and conditions at https://uapom-uapoldmutual.com/images/i-INVEST-Ts-&-Cs.pdf" +
                        "\n1. Accept" +
                        "\n2. Reject",
                        "next":
                            {
                                "1":
                                    "invest_accept_tnc",
                                "2":
                                    "invest_reject_tnc",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "SELECTION"
                    }
                ,
                "provide_details_photos_end":
                    {
                        "title":
                            "Thank you for the details provided, one of our Call Center representatives will contact you shortly.\n0 Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"
                    }
                ,
                "health_worker_beneficiary_phone_no":
                    {
                        "title":
                            "Kindly enter your beneficiary\'s phone number\n\n0 Main Menu",
                        "next":
                            {
                                "1":
                                    "health_worker_registration_end_message",
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "TEXT"
                    }
                ,
            }
        ,
        "13":
            {
                "health_worker_registration_end_message":
                    {
                        "title":
                            "We have successfully received your details. We will make claims ONLY against test results from the laboratory facilities accredited by the Ministry of Health. You can view the laboratory facilities accredited by the Ministry of Health and our terms and conditions here: https://bit.ly/3crRpzv and to view our online portal, click here: https://bit.ly/2V2jEze \n\n0. Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"
                    }
                ,
                "invest_accept_tnc":
                    {
                        "title":
                        "Thanks. Let's setup your username and password\n\n" +
                        "Please follow this link to capture your information https://bitly.setpin/MyOMRegistration",
                        "input_type":
                            "END"
                    },
                "invest_reject_tnc":
                    {
                        "title":
                        "You have to accept the terms and conditons for you to invest with Old Mutual",
                        "input_type":
                            "END"
                    }
            }
        ,

        "generic":
            {
                "user_invest_accounts":
                    { "title":
                            "Please select the account you want to view the statement \n%s1\n\n0 Main Menu",

                        "next":
                            { "1":
                                    "mmf",
                                "2":
                                    "balanced_fund",
                                "3":
                                    "equity",
                                "4":
                                    "bond_fund",

                                "0":
                                    "back_to_main"
                            }
                    },
                    "user_statement":
                    { "title":
                            "%s1\n\n0 Main Menu",

                        "next":
                            { 
                            
                            }
                    },    
                "response_options":
                    {
                        "next":
                            {
                                "1":
                                    "provide_details",
                                "2":
                                    "request_callback",
                                "0":
                                    "back_to_main"
                            }
                    }
                ,
                "provide_details":
                    {
                        "next":
                            {
                                "1":
                                    "success_end_message",
                                "0":
                                    "back_to_main"
                            }
                    }
                ,
                "success_end_message":
                    {
                        "title":
                            "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main"
                            }
                        ,
                        "input_type":
                            "END"
                    }
                ,
                "exit":
                    {
                        "title":
                            "Thank You For the Details Provided, One of Our Call Center Agents Will Call You Within 24 Hours."
                    }
                ,
                "covid_end_message":
                    {
                        "title":
                            "A Link Has Sent To You Via SMS on"
                    }
                ,
                "covid_update_message":
                    {
                        "title":
                            "A Link Has Been Sent To You Via SMS on  \n0 Main Menu",
                        "next":
                            {
                                "0":
                                    "back_to_main",
                                "11":
                                    "exit"
                            }
                    }
                ,
                "covid_menu":
                    {
                        "title":
                            "A Link Has Sent To You Via SMS on",
                        "1":
                            "How to Keep Healthy\n0 Main Menu",
                        "2":
                            "What To Do If You Feel Unwell\n0 Main Menu",
                        "3":
                            "How to Work From Home\n0 Main Menu",
                        "4":
                            "How to Study From Home\n0 Main Menu",
                        "5":
                            "How to Unwind From Home\n0 Main Menu",
                        "6":
                            "Ministry of Health Updates\n0 Main Menu",
                        "0":
                            "Back to Menu"
                    }
                ,
                "insurance_class":
                    [
                        "Health Insurance",
                        "Car Insurance",
                        "Life Insurance",
                        "Other Insurance Insurance"
                    ],
                "end":
                    {
                        "title":
                            "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                        "input_type":
                            "END"
                    }
            }
    }
}

