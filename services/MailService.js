"use strict";
const nodemailer = require("nodemailer");
const request = require('request');

// async..await is not allowed in global scope, must use a wrapper
async function main() {
    // Generate test SMTP service account from ethereal.email
    // Only needed if you don't have a real mail account for testing
    let testAccount = await nodemailer.createTestAccount();

    // create reusable transporter object using the default SMTP transport
    let transporter = nodemailer.createTransport({
        host: "*************",
        port: 25,
        secure: false, // true for 465, false for other ports

    });

    // send mail with defined transport object
    let info = await transporter.sendMail({
        from: '"UAP Old Mutual" <<EMAIL>>', // sender address
        // to: "<EMAIL>, <EMAIL>", // list of receivers
        to: "<EMAIL>", // list of receivers
        subject: "Hello ✔", // Subject line
        text: "Hello world?", // plain text body
        html: "<b>Hello world?</b>", // html bodys
    });

    console.log("Message sent: %s", info.messageId);
    // Message sent: <<EMAIL>>

    // Preview only available when sending through an Ethereal account
    console.log("Preview URL: %s", nodemailer.getTestMessageUrl(info));
    // Preview URL: https://ethereal.email/message/WaQKMgKddxQDoou...
}

async function sendMail(body, cc, country_code, callback) {
    const options = {
        'method': 'POST',
        'url': 'http://************:9385/mail_service',
        'headers': {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        form: {
            id: '',
            link: body,
            recepient: getReceipientEmailAddress(country_code),
            from: '<EMAIL>',
            subject: 'Facebook Messenger Sale & Service Request',
            cc: cc
        }
    };
    try {
        request(options, function(error, response) {
            if (error) {
                console.log("Error sending mail " + error);
                callback(true, error)
            } else {
                if (response.statusCode === 200) {
                    callback(false, response)
                } else {
                    console.log("Error sending mail error code: " + response.statusCode);
                    callback(true, error)
                }
            }

        });
    } catch (e) {
        console.log("Error sending mail caught error : " + e);
        callback(true, e)
    }

}

function getReceipientEmailAddress(country_code) {
    if (country_code === 'na')
        return '<EMAIL>'
    else if (country_code === 'sz')
        return '<EMAIL>'
    else if (country_code === 'bw')
        return '<EMAIL>'
    else if (country_code === 'mw')
        return '<EMAIL>'
    else
        return '<EMAIL>'

}
// main().catch(console.error);
module.exports = {
    main,
    sendMail: sendMail
}