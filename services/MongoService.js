'use strict';
var mongoose = require('mongoose');

async function connection(callback) {

    mongoose.connect('mongodb://127.0.0.1:27017/covid_19_apps', {useNewUrlParser: true});
    var db = mongoose.connection;
    db.on('error', console.error.bind(console, 'connection error:'));
    db.once('open', function() {
        /*var schema = new mongoose.Schema({ first_level: 'string', size: 'string' });
        var Athlete = mongoose.model('Athlete', yourSchema);*/
        console.log('Mongo connected!')
        callback(false,'Mongo connected!');
    });
}

module.exports={
    connection: connection
}