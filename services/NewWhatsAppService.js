'use strict';

var whatsAppMessageService = require('./WhatsAppService');
var whatsAppMenuService = require('./WhatsAppMenuService');
var whatsappDBService = require('./WhatsappDBService');
var whatsAppSessionService = require('./WhatsAppSessionService');
var selfservice = require('../services/SelfService-API');
var sessionService = require('../services/SessionService');
var validateService = require('../services/ValidateInputService');
var investUserService = require('../services/ke/whatsapp/InvestUserService');
const {
    isTravelUgOption,
    ProcessTravelUgRequest,
    GetUserSession,
    GetCurrentLevel,
    TravelOptions
} = require('../services/ug/TravelUgService');
var investService = require('./ke/InvestAPIService');
let utils = require('./Utils')
let source_channel = 2;
let enforceHWSingleRegistration = true
let tnc_link = 'https://bit.ly/2XfWN4o'

let salutation_trigger = [
    'hi', 'hello', 'helo', 'halo', 'hallo', 'hey', 'mambo', 'vipi', 'niaje', 'menu', 'help', 'ke hi'
];
let salutation_response = ['Hi', 'Hello', 'Hello', 'Hi', 'Hi', 'Hi', 'Hi', 'Hi', 'Hi', 'Hi', 'Hi', 'Hi'];
let res;
let level, shouldUpdateSession = true;
let menu_data = {}
let menu = '';

function handleUserText(req, _res, source_country = 'ke') {
    res = _res;
    let content = req.body.results[0];
    let unique_identifier = content.message.text
    let phone_no = content.from

    let sal_index = salutation_trigger.indexOf(unique_identifier.toLocaleLowerCase());

    if (sal_index > -1) {
        unique_identifier = 'start';
        whatsappDBService.getUserLevel(phone_no, function(err, user_response) {
            if (err) {
                initialWelcome(salutation_response[sal_index], content, source_country)
            } else {
                if (user_response.country != null) {
                    level = 1
                    unique_identifier = user_response.country
                } else if (user_response.country == null) {
                    level = 0
                    unique_identifier = 'start'
                    user_response.level = 0
                    updateCountry({ id: user_response.id, phone_no: phone_no, level: 0 })
                }

                menu = whatsAppMenuService.getMenu(level, unique_identifier, user_response)
                let name = ""
                if ("contact" in content && level == 1) {
                    content.contact.name = content.contact.name.replace(/([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
                    if (typeof content.contact.name == 'string') {
                        name = " " + content.contact.name
                        if (source_country === 'tz')
                            menu = 'Hi' + name + ', we\'re happy to have you here 🙂. Welcome to UAP. I will be your virtual assistant.' +
                            ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  https://bit.ly/2XfWN4o\n\n' + menu
                        else
                            menu = 'Hi' + name + ', we\'re happy to have you here 🙂. Welcome to UAP Old Mutual. I will be your virtual assistant.' +
                            ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n' + menu
                    }
                }
                if (level == '0' || level == '1') {
                    selfservice.create_wa_user(phone_no, name, user_response.country, function(err, resp) {
                        if (!err)
                            console.log('----> WhatsAPP user created ' + resp)
                    })

                    user_response.path = 'start'
                }
                whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                    if (err) {
                        console.log(content.from + ' | wa_response: ' + err)
                        res.json(err)

                    } else {
                        whatsappDBService.updateUserSession({
                            phone_no: content.from,
                            level: 1,
                            input: content.message.text,
                            unique_identifier: unique_identifier,
                            expected_input_type: 'SELECTION',
                            path: user_response.path + '*' + content.message.text
                        }, function(err, session_response) {
                            if (err) {
                                console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                    // res.json(err)
                            } else {
                                try {
                                    res.json(wa_response)
                                } catch (e) {

                                }
                            }
                        })
                    }
                })
                updateTargetProduct(phone_no, unique_identifier)

            }
        })
    } else {
        whatsappDBService.getUserLevel(phone_no, function(err, user_response) {
            if (err) {
                initialWelcome(salutation_response[0], content, source_country)
            } else {

                if (user_response.level == '1') {
                    unique_identifier = getCurrentUniqueIdentifier(content.message.text, user_response)
                    updateTargetProduct(phone_no, unique_identifier)
                    user_response.target_product = unique_identifier
                    user_response.unique_identifier = unique_identifier
                }

                if (user_response.target_product === 'invest_with_us') {
                    gotoInvestUserService(req, _res, source_country, user_response)
                } else {
                    if (user_response.expected_input_type === 'SELECTION') { // Menu selected
                        if (content.message.text == '0' && user_response.level == '1') {
                            level = 0
                            user_response.level = 0
                            user_response.country = null
                            unique_identifier = 'start'
                            initialWelcome('Hi', content, source_country)
                            return;
                        } else if (content.message.text == '0' || user_response.unique_identifier == 'back_to_main') {
                            level = 1
                            user_response.level = 1
                            unique_identifier = user_response.country
                            user_response.unique_identifier = user_response.country
                            user_response.path = 'start'
                        }
                        //update country
                        else if (user_response.country == null) {
                            user_response.level = 0
                            let country = getCurrentUniqueIdentifier(content.message.text, user_response)
                            user_response.country = country
                                // console.log('->1 country ' + country)
                            if (country === 'start') { // user entered an option not available.
                                // console.log('----> No country for option ' + content.message.text)
                                level = 0
                                unique_identifier = 'start'
                            } else {
                                level = 1
                                unique_identifier = country
                                user_response.country = country
                                user_response.country_id = content.message.text
                            }

                        } else {

                            level = user_response.level + 1
                            if(unique_identifier!='buy_insurance'){
                                unique_identifier = getCurrentUniqueIdentifier(content.message.text, user_response)
                            }
                            console.log(`unique_identifier ${unique_identifier} level ${level}`)
                            if (typeof unique_identifier == 'undefined') {
                                console.log('----> Unique undefined ' + content.message.text)
                                level = user_response.level
                                unique_identifier = user_response.unique_identifier
                            }
                            if (!shouldUpdateSession) {
                                level = user_response.level
                                if (level == '1') {
                                    console.log(`=========___ unique_identifier ${unique_identifier} level ${level}`)
                                    updateTargetProduct(phone_no, unique_identifier)
                                    user_response.target_product = unique_identifier
                                }
                            }
                        }
                        // Validation
                        let validation_error_msg = '';
                        if (unique_identifier === 'health_worker_sector' && enforceHWSingleRegistration) {
                            console.log('HW checking existence')
                            selfservice.CheckIfHWUserExists(phone_no, function(err, exists) {
                                if (!err) {
                                    if (exists) {
                                        validation_error_msg = 'There is an existing account registered to this phone number. \n'
                                        level = user_response.level
                                        unique_identifier = user_response.unique_identifier
                                            // console.log('--------------> Error creating HW ' + level + ' Error creating HW' + unique_identifier)
                                        menu_data = getMenu(level, unique_identifier, user_response)

                                        menu = validation_error_msg + '' + menu_data.menu

                                        console.log('--------------> 1')
                                        whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                            if (err) {
                                                console.error(content.from + ' | wa_response: ' + err)
                                                _res.json(false)
                                            } else
                                                _res.json(true)
                                        })
                                    } else {
                                        console.log('--------------> 2')
                                        validationPassed(source_country, level, unique_identifier, user_response, content)
                                    }
                                } else {
                                    console.error((utils.getDateTime() + ' | ' + content.from + ' Error checking user existence on ODS ' + err))
                                    whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, 'Error occurred processing your request. Please try again \n\n0. Main Menu', function(err, wa_response) {
                                        if (err) {
                                            console.error(content.from + ' | wa_response: ' + err)
                                            _res.json(false)
                                        } else
                                            _res.json(true)
                                    })
                                }
                            })

                        } 
                        //travel_insurance
                        if (unique_identifier === 'buy_insurance') {
                            console.log('buy_insurance menu')
                            const menures= getMenuOptionsAndTitle(level,unique_identifier,user_response)
                            let input_type="SELECTION"
                            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menures.menu, function(err, wa_response) {
                                                    if (err) {
                                                        console.error(content.from + ' | wa_response: ' + err)
                                                    } else {
                                                        // let _level = user_response.level + 1

                                                        console.error(content.from + ' | wa_response: ' + err)

                                                        let params = {
                                                            phone_no: content.from,
                                                            level: level,
                                                            input: content.message.text,
                                                            unique_identifier: unique_identifier,
                                                            country: user_response.country,
                                                            country_id: user_response.country_id,
                                                            expected_input_type: input_type,
                                                            path: user_response.path + '*' + content.message.text
                                                        }

                                                        whatsappDBService.updateUserSession(params, function(err, session_response) {

                                                            if (err) {
                                                                console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                                    // res.json(err)
                                                            } else {
                                                                try {
                                                                    console.log("Sending response")
                                                                    res.json(wa_response)
                                                                    console.log("Sent response")
                                                                } catch (e) {

                                                                }
                                                            }
                                                        })
                                                    }
                                                })

                        }
                        // if(isTravelUgOption(unique_identifier)){
                            
                        // }
                        if (unique_identifier === 'travel_insurance') {
                            console.log('travel_insurance menu')
                            const menures= getMenuOptionsAndTitle(level,unique_identifier,user_response)
                            let input_type="SELECTION"
                            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menures.menu, function(err, wa_response) {
                                                    if (err) {
                                                        console.error(content.from + ' | wa_response: ' + err)
                                                    } else {
                                                        // let _level = user_response.level + 1

                                                        console.error(content.from + ' | wa_response: ' + err)

                                                        let params = {
                                                            phone_no: content.from,
                                                            level: level,
                                                            input: content.message.text,
                                                            unique_identifier: unique_identifier,
                                                            country: user_response.country,
                                                            country_id: user_response.country_id,
                                                            expected_input_type: input_type,
                                                            path: user_response.path + '*' + content.message.text
                                                        }

                                                        whatsappDBService.updateUserSession(params, function(err, session_response) {

                                                            if (err) {
                                                                console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                                    // res.json(err)
                                                            } else {
                                                                try {
                                                                    console.log("Sending response")
                                                                    res.json(wa_response)
                                                                    console.log("Sent response")
                                                                } catch (e) {

                                                                }
                                                            }
                                                        })
                                                    }
                                                })

                        }
                        else if (unique_identifier === 'health_worker_register_claim_type') { // HW claim starting
                            selfservice.CheckIfHWUserExists(phone_no, function(err, exists) {
                                if (!err) {
                                    if (exists) {
                                        console.log('User exists')
                                            // validationPassed(source_country,level, unique_identifier, user_response, content)

                                        let menu_data = getMenu(level, unique_identifier, user_response)
                                        let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

                                        selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                            if (err) {
                                                console.log('*** Unique ID ignored')
                                            } else {
                                                // console.log('*** Ticket session received!' + resp)
                                                whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu_data.menu, function(err, wa_response) {
                                                    if (err) {
                                                        console.error(content.from + ' | wa_response: ' + err)
                                                    } else {
                                                        // let _level = user_response.level + 1

                                                        console.error(content.from + ' | wa_response: ' + err)

                                                        let params = {
                                                            phone_no: content.from,
                                                            level: level,
                                                            input: content.message.text,
                                                            unique_identifier: unique_identifier,
                                                            country: user_response.country,
                                                            country_id: user_response.country_id,
                                                            expected_input_type: input_type,
                                                            path: user_response.path + '*' + content.message.text
                                                        }

                                                        whatsappDBService.updateUserSession(params, function(err, session_response) {

                                                            if (err) {
                                                                console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                                    // res.json(err)
                                                            } else {
                                                                try {
                                                                    res.json(wa_response)
                                                                } catch (e) {

                                                                }
                                                            }
                                                        })
                                                    }
                                                })
                                            }

                                        })


                                    } else {

                                        validation_error_msg = 'You need to be a registered user before you can initiate a claim process. \n'
                                        level = user_response.level
                                        unique_identifier = user_response.unique_identifier
                                        menu_data = getMenu(level, unique_identifier, user_response)

                                        menu = validation_error_msg + '' + menu_data.menu

                                        console.log('--------------> 1')
                                        whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                            if (err) {
                                                console.error(content.from + ' | wa_response: ' + err)
                                                res.json(false)
                                            } else
                                                res.json(true)
                                        })
                                    }
                                } else {
                                    res.json(false)
                                }
                            })
                        } else if (unique_identifier === 'request_callback' || unique_identifier.includes('success_end_message')) {

                            menu_data = getMenu(level, unique_identifier, user_response)
                            unique_identifier = menu_data.unique_identifier
                            let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

                            user_response.path = user_response.path + '*end'
                            selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                                if (err) {} else {
                                    menu = getTicketResponseMessage(user_response.whatsapp_name, ticket_no, unique_identifier)
                                    sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                    endTicketSession(phone_no)
                                }
                            })

                        } else if (user_response.unique_identifier.includes('health_worker_')) {
                            menu_data = getMenu(level, unique_identifier, user_response)
                            unique_identifier = menu_data.unique_identifier
                            let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)
                                    user_response.path = user_response.path + '*' + content.message.text
                                    sendToWhatsApp(source_country, phone_no, menu_data.menu, input_type, unique_identifier, level)

                                }

                            })
                        } else if (unique_identifier === 'covid_update_message') {
                            selfservice.SMS_Update(phone_no, content.message.text, function(err, _cov_resp) {
                                if (err) {
                                    _res.json(false)
                                    sendToWhatsApp(source_country, phone_no, "Error processing your request. Please try again.\n0 Main Menu", 'END', unique_identifier, level)
                                } else {
                                    let cov_resp = JSON.parse(_cov_resp)
                                    sendToWhatsApp(source_country, phone_no, cov_resp.content + '\n0 Main Menu', 'END', unique_identifier, level)
                                }
                            })
                        } else if (user_response.expected_input_type === 'END') {
                            if (unique_identifier == 'success_end_message') {
                                user_response.unique_identifier = unique_identifier
                            }
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    // console.log('*** Unique ID stored!' + resp)
                                }

                            })
                            unique_identifier = getCurrentUniqueIdentifier(content.message.text, user_response)
                            console.log(utils.getDateTime() + " | REQ: " + content.from + ' | END level: ' + user_response.level + ' input ' + content.message.text + ' Msg Id: ' + content.messageId)
                                /*  if (content.message.text == '0') {
                                      level = 1
                                      user_response.level = 1
                                      unique_identifier = user_response.country
                                      user_response.unique_identifier = user_response.country
                                      console.log(content.from + ' | SELECTION ****: ')
                                  } else {
                                      level = user_response.level + 1
                                  }*/

                            level = 1
                            unique_identifier = user_response.country
                            menu_data = getMenu(level, unique_identifier, user_response)
                            menu = menu_data.menu
                                /*unique_identifier = menu_data.unique_identifier
                                if (user_response.country != null) {
                                    // console.log(content.from + ' | country: ' + country)
                                    unique_identifier = user_response.country
                                }*/

                            if (content.message.text == 1) { //Done uploading photos
                                if (user_response.unique_identifier === 'provide_details_health_worker_success_end_message') {
                                    // Send photos
                                    // Close whatsapp session
                                    // Respond back to user.
                                    console.log('claim_req health worker:')
                                    selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                                        if (err) {} else {
                                            if (ticket_no > 0) {
                                                selfservice.healthcare_worker_claim_request(ticket_no, function(err, claim_response) {
                                                    if (err) {

                                                    } else {
                                                        console.log('claim_response: ' + claim_response)
                                                        user_response.path = user_response.path + '*' + content.message.text
                                                        menu_data = getMenu(level, unique_identifier, user_response)
                                                        menu = menu_data.menu
                                                        unique_identifier = menu_data.unique_identifier

                                                        let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

                                                        menu = getTicketResponseMessage(user_response.whatsapp_name, ticket_no)
                                                        sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)

                                                        console.log('Ticket to close - HealhWorker: ' + ticket_no)
                                                        selfservice.close_user_WhatsApp_Session(phone_no, ticket_no, function(err, close_sess_response) {
                                                            if (err) {

                                                            } else {
                                                                console.log('Session closed for: @close_user_WhatsApp_Session' + phone_no)
                                                            }

                                                        })

                                                    }
                                                })

                                            }
                                        }

                                    })

                                }
                            } else {
                                whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                    if (err) {
                                        console.error(content.from + ' | wa_response: ' + err)
                                    } else {
                                        // if (shouldUpdateSession)

                                        whatsappDBService.updateUserSession({
                                            phone_no: content.from,
                                            level: 1,
                                            expected_input_type: 'SELECTION',
                                            unique_identifier: unique_identifier,
                                            path: user_response.path + '*end',
                                        }, function(err, session_response) {

                                            if (err) {
                                                console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                                    // res.json(err)
                                            } else {
                                                try {
                                                    res.json(wa_response)
                                                } catch (e) {

                                                }
                                            }
                                        })
                                    }
                                })
                            }
                        } else {
                            console.log('-------> *Generic =>> GO TO NEXT ')
                            menu_data = getMenu(level, unique_identifier, user_response)
                            sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', unique_identifier, level, user_response.path)

                        }

                    }
                    /**
                     * USER SENT AN IMAGE
                     *
                     * */
                    else if (user_response.expected_input_type === 'IMAGE') {
                        if (content.message.text == '0' || user_response.unique_identifier == 'back_to_main') {
                            level = 1
                            user_response.level = 1
                            unique_identifier = user_response.country
                            user_response.unique_identifier = user_response.country
                            user_response.path = 'start'
                            console.log(content.from + ' | IMAGE ****: ')
                        } else {
                            level = user_response.level + 1
                            unique_identifier = getCurrentUniqueIdentifier(1, user_response) // An IMAGE share level always has one NEXT step!
                        }
                        console.log(content.from + ' | REQ: IMAGE level: ' + user_response.level + ' new unique_identifier ' + unique_identifier + ' expected_input_type ' + user_response.expected_input_type + ' Msg Id: ' + content.messageId)

                        menu_data = getMenu(level, unique_identifier, user_response)
                        menu = menu_data.menu
                        unique_identifier = menu_data.unique_identifier
                        let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

                        /*if (unique_identifier.includes('health_worker')) {
                            console.log('*** Health worker')
                            user_response.unique_identifier = unique_identifier
                        }
                        else */

                        if (content.message.text == 1) { //Done uploading photos
                            if (unique_identifier === 'provide_details_health_worker_success_end_message') {
                                // Send photos
                                // Close whatsapp session
                                // Respond back to user.
                                console.log('claim_req health worker:')
                                selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                                    if (err) {
                                        _res.json(false)
                                    } else {
                                        if (ticket_no > 0) {
                                            selfservice.healthcare_worker_claim_request(ticket_no, function(err, claim_response) {
                                                if (err) {
                                                    _res.json(false)
                                                } else {
                                                    console.log('claim_response: ' + claim_response)
                                                    user_response.path = user_response.path + '*' + content.message.text
                                                    sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)

                                                    console.log('Ticket to close - HealthWorker: ' + ticket_no)
                                                    selfservice.close_user_WhatsApp_Session(phone_no, ticket_no, function(err, close_sess_response) {
                                                        if (err) {

                                                        } else {
                                                            console.log('Session closed for: @close_user_WhatsApp_Session' + phone_no)
                                                        }

                                                    })

                                                }
                                            })

                                        }
                                    }

                                })

                            } else if (unique_identifier === 'provide_details_photos_end') {
                                selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                                    if (err) {
                                        _res.json(false)
                                    } else {
                                        if (ticket_no > 0) {
                                            user_response.path = user_response.path + '*end'
                                            menu = getTicketResponseMessage(user_response.whatsapp_name, ticket_no, user_response.unique_identifier)
                                            sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                            endTicketSession(phone_no)
                                        } else
                                            _res.json(false)
                                    }
                                })

                            }
                        } else {
                            if (input_type == 'END') {
                                user_response.unique_identifier = 'success_end_message'
                                user_response.path = user_response.path + '*end'
                            }

                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)

                                    user_response.path = user_response.path + '*' + content.message.text
                                    sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                    endTicketSession(phone_no)
                                }

                            })


                        }
                    }
                    /**
                     * USER ENTERED UNSTRUCTURED INPUT e.g. user entered a name, email, phone number etc
                     *
                     * */
                    else {
                        // unique_identifier = user_response.unique_identifier
                        if (content.message.text == '0') {
                            level = 1
                            user_response.level = 1
                            unique_identifier = user_response.country
                            user_response.unique_identifier = user_response.country
                            user_response.path = 'start'
                                // console.log(content.from + ' | SELECTION ****: ')
                        } else {
                            level = user_response.level + 1
                            unique_identifier = getCurrentUniqueIdentifier(1, user_response) // And entry level always has one next step!
                        }

                        console.log(content.from + ' | level: ' + level + ' new unique_identifier ' + unique_identifier + ' OLD ' + user_response.unique_identifier + ' expected_input_type ' + user_response.expected_input_type + ': ' + content.message.text)

                        menu_data = getMenu(level, unique_identifier, user_response)
                        menu = menu_data.menu
                        unique_identifier = menu_data.unique_identifier

                        let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

                        // console.log(utils.getDateTime() + " | REQ: " + content.from + ' | TEXT level: ' + level + ' new unique_identifier ' + unique_identifier + ' | expected_input_type ' + user_response.expected_input_type + ' Msg Id: ' + content.messageId)

                        /**
                         * Add here any unique_identifier that requires ID or Passport verification
                         * */
                        if (user_response.unique_identifier === 'health_worker_national_id' || user_response.unique_identifier === 'verify_id_passport') {
                            let data = validateService.isValidIdOrPassportNo(content.message.text)
                            if (data.error) {
                                level = user_response.level
                                unique_identifier = user_response.unique_identifier
                                    // console.log('--------------> Invalid Input')
                                menu_data = getMenu(level, unique_identifier, user_response)

                                menu = 'Invalid input\n' + menu_data.menu
                                whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                    if (err) {
                                        console.error(content.from + ' | wa_response: ' + err)
                                    } else
                                        res.json(wa_response)
                                })
                            } else {
                                if (user_response.unique_identifier === 'health_worker_national_id')
                                    selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                        if (err) {
                                            console.log('*** Unique ID ignored')
                                        } else {
                                            // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)
                                            user_response.path = user_response.path + '*' + content.message.text
                                            sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)

                                        }

                                    })
                                else
                                    selfservice.ticket_session(phone_no, unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                        if (err) {
                                            console.log('*** Unique ID ignored')
                                        } else {
                                            // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)
                                            user_response.path = user_response.path + '*' + content.message.text
                                            sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)

                                        }

                                    })
                            }

                        }
                        /**
                         * Add here any unique_identifier that requires PHONE NUMBER input validation
                         * */
                        else if (user_response.unique_identifier === 'signup_phone_no') {
                            let valid = validateService.validatePhoneNo(content.message.text)
                            if (!valid) {
                                level = user_response.level
                                unique_identifier = user_response.unique_identifier
                                    // console.log('--------------> Invalid Input')
                                menu_data = getMenu(level, unique_identifier, user_response)

                                menu = 'Invalid phone number format\n' + menu_data.menu
                                whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                    if (err) {
                                        console.error(content.from + ' | wa_response: ' + err)
                                    } else
                                        res.json(wa_response)
                                })
                            } else {
                                if (unique_identifier === 'otp_verification') {
                                    // todo initiate OTP process

                                    selfservice.sendOTP(content.message.text, function(err, otp_resp) {
                                        if (err) {
                                            sendToWhatsApp(source_country, phone_no, otp_resp, input_type, unique_identifier, level, user_response.path)
                                            res.json(false)
                                        } else {
                                            console.log('OTP checking: ' + otp_resp.statusCode)
                                            if (otp_resp.statusCode == '200') {

                                                console.log("Ticket No @200" + otp_resp.statusCode);
                                                sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                                selfservice.check_ticket_no(phone_no, function(e, ticket_no) {
                                                        if (e) {
                                                            console.log("Ticket No Error @sendOTP " + e);
                                                        } else {
                                                            console.log("Ticket No @sendOTP " + ticket_no);

                                                            sessionService.saveWhatsAppSessionData(phone_no, 'customer_verification_phone', ticket_no, content.message.text, function(err, response) {
                                                                if (err) {
                                                                    console.log("Insert new session data Error => " + err);
                                                                } else {
                                                                    console.log("Saved phone number " + response);

                                                                }
                                                            });
                                                        }
                                                    })
                                                    // endTicketSession(phone_no)
                                            } else {
                                                sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                            }

                                        }
                                    })

                                } else if (user_response.unique_identifier.contains('health_worker_')) {
                                    console.log('*** -----> @ health_worker_')
                                    selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                        if (err) {
                                            console.log('*** Unique ID ignored')
                                        } else {
                                            // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)
                                            user_response.path = user_response.path + '*' + content.message.text
                                            sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)

                                        }

                                    })
                                } else
                                    selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                        if (err) {
                                            console.log('*** Unique ID ignored')
                                        } else {
                                            // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)
                                            user_response.path = user_response.path + '*' + content.message.text
                                            sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)

                                        }

                                    })
                            }


                        } else if (user_response.unique_identifier === 'health_worker_beneficiary_phone_no') {
                            let valid = validateService.validatePhoneNo(content.message.text)
                                // console.log('Phone no ' + content.message.text + ' - valid: ' + valid)
                            if (!valid) {
                                let validation_error_msg = 'Invalid input\n'
                                level = user_response.level
                                unique_identifier = user_response.unique_identifier
                                    // console.log('--------------> Invalid Input')
                                menu_data = getMenu(level, unique_identifier, user_response)

                                menu = validation_error_msg + '' + menu_data.menu
                                whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                    if (err) {
                                        console.error(content.from + ' | wa_response: ' + err)
                                    } else
                                        res.json(wa_response)
                                })
                            } else
                                selfservice.ticket_session(phone_no, unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                    if (err) {
                                        /*whatsAppMessageService.sendWhatsAppMessage(source_country,phone_no, 'Error processing your request. Please try again. \n\n0. Main Menu', function (err, wa_response) {
                                            if (err) {
                                                _res.json(false)
                                            } else {
                                                _res.json('Error processing your request. Please try again. \n\n0. Main Menu ')
                                            }
                                        })*/
                                        sendErrorMessage(phone_no, _res)
                                        console.error(utils.getDateTime() + ' | ' + phone_no + 'Error on creating HW Error on ticket_session. Error: ' + err)
                                    } else {
                                        selfservice.reghealthworkers(phone_no, function(err, reg_respose) {
                                            if (err) {
                                                /*whatsAppMessageService.sendWhatsAppMessage(source_country,phone_no, 'Error processing your request. Please try again. \n\n0. Main Menu ', function (err, wa_response) {
                                                    if (err) {
                                                        _res.json(false)
                                                    } else {
                                                        _res.json('Error processing your request. Please try again. \n\n0. Main Menu ')
                                                    }
                                                })*/
                                                sendErrorMessage(phone_no, _res)
                                                console.error(utils.getDateTime() + ' | ' + phone_no + ' Error creating user on ODS. Error: ' + err)
                                            } else {
                                                console.log(utils.getDateTime() + ' | ' + phone_no + 'User created on ODS.')
                                                user_response.path = user_response.path + '*' + content.message.text
                                                level = user_response.level + 1
                                                menu_data = getMenu(level, unique_identifier, user_response)
                                                input_type = 'END'
                                                sendToWhatsApp(source_country, phone_no, menu_data.menu, input_type, unique_identifier, level, user_response.path)

                                                selfservice.end_health_worker_session(phone_no, function(err, sess_response) {

                                                    console.log('*** end_health_worker_session: ' + sess_response)
                                                    if (err) {
                                                        res.json(false)
                                                    } else {
                                                        console.log('Session closed for: @end_health_worker_session' + sess_response)
                                                    }
                                                })
                                            }
                                        })
                                    }
                                })
                        } else if (content.message.text == '00' && user_response.unique_identifier === 'otp_verification') {

                            selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                                if (err) {
                                    _res.json(false)
                                } else {

                                    sessionService.getWhatsAppSessionData(ticket_no, function(err, resp) {
                                        if (err) {
                                            _res.json(false)
                                        } else {

                                            resp.forEach(element => {

                                                if (element.key === "customer_verification_phone") {
                                                    console.log('OTP Resend: ' + JSON.stringify(resp))
                                                    selfservice.sendOTP(element.session_data, function(err, otp_resp) {
                                                        if (err) {
                                                            menu_data = getMenu(7, 'signup_phone_no', user_response)
                                                            menu = menu_data.menu
                                                            sendToWhatsApp(source_country, phone_no, menu, 'TEXT', 'signup_phone_no', 7, user_response.path)
                                                        } else {
                                                            menu_data = getMenu(8, 'otp_verification', user_response)
                                                            menu = menu = 'OTP code has been sent\n' + menu_data.menu
                                                            sendToWhatsApp(source_country, phone_no, menu, 'TEXT', 'otp_verification', 8, user_response.path)
                                                                // endTicketSession(phone_no)
                                                        }
                                                    })
                                                }


                                            })

                                        }


                                    })

                                }
                            })
                        } else if (user_response.unique_identifier === 'photo_selfie_passport_') {
                            if (user_response.input == 1 || user_response.input == 4) {
                                var level = 14
                                menu_data = getMenu(level, 'success_end_message', user_response)
                                sendToWhatsApp(source_country, phone_no, menu_data.menu, 'END', 'success_end_message', level, user_response.path)
                            } else {
                                console.log(`user_response ${JSON.stringify(user_response)}`)
                                var level = user_response.level + 1
                                menu_data = getMenu(level, 'enter_tax_id_number', user_response)
                                sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'enter_tax_id_number', level, user_response.path)
                            }
                        } else if (user_response.unique_identifier === 'enter_tax_id_number') {
                            // validate tax number
                            let valid = validateService.validateTaxIDNumber(content.message.text, source_country)
                            if (!valid) {
                                let validation_error_msg = 'Invalid Taxpayers Identification Number\n'
                                var level = user_response.level
                                unique_identifier = user_response.unique_identifier
                                menu_data = getMenu(level, unique_identifier, user_response)

                                menu = validation_error_msg + '' + menu_data.menu
                                whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                    if (err) {
                                        console.error(content.from + ' | wa_response: ' + err)
                                        res.statusCode(false)
                                    } else
                                        res.json(wa_response)
                                })
                            } else {
                                var currentPath = user_response.path.split("*")
                                var menu = currentPath[3]
                                console.log(`Menu ${menu}`)
                                if (menu === "1") {
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'enter_car_make', user_response)
                                    sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'enter_car_make', level, user_response.path)
                                } else {
                                    var level = user_response.level + 1
                                    menu_data = getMenu(level, 'success_end_message', user_response)
                                    sendToWhatsApp(source_country, phone_no, menu_data.menu, 'END', 'success_end_message', level, user_response.path)
                                }
                            }
                        } else if (user_response.unique_identifier === 'enter_email_address') {
                            let valid = validateService.validateEmail(content.message.text)
                            if (!valid) {
                                let validation_error_msg = 'Invalid email address\n'
                                var level = user_response.level
                                unique_identifier = user_response.unique_identifier
                                menu_data = getMenu(level, unique_identifier, user_response)

                                menu = validation_error_msg + '' + menu_data.menu
                                whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                    if (err) {
                                        console.error(content.from + ' | wa_response: ' + err)
                                        res.statusCode(false)
                                    } else
                                        res.json(wa_response)
                                })
                            } else {
                                var level = user_response.level + 1
                                menu_data = getMenu(level, 'enter_postal_address', user_response)
                                sendToWhatsApp(source_country, phone_no, menu_data.menu, 'SELECTION', 'enter_postal_address', level, user_response.path)
                            }

                        } else if (user_response.unique_identifier === 'enter_car_reg_number') {
                            console.log(`Reg Number ${content.message.text}`)
                            let valid = validateService.validateCarRegistration(content.message.text)
                            if (!valid) {
                                let validation_error_msg = 'Invalid Car Registration No\n'
                                var level = user_response.level
                                unique_identifier = user_response.unique_identifier
                                menu_data = getMenu(level, unique_identifier, user_response)

                                menu = validation_error_msg + '' + menu_data.menu
                                whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                    if (err) {
                                        console.error(content.from + ' | wa_response: ' + err)
                                        res.statusCode(400)
                                    } else
                                        res.json(wa_response)
                                })
                            } else {
                                var level = user_response.level + 1
                                menu_data = getMenu(level, 'success_end_message', user_response)
                                sendToWhatsApp(source_country, phone_no, menu_data.menu, 'END', 'success_end_message', level, user_response.path)
                            }

                        } else if (user_response.unique_identifier === 'enter_national_id') {
                            console.log(`National ID ${content.message.text}`)
                            let valid = validateService.validateIdNumber(content.message.text, source_country)
                            if (!valid) {
                                let validation_error_msg = 'Invalid National ID\n'
                                var level = user_response.level
                                unique_identifier = user_response.unique_identifier
                                menu_data = getMenu(level, unique_identifier, user_response)

                                menu = validation_error_msg + '' + menu_data.menu
                                whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                                    if (err) {
                                        console.error(content.from + ' | wa_response: ' + err)
                                        res.statusCode(false)
                                    } else
                                        res.json(wa_response)
                                })
                            } else {
                                var level = user_response.level + 1;
                                menu_data = getMenu(level, 'invest_enter_kra_pin', user_response)
                                sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'enter_mobile_number', level, user_response.path)
                            }
                        } else if (unique_identifier === 'customer_verification_success_end_message') {
                            console.log('Checking OTP: ' + content.message.text)
                            selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                                if (err) {
                                    _res.json(false)
                                } else {

                                    sessionService.getWhatsAppSessionData(ticket_no, function(err, resp) {
                                        if (err) {
                                            _res.json(false)
                                        } else {

                                            // resp.forEach(element => {
                                            for (let element of resp) {

                                                if (element.key === "customer_verification_phone") {
                                                    selfservice.validateOTP(element.session_data, content.message.text, function(err, otp_resp) {
                                                        if (err) {
                                                            menu = 'Invalid OTP, Please enter again.\n0 Main Menu\n00. Resend OTP'
                                                                // sendToWhatsApp(source_country,phone_no, menu, input_type, unique_identifier, user_response.level, user_response.path)
                                                            whatsAppMessageService.sendWhatsAppMessage(source_country, phone_no, menu, function(err, wa_response) {
                                                                if (err) {
                                                                    _res.json(false)
                                                                } else {
                                                                    _res.json(menu)
                                                                }
                                                            })
                                                        } else {
                                                            console.log('VALID OTP: ' + content.message.text)
                                                            user_response.path = user_response.path + '*' + content.message.text
                                                            selfservice.addCustomerVerification(ticket_no, function(err, add_response) {
                                                                if (err) {
                                                                    _res.json(false)
                                                                } else {
                                                                    sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                                                    endTicketSession(phone_no)
                                                                }


                                                            })

                                                        }
                                                    })
                                                    break;
                                                }
                                            }
                                        }
                                    })
                                }
                            })

                        } else if (unique_identifier.includes('success_end_message')) {

                            let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

                            user_response.path = user_response.path + '*end'
                            selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
                                if (err) {} else {
                                    selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                        if (err) {
                                            console.log('*** Unique ID ignored')
                                        } else {
                                            // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)
                                            user_response.path = user_response.path + '*' + content.message.text
                                            menu = getTicketResponseMessage(user_response.whatsapp_name, ticket_no)
                                            sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, user_response.path)
                                            endTicketSession(phone_no)

                                        }
                                    })
                                }
                            })

                        } else {
                            if (input_type === 'END') {
                                user_response.unique_identifier = 'success_end_message'
                            }
                            selfservice.ticket_session(phone_no, user_response.unique_identifier, content.message.text, user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                if (err) {
                                    console.log('*** Unique ID ignored')
                                } else {
                                    // console.log('*** Unique ID stored! ' + resp + ' uniq: ' + unique_identifier)
                                    user_response.path = user_response.path + '*' + content.message.text
                                    sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)

                                }

                            })
                        }

                    }

                }
            }

        })

    }

}

function gotoInvestUserService(req, _res, source_country, user_response) {
    switch (user_response.expected_input_type) {
        case 'SELECTION':
            investUserService.handleMenuSelection(req, _res, source_country, user_response)
            break;
        case 'TEXT':
            {
                investUserService.handleUserUnstructuredTextInput(req, _res, source_country, user_response)
                break;
            }
        default:
            investUserService.handleUserUnstructuredTextInput(req, _res, source_country, user_response)
            break;
    }

}

function updateTargetProduct(phone_no, unique_identifier) {
    try {

        console.log(phone_no + ' | Updating target menu: ' + unique_identifier)
        whatsappDBService.updateUserSession({
            phone_no: phone_no,
            target_product: unique_identifier,
            unique_identifier: unique_identifier
        }, function(err, session_response) {
            if (err) {
                console.error(phone_no + ' | Existing Error updating user session : ' + session_response)
                    // res.json(err)
            } else {
                console.log(phone_no + ' | Success updating user session : ' + session_response)
            }
        })
    } catch (e) {
        console.error(phone_no + ' | Catch Existing Error updating user session : ' + e.message)
    }

}

function validationPassed(source_country, level, unique_identifier, user_response, content) {
    let phone_no = content.from
    menu_data = getMenu(level, unique_identifier, user_response)
    let name = ""

    if ("contact" in content && level == 1) {
        content.contact.name = content.contact.name.replace(/([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        if (typeof content.contact.name == 'string') {
            name = content.contact.name
            if (source_country === 'tz')
                menu = 'Hi ' + name + ', we\'re happy to have you here 🙂. Welcome to UAP. I will be your virtual assistant.' +
                ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n' + menu_data.menu
            else {
                menu = 'Hi ' + name + ', we\'re happy to have you here :-). Welcome to UAP Old Mutual and Faulu Microfinance Bank. I will be your virtual assistant.' +
                    ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n' + menu_data.menu
            }
        } else
            menu = menu_data.menu

    } else
        menu = menu_data.menu


    if (level == '1') {
        // console.log('----> WhatsAPP to be created')
        selfservice.create_wa_user(phone_no, name, user_response.country, function(err, resp) {
            if (!err)
                console.log('----> WhatsAPP user created ' + resp)
        })
    }

    unique_identifier = menu_data.unique_identifier


    let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);
    // console.log(content.from + ' @validationPassed | new level: ' + level + ' old level: ' + user_response.level + ' new unique_identifier ' + unique_identifier + ' OLD ' + user_response.unique_identifier + ' next input type ' + input_type)

    /* if (unique_identifier.includes('health_worker')) {
         console.log('*** Health worker')
         user_response.unique_identifier = unique_identifier
     }
     else */
    if (input_type == 'END') {
        user_response.unique_identifier = 'success_end_message'
    }
    // phone_no, unique_identifier, input, level, expected_input_type, path_taken, source_channel,
    selfservice.ticket_session(phone_no, unique_identifier, content.message.text, level, user_response.expected_input_type, user_response.path, function(err, resp) {
        if (err) {
            console.log('*** Unique ID ignored' + err)
        } else {
            // console.log('*** Ticket session received!' + resp)
            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
                if (err) {
                    console.error(content.from + ' | wa_response: ' + err)
                } else {
                    // let _level = user_response.level + 1

                    console.error(content.from + ' | wa_response: ' + err)

                    let params = {
                            phone_no: content.from,
                            level: level,
                            input: content.message.text,
                            unique_identifier: unique_identifier,
                            country: user_response.country,
                            country_id: user_response.country_id,
                            expected_input_type: input_type,
                            path: user_response.path + '*' + content.message.text
                        }
                        //console.log('### params: ' + JSON.stringify(params))
                        // res.json(wa_response)
                    if (shouldUpdateSession)
                        whatsappDBService.updateUserSession(params, function(err, session_response) {

                            if (err) {
                                console.error(content.from + ' | Existing Error updating user session : ' + session_response)
                                    // res.json(err)
                            } else {
                                try {
                                    res.json(wa_response)
                                } catch (e) {

                                }
                            }
                        })
                }
            })
        }

    })
}

function endTicketSession(phone_no) {
    selfservice.check_ticket_no(phone_no, function(err, ticket_no) {
        if (err) {

        } else {

            // console.log('provide_details_photos_end NON healthWorker:')
            selfservice.update_ticket(ticket_no, function(err, update_ticket_response) {
                if (err) {

                } else {
                    selfservice.close_user_WhatsApp_Session(phone_no, ticket_no, function(err, close_sess_response) {
                        if (err) {

                        } else {
                            // console.log('Ticket to closed NON Healthworker: ' + ticket_no + ' Message: ' + update_ticket_response)
                        }

                    })
                }
            })
        }

    })
}

function handleUserImages(req, _res, source_country = 'ke') {
    res = _res;
    let content = req.body.results[0];
    let unique_identifier
    let phone_no = content.from

    console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: IMAGE ' + content.messageId)
        // console.log('Attached docs: '+JSON.stringify(req.body))

    whatsappDBService.getUserLevel(phone_no, function(err, user_response) {
        if (err) {
            initialWelcome(salutation_response[0], content, source_country)
        } else {
            unique_identifier = getCurrentUniqueIdentifier(1, user_response)
            console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: IMAGE level: ' + user_response.level + ' new unique_identifier ' + unique_identifier + ' old unique_identifier ' + user_response.unique_identifier + ' input ' + content.message.url + ' expected_input_type ' + user_response.expected_input_type + ' Msg Id: ' + content.messageId)

            if (content.message.text == '0' || unique_identifier == 'back_to_main') {
                level = 1
                unique_identifier = user_response.country
                user_response.path = 'start'
            } else
                level = user_response.level

            let input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);

            /*if (unique_identifier.includes('health_worker')) {
                console.log('*** Health worker')
                user_response.unique_identifier = unique_identifier
            }
            else */
            console.log(user_response.unique_identifier)
            if (user_response.unique_identifier === 'photo_national_id' || user_response.unique_identifier === 'photo_passport') {
                // if (user_response.input == 1 || user_response.input == 4) {
                //     var level = 14
                //     menu_data = getMenu(level, 'success_end_message', user_response)
                //     sendToWhatsApp(source_country, phone_no, menu_data.menu, 'END', 'success_end_message', level, user_response.path)
                // } else {
                console.log(`user_response ${JSON.stringify(user_response)}`)
                var level = user_response.level + 1
                menu_data = getMenu(level, 'enter_tax_id_number', user_response)
                sendToWhatsApp(source_country, phone_no, menu_data.menu, 'TEXT', 'enter_tax_id_number', level, user_response.path)
                    // }
            }
            if (input_type == 'END') {
                user_response.unique_identifier = 'success_end_message'
            }
            if (user_response.unique_identifier === 'health_worker_national_id_photo' || user_response.unique_identifier === 'health_worker_staff_id_photo') {

                selfservice.download_reg_media(content.message.url, phone_no, content.message.caption, function(err, resp) {
                    if (err) {
                        console.error(utils.getDateTime() + ' | ' + phone_no + 'Error downloading HW registration images ' + err)
                        sendErrorMessage(phone_no, _res)
                    } else {
                        console.log('*** Health worker media saved')
                        level = user_response.level + 1
                        menu_data = getMenu(level, unique_identifier, user_response)
                        menu = menu_data.menu
                        unique_identifier = menu_data.unique_identifier
                        input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);
                        sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)
                    }
                })

            } else
                selfservice.get_ticket_no(phone_no, 2, user_response.level, content.message.url, user_response.unique_identifier, user_response.expected_input_type, user_response.path, function(err, ticket_no) {
                    if (err) {
                        res.json(false)
                    } else {
                        selfservice.download_file(content.message.url, phone_no, ticket_no, content.message.caption, function(err, resp) {
                            if (err) {
                                console.log("Error => " + err);
                            } else {
                                selfservice.ticket_session(phone_no, user_response.unique_identifier, 'image', user_response.level, user_response.expected_input_type, user_response.path, function(err, resp) {
                                    if (err) {
                                        console.log('*** Unique ID ignored')
                                    } else {
                                        // console.log('*** Unique ID stored!' + unique_identifier)
                                        if (unique_identifier.includes('health_worker') ||
                                            unique_identifier === 'signup_phone_no' ||
                                            unique_identifier === 'selfie_passport_photo') {
                                            console.log('--HW photo' + resp)
                                            level = user_response.level + 1
                                            menu_data = getMenu(level, unique_identifier, user_response)
                                            menu = menu_data.menu
                                            unique_identifier = menu_data.unique_identifier
                                            input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier, user_response);
                                            sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level)
                                        }
                                        /*else if (unique_identifier === 'provide_details_photos_end') {
                                                                               level = user_response.level + 1
                                                                               menu_data = getMenu(level, unique_identifier, user_response)
                                                                               menu = menu_data.menu
                                                                               unique_identifier = menu_data.unique_identifier
                                                                               input_type = whatsAppMenuService.getMenuBlock(level, unique_identifier,user_response)
                                                                               sendToWhatsApp(source_country,phone_no, menu, input_type, unique_identifier, level)
                                                                               endTicketSession(phone_no)
                                                                           }*/
                                        else

                                            res.json(true)
                                    }

                                })
                            }
                        })
                    }
                })

        }
    })

}

function handleUserVideos(req, _res) {
    console.log('*** Unique ID ignored')
    let content = req.body.results[0];
    console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: VIDEO TYPE UPLOADED Msg Id ' + content.messageId)

    let menu = 'Please upload a photo or a document.'
    whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, menu, function(err, wa_response) {
        if (err) {
            _res.json(false)
        } else {
            _res.json(menu)
        }
    })
}

function handleUserDocuments(req, _res) {
    console.log(utils.getDateTime() + ' ' + content.from + ' | REQ: DOCUMENT ' + content.messageId)
    handleUserImages(req, _res)

}

function handleUserAudio() {

}

function goToMainMenu() {


}
function getMenuOptionsAndTitle(level, unique_identifier, user_response) {
    
    console.log('--> 1 CHECK MENU  level: ' + level + ' Unique: ' + unique_identifier + ' Prev input: ' + user_response.input)
    console.log('User data: ' + JSON.stringify(user_response))

    let _menuTitle = whatsAppMenuService.getMenu(level, unique_identifier, user_response)
    let _menuOptions=whatsAppMenuService.getMenuTags(level, unique_identifier, user_response)
    let menu=''

    menu+=_menuTitle
    menu+='\n'
    for( const [key,value] of Object.entries(_menuOptions)){
        menu+=`${key}. ${value}\n`
    }
     shouldUpdateSession = true;
     return { menu: menu, unique_identifier: unique_identifier };
    

}
function getMenu(level, unique_identifier, user_response) {

    console.log('--> 1 CHECK MENU  level: ' + level + ' Unique: ' + unique_identifier + ' Prev input: ' + user_response.input)
    console.log('User data: ' + JSON.stringify(user_response))
    let _menu = whatsAppMenuService.getMenu(level, unique_identifier, user_response)
    if (typeof _menu === 'undefined') {
        let _level = level - 1;
        console.log(utils.getDateTime() + " | " + user_response.phone_no + 'MENU: getMenu() not found  level: ' + _level + ' Unique: ' + unique_identifier + ' Prev input: ' + user_response.input)
        shouldUpdateSession = false;
        unique_identifier = whatsAppMenuService.getMenuByIndex(user_response.level, user_response.unique_identifier, user_response.input, user_response)
            //console.log('--> Previous uniq %% ' + unique_identifier)
        _menu = whatsAppMenuService.getMenu(level, unique_identifier, user_response)
        return { menu: _menu, unique_identifier: unique_identifier };
    } else {
        console.log('--> Menu Found  level: ' + level + ' Unique: ' + unique_identifier + ' Prev input: ' + user_response.input + ' - ' + _menu.title)
        shouldUpdateSession = true;
        return { menu: _menu, unique_identifier: unique_identifier };
    }
}

function getCurrentUniqueIdentifier(input, user_response) {

    let _unique_identifier, level;
    _unique_identifier = whatsAppMenuService.getMenuByIndex(user_response.level, user_response.unique_identifier, input, user_response)

    if (typeof _unique_identifier === 'undefined') {
        shouldUpdateSession = false

        if (user_response.level == 1) {
            //return whatsAppMenuService.getMenuByIndex(0, 'start', input, user_response)

            return whatsAppMenuService.getMenuByIndex(0, 'start', user_response.country_id, user_response)
        } else {
            return user_response.unique_identifier
        }
        return whatsAppMenuService.getMenu(level, _unique_identifier)
    } else {
        if (user_response.level == 1)
            shouldUpdateSession = true;
        return _unique_identifier;
    }
}

function initialWelcome(salutation, content, source_country) {
    let name = ""
    if ("contact" in content) {
        content.contact.name = content.contact.name.replace(/([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        if (typeof content.contact.name == 'string') {
            salutation = salutation + " " + content.contact.name
            name = content.contact.name
        }

    }
    let salute = ''
    if (source_country === 'tz')
        salute = salutation + ', we\'re happy to have you here 🙂. Welcome to UAP. I will be your virtual assistant.' +
        ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n'
    else
    //
    // salute = salutation + ', we\'re happy to have you here 🙂. Welcome to UAP Old Mutual and Faulu Microfinance Bank. I will be your virtual assistant.' +
    // ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n'

        salute = salutation + ', we\'re happy to have you here 🙂. Welcome to UAP Old Mutual and Faulu Microfinance Bank. I will be your virtual assistant.' +
        ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n\n'

    /*let salute = salutation + ', we\'re happy to have you here 🙂. Welcome to UAP Old Mutual and Faulu Microfinance Bank. I will be your virtual assistant.' +
        ' By proceeding to the next step you\'re agreeing to our terms and conditions found here:  ' + tnc_link + ' \n';*/
    // console.log('Salutation ' + salute)
    const countryCodes = require('country-codes-list')

    // const myCountryCodesObject = countryCodes.customList('countryCode', '{countryNameEn}')
    const myCountryCodesObject = countryCodes.findOne('countryCode', source_country.toUpperCase())
    console.log('CCCC ' + JSON.stringify(myCountryCodesObject))

    whatsappDBService.updateUserSession({
        phone_no: content.from,
        whatsapp_name: name,
        level: 0,
        input: content.message.text,
        unique_identifier: 'start',
        expected_input_type: 'SELECTION',
        country: myCountryCodesObject.countryNameEn.toLowerCase(),
        country_id: null,
        path: 'start'
    }, function(err, msg) {
        if (err) {
            res.json(msg)
        } else {
            salute = salute + '\n' + whatsAppMenuService.getMenu(0, 'start', { country: myCountryCodesObject.countryNameEn })
            whatsAppMessageService.sendWhatsAppMessage(source_country, content.from, salute, function(err, wa_response) {
                if (err) {
                    console.error(content.from + ' | wa_response: ' + err)
                } else {
                    // whatsAppSessionService.startSession(content.from,'name','Emmanuel')
                    try {
                        res.json(wa_response)
                    } catch (e) {

                    }
                }

            })

        }


    });

}

function sendToWhatsApp(source_country, phone_no, menu, input_type, unique_identifier, level, path) {
    whatsAppMessageService.sendWhatsAppMessage(source_country, phone_no, menu, function(err, wa_response) {
        if (err) {
            console.error(phone_no + ' | wa_response: ' + err)
            try {
                res.json(false)
            } catch (e) {

            }
        } else {
            // if (shouldUpdateSession)

            whatsappDBService.updateUserSession({
                phone_no: phone_no,
                expected_input_type: input_type,
                unique_identifier: unique_identifier,
                level: level,
                path: path
            }, function(err, session_response) {

                if (err) {
                    console.error(phone_no + ' | Existing Error updating user session : ' + session_response)
                        // res.json(err)
                } else {
                    // console.log('All done ' + wa_response)
                    try {
                        res.json(true)
                    } catch (e) {

                    }
                }
            })
        }
    })
}

function getTicketResponseMessage(name, ticket_no, unique_identifier = null) {

    if (unique_identifier !== null && unique_identifier == 'bank_with_us_option_success_end_message')
        return 'Hi ' + name + ', Thank you for contacting us. Your ticket number is ' + ticket_no + '. You will be contacted by our call centre team shortly. ' +
            'To view our online portal, click here: https://www.faulukenya.com/ and to view our T&Cs click here: https://bit.ly/3gF1AnI\n\n0. Main Menu'
    else if (unique_identifier !== null && unique_identifier === 'provide_details_car_photos')
        return 'Hi ' + name + ', Thank you for contacting us. Your ticket number is ' + ticket_no + '. You will be contacted by our call centre team shortly. ' +
            'If within Nairobi and its environs, kindly proceed to our assessment centre at Lower Upper Hill road for review and authorization. ' +
            'To view our online portal, click here: https://bit.ly/2V2jEze and to view our T&Cs click here:  ' + tnc_link + ' \n\n0. Main Menu'
    else
        return 'Hi ' + name + ', Thank you for contacting us. Your ticket number is ' + ticket_no + '. You will be contacted by our call centre team shortly. ' +
            'To view our online portal, click here: https://bit.ly/2V2jEze and to view our T&Cs click here:  ' + tnc_link + ' \n\n0. Main Menu'

}

function updateCountry(params) {
    whatsappDBService.updateCountry(params, function(err, msg) {
        if (err) {
            console.log('Error updating country')
        } else {
            console.log('Country updated: ' + JSON.stringify(params))
        }
    })
}

function sendErrorMessage(phone_no, _res) {
    whatsAppMessageService.sendWhatsAppMessage(source_country, phone_no, 'Error processing your request. Please try again. \n\n0. Main Menu', function(err, wa_response) {
        if (err) {
            _res.json(false)
        } else {
            _res.json('Error processing your request. Please try again. \n\n0. Main Menu ')
        }
    })
}


module.exports = {
    handleUserText: handleUserText,
    handleUserImages,
    handleUserDocuments,
    handleUserVideos
};