'use strict';

let date_format = require('dateformat');

function getCurrentDateTime() {
    return date_format(new Date());
}

/***
 * @example parameterizedString("my name is %s1 and surname is %s2", "<PERSON>", "<PERSON><PERSON>");
 * @return "my name is <PERSON> and surname is <PERSON><PERSON>"
 *
 * @firstArgument {String} like "my name is %s1 and surname is %s2"
 * @otherArguments {String | Number}
 * @returns {String}
 */
const parameterizedString = (...args) => {
    const str = args[0];
    const params = args.filter((arg, index) => index !== 0);
    if (!str) return "";
    return str.replace(/%s[0-9]+/g, matchedStr => {
        const variableIndex = matchedStr.replace("%s", "") - 1;
        return params[variableIndex];
    });
}

const countryCodes = require('country-codes-list')
function getCountryDetailsByCode(source_country){
    return countryCodes.findOne('countryCode', source_country.toUpperCase())
}
function getCountryDetailsByCountryName(source_country){

    return countryCodes.findOne('countryNameEn', titleCase(source_country))
}
function titleCase(str) {
    str = str.toLowerCase().split(' ');
    for (var i = 0; i < str.length; i++) {
        str[i] = str[i].charAt(0).toUpperCase() + str[i].slice(1);
    }
    return str.join(' ');
}

module.exports = {
    getDateTime: getCurrentDateTime,
    getCountryDetailsByCode: getCountryDetailsByCode,
    getCountryDetailsByCountryName: getCountryDetailsByCountryName,
    titleCase: titleCase,
    parameterizedString: parameterizedString
}