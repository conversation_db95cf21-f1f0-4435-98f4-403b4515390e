'use strict';

let menuContent = require('./WhatsAppMenuContent');
let menuContent_rw = require('./rw/WhatsAppMenuContent_rw');
let menuContent_tz = require('./WhatsAppTzMenu');
let menuContent_ss = require('./WhatsAppMenuContent_ss');
let menuContent_ug = require('./ug/WhatsAppMenuContent_ug');

const ERR_GET_MENU = 'EGM';

function getMenuDataByCountry(country) {
    let rawdata;
    try {
        if (country == null)
            country = 'uganda'

        // console.log('getMenuDataByCountry country_response: ' + country)
        switch (country) {
            case 'rwanda':
                rawdata = menuContent_rw.getMenuContent();
                break;
            case 'kenya':
                rawdata = menuContent.getMenuContent();
                break;
            case 'uganda':
                rawdata = menuContent_ug.getMenuContent();
                break;
            case 'tanzania':
                rawdata = menuContent_tz.getMenuContent();
                break;
            case 'south_sudan':
                rawdata = menuContent_ss.getMenuContent();
                break;
            default:
                rawdata = menuContent.getMenuContent();
                break;
        }
        return rawdata;

    } catch (e) {
        console.log('Error catch getWhatsAppCountry: ' + e)
        return 'Error getting Country';
    }


}

function getMenu(level, unique_identifier, user_response) {
    try {
        user_response.country="uganda";
        if(unique_identifier==="kenya"){
            unique_identifier="uganda";
        }
        let rawdata = getMenuDataByCountry(user_response.country);
        return rawdata[level][unique_identifier].title;
    } catch (e) {
        console.log('Error getMenu: ' + e)
    }
}

function getMenuByIndex(level, unique_identifier, menu_index, user_response) {
    try {
         user_response.country="uganda";
        if(unique_identifier==="kenya"){
            unique_identifier="uganda";
        }
        let rawdata = getMenuDataByCountry(user_response.country);
        return rawdata[level][unique_identifier].next[menu_index];
    } catch (e) {
        console.log('Error getMenuByIndex: ' + e)
    }
}

function getMenuBlock(level, unique_identifier, user_response) {
    try {
        let rawdata = getMenuDataByCountry(user_response.country);
        return rawdata[level][unique_identifier].input_type;
    } catch (e) {
        console.log('Error getMenuBlock: ' + e)
    }
}

function getMenuTags(level, unique_identifier, user_response) {
    try {
        user_response.country="uganda";
        if(unique_identifier==="kenya"){
            unique_identifier="uganda";
        }
        let rawdata = getMenuDataByCountry(user_response.country);
        return rawdata[level][unique_identifier].next;
    } catch (e) {
        console.log('Error getMenuTags: ' + e)

    }

}


function getErrorMsg(location) {
    return 'An error occurred please try again.\n Error code:' + ERR_GET_MENU + '_' + location;

}



module.exports = {
    getMenu: getMenu,
    getMenuTags: getMenuTags,
    getMenuByIndex: getMenuByIndex,
    getMenuBlock: getMenuBlock
};