exports.getMenuContent = function () {
    return {
        "0": {
            "start": {
                "title": "Kindly Select your Country of Choice\n1 Kenya\n2 Uganda\n3 Tanzania\n4 Rwanda\n5 South Sudan",
                "next": {
                    "1": "kenya",
                    "2": "uganda",
                    "3": "tanzania",
                    "4": "rwanda",
                    "5": "south_sudan"
                },
                "input_type": "SELECTION"
            }
        },
        "1": {
            "kenya": {
                "title": "How can we be of service today?\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n5 Invest With Us\n6 COVID-19 HealthCare Workers Insurance \n7 Bank With Us\n8 New Customer Verification\n9 Update My Personal Details\n10 Wellness & COVID-19 Updates\n11 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    "5": "invest_with_us",
                    "6": "health_worker",
                    "7": "bank_with_us",
                    "8": "new_customer_verification",
                    "9": "update_personal_details",
                    "10": "covid",
                    "11": "start",
                },
                "input_type": "SELECTION"
            },
            "uganda": {
                "title": "How can we be of service today?\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n5 Invest With Us\n6 New Customer Verification\n7 Update My Personal Details\n8 Wellness & COVID-19 Updates\n11 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    "5": "invest_with_us",
                    "6": "new_customer_verification",
                    "7": "update_personal_details",
                    "8": "covid",
                    "11": "start",
                },
                "input_type": "SELECTION"
            },
            "tanzania": {
                "title": "How can we be of service today?\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n5 New Customer Verification\n6 Update My Personal Details\n7 Wellness & COVID-19 Updates\n11 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    "5": "new_customer_verification",
                    "6": "update_personal_details",
                    "7": "covid",
                    "11": "start",
                },
                "input_type": "SELECTION"
            },
            "rwanda": {
                "title": "How can we be of service today?\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n5 New Customer Verification\n6 Update My Personal Details\n7 Wellness & COVID-19 Updates\n11 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    "5": "new_customer_verification",
                    "6": "update_personal_details",
                    "7": "covid",
                    "11": "start",
                },
                "input_type": "SELECTION"
            },
            "south_sudan": {
                "title": "How can we be of service today?\n1 Register Claim\n2 Policy Status\n3 Buy Insurance\n4 Insurance Payment\n5 New Customer Verification\n6 Update My Personal Details\n8 Wellness & COVID-19 Updates\n11 Change Country",
                "next": {
                    "1": "register_claim",
                    "2": "policy_status",
                    "3": "buy_insurance",
                    "4": "make_insurance_payment",
                    "5": "new_customer_verification",
                    "6": "update_personal_details",
                    "8": "covid",
                    "11": "start",
                },
                "input_type": "SELECTION"
            }
        },
        "2": {
            "register_claim": {
                "title": "Which insurance would you like to register claim for?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "claim_health",
                    "2": "claim_car",
                    "3": "claim_life",
                    "4": "claim_other",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status": {
                "title": "Policy status for?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "policy_status_health",
                    "2": "policy_status_car",
                    "3": "policy_status_life",
                    "4": "policy_status_other",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "buy_insurance": {
                "title": "Buy For?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "buy_insurance_health_success_end_message",
                    "2": "buy_insurance_car_success_end_message",
                    "3": "buy_insurance_life_success_end_message",
                    "4": "buy_insurance_other_success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment": {
                "title": "Make payment to?\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "make_insurance_payment_health",
                    "2": "make_insurance_payment_car",
                    "3": "make_insurance_payment_life",
                    "4": "make_insurance_payment_other",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "invest_with_us": {
                "title": "We have a fully dedicated investment platform i-INVEST\nClick here https://bit.ly/3bDgln4 to chat with Arifa our online virtual assistant. Type 'hi Arifa' in your Facebook Messenger chat to start investing.\nor\nDial *480#\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "new_customer_verification": {
                "title": "Which Insurance class have you bought:\n1. Health Insurance\n2. Car Insurance\n3. Life Insurance\n4. Other Insurance type\n0 Main Menu",
                "next": {
                    "1": "verify_enter_name",
                    "2": "verify_enter_name",
                    "3": "verify_enter_name",
                    "4": "verify_enter_name",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "update_personal_details": {
                "title": "To update your personal details kindly click on this link https://bit.ly/2LazEcA#",
                "next": {
                    "1": "end"
                },
                "input_type": "END"
            },
            "bank_with_us": {
                "title": "Which Faulu Micro-finance Bank product are you interested In: \n1. Loan\n2. Current Account\n3. Savings Account\n4. Fixed Deposit Account\n0 Main Menu",
                "next": {
                    "1": "bank_with_us_option_success_end_message",
                    "2": "bank_with_us_option_success_end_message",
                    "3": "bank_with_us_option_success_end_message",
                    "4": "bank_with_us_option_success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "health_worker": {
                "title": "How can we help you?\n1. New User Registration\n2. Register a Claim \n0 Main Menu",
                "next": {
                    "1": "health_worker_profession",
                    "2": "health_worker_register_claim_type",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "covid": {
                "title": "Which option would you prefer an update on?\n1. Keeping Healthy\n2. Feeling Unwell\n3. Working From Home\n4. Studying From Home\n5. Unwinding From Home\n6. Ministry of Health Updates\n0 Main Menu",
                "next": {
                    "1": "covid_update_message",
                    "2": "covid_update_message",
                    "3": "covid_update_message",
                    "4": "covid_update_message",
                    "5": "covid_update_message",
                    "6": "covid_update_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            }
        },
        "3": {
            "claim_health": {
                "title": "Select how to start the Health Insurance claim\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_health",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "claim_car": {
                "title": "Select how to start the Car Insurance claim\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_car",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "claim_life": {
                "title": "Select how to start the Life Insurance claim\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_life",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "claim_other": {
                "title": "Select how to start the Insurance claim\n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_other",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_health": {
                "title": "Select how to start your policy status inquiry \n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_health",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_car": {
                "title": "Select how to start your policy status inquiry \n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_car",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_life": {
                "title": "Select how to start your policy status inquiry \n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_life",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "policy_status_other": {
                "title": "Select how to start your policy status inquiry \n1. Provide Policy Details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_policy_status_other",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "buy_insurance_health_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "buy_insurance_car_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "buy_insurance_life_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "buy_insurance_other_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "make_insurance_payment_health": {
                "title": "Select How to Start the Insurance Payment \n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_health",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment_car": {
                "title": "Select How to Start the Insurance Payment \n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_car",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment_life": {
                "title": "Select How to Start the Insurance Payment \n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_life",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "make_insurance_payment_other": {
                "title": "Select How to Start the Insurance Payment \n1. Provide policy details \n2. Request a call back  \n0 Main Menu",
                "next": {
                    "1": "provide_details_payment_other",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "verify_enter_name": {
                "title": "Kindly enter your full name?\n0 Main Menu",
                "next": {
                    "1": "verify_id_passport",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "bank_with_us_option_success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "covid_update_message": {
                "title": "Dear customer, please check health tips here <>  \n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "health_worker_profession": {
                "title": "Kindly select your profession:\n1. Doctor\n2. Nurse\n3. Clinical Officer\n4. Laboratory Technician\n5. Public Health Officer or Technician\n0 Main Menu",
                "next": {
                    "1": "health_worker_name",
                    "2": "health_worker_name",
                    "3": "health_worker_name",
                    "4": "health_worker_name",
                    "5": "health_worker_name",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "health_worker_register_claim_type": {
                "title": "Kindly select the type of claim\n1. Hospital Benefit \n2. Last Expense Benefit\n0 Main Menu",
                "next": {
                    "1": "health_worker_register_claim_hosp",
                    "2": "health_worker_register_claim_last",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            }
        },
        "4": {
            "request_callback": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "provide_details_policy_status_health": {
                "title": "What\'s Your Health Insurance Policy Number? eg. UK032344-01 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_policy_status_car": {
                "title": "What\'s Your Car Registration Number? e.g KAA123A\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_policy_status_other": {
                "title": "What\'s Your Insurance Policy Number?\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_policy_status_life": {
                "title": "What\'s your Life Insurance policy number? e.g. OMK001429206 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            }, "provide_details_payment_health": {
                "title": "What\'s Your Health Insurance Policy Number? eg. UK032344-01 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_payment_car": {
                "title": "What\'s Your Car Registration Number? e.g KAA123A\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_payment_other": {
                "title": "What\'s Your Insurance Policy Number?\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_payment_life": {
                "title": "What\'s Your Life Insurance Policy Number? e.g. OMK001429206 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_car": {
                "title": "What\'s Your Car Registration Number? e.g KAA123A\n \n0 Main Menu",
                "next": {
                    "1": "provide_details_car_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },

            "provide_details_other": {
                "title": "What\'s Your Insurance policy number?\n \n0 Main Menu",
                "next": {
                    "1": "provide_details_other_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_life": {
                "title": "What\'s your Life Insurance policy number? e.g. OMK001429206 or National ID Number\n \n0 Main Menu",
                "next": {
                    "1": "provide_details_life_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health": {
                "title": "What\'s your Health Insurance policy number? e.g. UK032344-01 or National ID Number\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_photos",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "verify_id_passport": {
                "title": "What\'s your National ID e.g. 1234567 or Passport Number e.g. ********\n\n0 Main Menu",
                "next": {
                    "1": "verify_id_photo",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "health_worker_name": {
                "title": "Kindly enter your  first name and surname\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_national_id",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            }, "health_worker_register_claim_hosp": {
                "title": "Select how to start your Hospital Benefit claim \n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_id",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            }, "health_worker_register_claim_last": {
                "title": "Select how to start the Last Expense Benefit claim \n1. Provide claim details \n2. Request a call back \n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_last_claim_id",
                    "2": "request_callback",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
        },
        "5": {
            "provide_details_health_photos": {
                "title": "Share photos of your insurance supporting documents e.g. Medical Receipts \nType 1 to complete your submission\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_photos_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_car_photos": {
                "title": "Share photos of your car and any other photos of supporting documents e.g. Police Abstract.\nType 1 to complete your submission\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_photos_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_life_photos": {
                "title": "Share a Photo of Your Insurance Supporting Documents e.g. Death Certificate.\nType 1 to complete your submission\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_photos_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },

            "provide_details_other_photos": {
                "title": "Share photo of your Insurance supporting Documents? e.g. Medical receipts, Death certificate, Police abstract, Damaged property\nType 1 to complete your submission\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_photos_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },

            "provide_details": {
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "SELECTION"
            },
            "verify_id_photo": {
                "title": "Kindly Take & Upload a Photo of Your National ID or Passport \n\n0 Main Menu",
                "next": {
                    "1": "selfie_passport_photo",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "health_worker_national_id": {
                "title": "Kindly enter your National ID Number e.g. 12345678\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_facility",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_id": {
                "title": "Kindly enter your National ID Number? e.g. 12345678\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_staff_card",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_hosp_claim_id": {
                "title": "Kindly share photo of your National ID Card\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_staff_card",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_last_claim_id": {
                "title": "Kindly share photo of your National ID Card\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_last_claim_id_hw",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "request_callback": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
        },
        "6": {
            "provide_details_car_additional_photos": {
                "title": "Share a photo of police abstract or any other supportive photos\n\n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_other_additional_photos": {
                "title": "Share a photo of additional supportive documents\n 1. Complete Submission \n0. Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_photos_end": {
                "title": "Thank you for the details provided, one of our Call Center representatives will contact you within 24 Hours.\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "provide_details_life_additional_photos": {
                "title": "Share a photo of additional supportive documents\n 1. Complete Submission \n0. Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "selfie_passport_photo": {
                "title": "Kindly take & upload a Selfie or Passport Photo\n0. Main Menu",
                "next": {
                    "1": "signup_phone_no",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "health_worker_facility": {
                "title": "Kindly enter your primary health facility of operation?\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_reg_no",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_supporting_docs": {
                "title": "Kindly share photos of your insurance claim supporting documents e.g. Medical Receipts, Hospital Admission Form or Death Certificate\n\nMaximum 10 attachments.\nType 1 when done.\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_end",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_hosp_claim_staff_card": {
                "title": "Kindly share your a photo of staff card of employing health facility\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_discharge",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_last_claim_id_hw": {
                "title": "Kindly share a photo of the Health Worker\'s National ID Card\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_last_facility",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
        },
        "7": {
            "success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "signup_phone_no": {
                "title": "Kindly enter the phone number you used to sign-up for the new product? e.g. 0721******\n0. Main Menu",
                "next": {
                    "1": "otp_verification",
                    "2": "end"
                },
                "input_type": "TEXT"
            },
            "health_worker_reg_no": {
                "title": "Kindly enter your professional registration / licence number\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_beneficiary_name",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_end": {
                "title": "We have successfully received your claim notification.\n One of our call center representatives will contact you within 24 Hours.\n\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "provide_details_health_worker_hosp_claim_discharge": {
                "title": "Kindly share your hospitalization discharge summary indicating dates of admission and discharge.\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_hosp_claim_covid_report",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_last_facility": {
                "title": "Kindly share a photo staff ID card of the facility where the Healthcare Worker worked.\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_last_death_letter",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            }
        },
        "8": {
            "end": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                "input_type": "END"
            },
            "back_to_main": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                "input_type": "END"
            },
            "otp_verification": {
                "title": "Kindly Insert the 4 Digit Code Sent To You Via SMS\n0 Main Menu\n00. Resend OTP",
                "next": {
                    "1": "customer_verification_success_end_message",
                    "0": "back_to_main",
                    "00": "otp_verification",
                },
                "input_type": "TEXT"
            },
            "health_worker_beneficiary_name": {
                "title": "Beneficiary Information\nKindly enter your beneficiary\'s name (first name & surname)\n\n0 Main Menu",
                "next": {
                    "1": "health_worker_beneficiary_phone_no",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_hosp_claim_covid_report": {
                "title": "Kindly share your positive COVID-19 diagnosis report\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            },
            "provide_details_health_worker_last_death_letter": {
                "title": "Kindly share a photo of Police or Hospital Death Notification Letter indicating details of death\n\n0 Main Menu",
                "next": {
                    "1": "provide_details_health_worker_success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "IMAGE"
            }
        },
        "9": {
            "customer_verification_success_end_message": {
                "title": "We have successfully received your details. Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                "input_type": "END"
            },
            "end": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                "input_type": "END"
            },
            "health_worker_beneficiary_phone_no": {
                "title": "Kindly enter your beneficiary\'s phone number\n\n0 Main Menu",
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                },
                "input_type": "TEXT"
            },
            "provide_details_health_worker_success_end_message": {
                "title": "Kindly respond with: \n1. To complete Claim Submission\n\n0. Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            }
        },
        "10": {
            "end": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                "input_type": "END"
            },
            "success_end_message": {
                "title": "We have successfully received your claim notification.\nThank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0. Main Menu",
                "next": {
                    "0": "success_end_message"
                },
                "input_type": "END"
            },
        },

        "generic": {
            "response_options": {
                "next": {
                    "1": "provide_details",
                    "2": "request_callback",
                    "0": "back_to_main"
                }
            },
            "provide_details": {
                "next": {
                    "1": "success_end_message",
                    "0": "back_to_main"
                }
            },
            "success_end_message": {
                "title": "Thank you for the details provided, one of our call center representatives will contact you within 24 Hours.\n\n0 Main Menu",
                "next": {
                    "0": "back_to_main"
                },
                "input_type": "END"
            },
            "exit": {
                "title": "Thank You For the Details Provided, One of Our Call Center Agents Will Call You Within 24 Hours."
            },
            "covid_end_message": {
                "title": "A Link Has Sent To You Via SMS on"
            },
            "covid_update_message": {
                "title": "A Link Has Been Sent To You Via SMS on  \n0 Main Menu",
                "next": {
                    "0": "back_to_main",
                    "11": "exit"
                }
            },
            "covid_menu": {
                "title": "A Link Has Sent To You Via SMS on",
                "1": "How to Keep Healthy\n0 Main Menu",
                "2": "What To Do If You Feel Unwell\n0 Main Menu",
                "3": "How to Work From Home\n0 Main Menu",
                "4": "How to Study From Home\n0 Main Menu",
                "5": "How to Unwind From Home\n0 Main Menu",
                "6": "Ministry of Health Updates\n0 Main Menu",
                "0": "Back to Menu"
            },
            "insurance_class": [
                "Health Insurance",
                "Car Insurance",
                "Life Insurance",
                "Other Insurance Insurance"
            ],
            "end": {
                "title": "Thank You For Being a UAP Old Mutual Customer :) Click on This Link To View Our Online Platform https://www.uapoldmutual.com/",
                "input_type": "END"
            }
        }
    }
}
