'use strict';
let db = require('../models');
let menuContent = require('./MenuContent');
// var menus    = require(__dirname + '/./config/menu_content.json');
// var menus    = require(__dirname + '../config/menu_content.json')[''];
var fs = require('fs');
var path = require('path');
const ERR_GET_MENU = 'EGM';

function getMenu(unique_identifier, level) {
    try {
        /*let rawdata = fs.readFileSync(path.join(__dirname, '../config/menus.json'));
        // console.log('---- menu ' + JSON.stringify(JSON.parse(rawdata)[level][unique_identifier].title));
        return JSON.parse(rawdata)[level][unique_identifier].title;*/
        let rawdata = menuContent.getMenuContent();
        return rawdata[level][unique_identifier].title;
    } catch (e) {
        console.log('Error getMenu: ' + e)
    }
}

function getMenuByIndex(unique_identifier, level, index) {
    try {
        /*let rawdata = fs.readFileSync(path.join(__dirname, '../config/menus.json'));
        // console.log('---- menu ' + JSON.stringify(JSON.parse(rawdata)[level][unique_identifier].title));
        return JSON.parse(rawdata)[level][unique_identifier][index];*/
        let rawdata = menuContent.getMenuContent();
        return rawdata[level][unique_identifier][index];
    } catch (e) {
        console.log('Error getMenuByIndex: ' + e)
    }
}

function getMenuTags(unique_identifier, level) {
    try {
        /*let rawdata = fs.readFileSync(path.join(__dirname, '../config/menus.json'));
        // console.log('---- next ' + JSON.stringify(JSON.parse(rawdata)[level][unique_identifier].next));
        return JSON.parse(rawdata)[level][unique_identifier].next;*/
        let rawdata = menuContent.getMenuContent();
        return rawdata[level][unique_identifier].next;
    } catch (e) {
        console.log('Error getMenuTags: ' + e)

    }

}


function getErrorMsg(location) {
    return 'An error occurred please try again.\n Error code:' + ERR_GET_MENU + '_' + location;

}



module.exports = {
    getMenu: getMenu,
    getMenuTags: getMenuTags,
    getMenuByIndex: getMenuByIndex
};