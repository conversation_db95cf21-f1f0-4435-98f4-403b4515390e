var express = require('express');
var router = express.Router();
const path = require('path');
const motorController = require('../services/motor_ug/motor_ug_controller');

const DOWNLOAD_FOLDER = path.join(__dirname,"..", 'public/downloads');

router.get('/ping', function(req, res, next) {
    res.json('Hello from server')
});
router.use('/downloads', express.static(DOWNLOAD_FOLDER));

router.use("/motor", motorController);

module.exports = router;