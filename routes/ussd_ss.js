"use strict";
var express = require('express');
var menuService = require('../services/MenuService_ss');
var sessionService = require('../services/SessionService');
var selfservice = require('../services/SelfService-API');
var validateService = require('../services/ValidateInputService');

var router = express.Router();
let level_string = '';
const UssdMenu = require('ussd-menu-builder');
let menu = new UssdMenu();
const ussdRouter = require('ussd-router');
//let source_channel = 1, healthworker_request_category = 5;

/**
 * For the below order, refer to the menu order on menu content json file
* */
let menu_order = {
    register_claim: 1,
    policy_status: 2,
    buy_insurance: 3,
    //invest_with_us: 4,
    //bank_with_us: 5,
    make_insurance_payment: 4,
   // covid: 7
};
// Define ussd_menu.js states
menu.startState({

    run: () => {
        // use ussd_menu.js.con() to send response without terminating session
        menu.session.set('request_category', '')
        menu.session.set('line_of_business', '')
        menu.session.set('policy_reg_no', '')
        /*menu.session.set('bank_subcategory', '');

        menu.session.set('health_worker_name', '');
        menu.session.set('health_worker_id_number', '');
        menu.session.set('health_worker_license_number', '');
        menu.session.set('health_worker_profession', '');
        menu.session.set('health_worker_beneficiary_phone_no', '');
        menu.session.set('health_worker_beneficiary_name', '');
        menu.session.set('health_worker_facility', '');
        menu.session.set('health_worker_claim_type', '');*/
        menu.con(menuService.getMenu('start', '1'))
    },
    next: menuService.getMenuTags('start', '1')
});

/**
 * SECOND LEVEL MENUS
 * */

menu.state('register_claim', {
    run: () => {
        menu.session.set('request_category', menu_order.register_claim);
        menu.con(menuService.getMenu('register_claim', '2'))
    },
    next: menuService.getMenuTags('register_claim', '2')
});
menu.state('policy_status', {
    run: () => {

        menu.session.set('request_category', menu_order.policy_status);
        menu.con(menuService.getMenu('policy_status', '2'))
    },
    next: menuService.getMenuTags('policy_status', '2')
});
menu.state('buy_insurance', {
    run: () => {
        menu.session.set('request_category', menu_order.buy_insurance);
        menu.con(menuService.getMenu('buy_insurance', '2'))
    },
    next: menuService.getMenuTags('buy_insurance', '2')
});
menu.state('make_insurance_payment', {
    run: () => {
        menu.session.set('request_category', menu_order.make_insurance_payment);
        menu.con(menuService.getMenu('make_insurance_payment', '2'))
    },
    next: menuService.getMenuTags('make_insurance_payment', '2')
});
/*menu.state('invest_with_us', {
    run: () => {
        menu.session.set('request_category', menu_order.invest_with_us);
        // menu.go('success_end_message');
        menu.con(menuService.getMenu('invest_with_us', '2'))
        const resp = selfservice.API_Function(menu.args.phoneNumber, "", menu_order.invest_with_us, 1, "", "", 0, '');

    },
    next: menuService.getMenuTags('invest_with_us', '2')
});
menu.state('bank_with_us', {
    run: () => {
        menu.session.set('request_category', menu_order.bank_with_us);
        menu.con(menuService.getMenu('bank_with_us', '2'))
    },
    next: menuService.getMenuTags('bank_with_us', '2')
});
menu.state('covid', {
    run: () => {
        menu.session.set('request_category', menu_order.covid);
        menu.con(menuService.getMenu('covid', '2'))
    },
    next: menuService.getMenuTags('covid', '2')
});
menu.state('health_worker', {
    run: () => {
        menu.con(menuService.getMenu('health_worker', '2'))
    },
    next: menuService.getMenuTags('health_worker', '2')
});*/
/**
 * ---END SECOND LEVEL MENUS
 * */

// THIRD LEVEL MENUS

menu.state('claim_options', {
    run: () => {
        menu.session.set('line_of_business', level_string.charAt(level_string.length - 1));
        menu.con(menuService.getMenu('claim_options', '3'))
    },
    next: menuService.getMenuTags('claim_options', '3')
});
menu.state('policy_status_options', {
    run: () => {
        menu.session.set('line_of_business', level_string.charAt(level_string.length - 1));
        menu.con(menuService.getMenu('policy_status_options', '3'))
    },
    next: menuService.getMenuTags('policy_status_options', '3')
});
menu.state('buy_insurance_options', {
    run: () => {
        menu.session.set('line_of_business', level_string.charAt(level_string.length - 1));
        menu.go('success_end_message')
    },
    next: menuService.getMenuTags('buy_insurance_options', '3')
});
menu.state('make_insurance_payment_options', {
    run: () => {
        menu.session.set('line_of_business', level_string.charAt(level_string.length - 1));
        menu.con(menuService.getMenu('make_insurance_payment_options', '3'))
    },
    next: menuService.getMenuTags('make_insurance_payment_options', '3')

});
/*menu.state('health_worker_new_user', {
    run: () => {
        menu.con(menuService.getMenu('health_worker_new_user', '3'))
    },
    next: menuService.getMenuTags('health_worker_new_user', '3')

});

menu.state('bank_with_us_option', {
    run: () => {
        menu.session.set('bank_subcategory', (menu.args.text).charAt(level_string.length - 1));
        menu.go('success_end_message');
    }
});*/
menu.state('provide_details', {
    run: () => {
        let i = level_string.charAt(level_string.length - 3)
        console.log('---> Details: ' + i)
        if (i == 1)
            menu.con(menuService.getMenu('provide_details_health', '4'));
        else if (i == 2)
            menu.con(menuService.getMenu('provide_details_car', '4'));
        else if (i == 3)
            menu.con(menuService.getMenu('provide_details_life', '4'));
        else if (i == 4)
            menu.con(menuService.getMenu('provide_details_other', '4'));

    },
    next: {
        // using regex to match user input to next state
        '*\\w+': 'provide_details.policy_no'
    }
});
menu.state('make_insurance_payment_options.policy_no', {
    run: () => {
        try {
            menu.session.set('bank_subcategory', menu.val);
            menu.go('success_end_message')
        } catch (e) {
            menu.go('error');
            console.log('make_insurance_payment_options: User Entry Error: ' + e)
        }
    }
});
// 4 LEVEL MENUS
// todo clear duplicate registrations here
/*menu.state('health_worker_profession', {
    run: () => {

        selfservice.CheckIfHWUserExists(menu.args.phoneNumber, function (err, exists) {
            if (!err) {
                if (exists) {
                    let validation_error_msg = 'There is an existing account registered to this phone number.'

                    menu.con(validation_error_msg + '\n' + menuService.getMenu('health_worker', '2'))
                } else {
                    menu.con(menuService.getMenu('health_worker_profession', '3'))
                }
            } else {
                menu.go('error');
            }
        })
    },
    next: {
        'input': function () {
            selfservice.CheckIfHWUserExists(menu.args.phoneNumber, function (err, exists) {
                if (!err) {
                    if (exists) {
                        console.log('phone number exists + ' + menu.args.phoneNumber)
                        menuService.getMenuTags('health_worker', '2')
                    } else {
                        menuService.getMenuTags('health_worker_profession', '3')

                    }
                }
            })
        }
    }

});

menu.state('health_worker_register_claim', {
    run: () => {
        menu.con(menuService.getMenu('health_worker_register_claim', '3'))
    },
    next: menuService.getMenuTags('health_worker_register_claim', '3')

});
menu.state('health_worker_register_claim_type', {
    run: () => {
        menu.con(menuService.getMenu('health_worker_register_claim_type', '3'))
    },
    next: menuService.getMenuTags('health_worker_register_claim_type', '3')

});

// 4 LEVEL MENUS

menu.state('health_worker_name', {
    run: () => {
        menu.session.set('health_worker_profession', menu.args.text);
        menu.con(menuService.getMenu('health_worker_name', '4'))

    },
    next: menuService.getMenuTags('health_worker_name', '4')

});
menu.state('health_worker_register_claim_hosp', {
    run: () => {
        menu.session.set('health_worker_claim_type', menu.args.text)
        menu.con(menuService.getMenu('health_worker_register_claim_hosp', '4'))
    },
    next: menuService.getMenuTags('health_worker_register_claim_hosp', '4')

});
menu.state('health_worker_register_claim_last', {
    run: () => {
        menu.session.set('health_worker_claim_type', menu.args.text)
        menu.con(menuService.getMenu('health_worker_register_claim_last', '4'))
    },
    next: menuService.getMenuTags('health_worker_register_claim_last', '4')

});

// 5 LEVEL MENUShealth_worker_register_claim_hosp


menu.state('health_worker.name', {
    run: () => {
        menu.session.set('health_worker_name', menu.val);
        menu.con(menuService.getMenu('health_worker_national_id', '5'))
    },

    next: menuService.getMenuTags('health_worker_national_id', '5')

});

menu.state('health_worker.national_id', {
    run: () => {
        menu.session.set('health_worker_id_number', menu.val);
        menu.con(menuService.getMenu('health_worker_facility', '6'))
        /*let data = validateService.isValidIdOrPassportNo(menu.val)
        if (data.error) {
            menu.con('Invalid input\n' + menuService.getMenu('health_worker_national_id', '5'))
        } else {
            menu.session.set('health_worker_id_number', menu.val);
            menu.con(menuService.getMenu('health_worker_facility', '6'))
        }*/

    //},
    /*next:{
        'input': function(){
            if(validateService.isValidIdOrPassportNo(menu.val)){
                return   menuService.getMenuTags('health_worker_facility', '6');
            } else {
                return  menuService.getMenuTags('health_worker_national_id', '5');
            }
        }
    }*/
   // next: menuService.getMenuTags('health_worker_facility', '6')

//});
/*
menu.state('health_worker.facility', {
    run: () => {
        menu.session.set('health_worker_facility', menu.val);
        menu.con(menuService.getMenu('health_worker_reg_no', '7'))
    },
    next: menuService.getMenuTags('health_worker_reg_no', '7')

});

menu.state('health_worker.reg_no', {
    run: () => {
        menu.session.set('health_worker_license_number', menu.val);
        menu.con(menuService.getMenu('health_worker_beneficiary_name', '8'))
    },

    next: menuService.getMenuTags('health_worker_beneficiary_name', '8')

});

menu.state('health_worker.beneficiary_name', {
    run: () => {
        menu.session.set('health_worker_beneficiary_name', menu.val);
        menu.con(menuService.getMenu('health_worker_beneficiary_phone_no', '9'))
    },

    next: menuService.getMenuTags('health_worker_beneficiary_phone_no', '9')

});
menu.state('health_worker.beneficiary_phone_no', {
    run: () => {
        if (!(validateService.validatePhoneNo(menu.val))) {
            menu.con('Invalid input\n' + menuService.getMenu('health_worker_beneficiary_phone_no', '9'))
        } else {
            menu.session.set('health_worker_beneficiary_phone_no', menu.val);
            // menu.go('success_end_message')
            sendHealthWorkerDetails(menu.args.phoneNumber)
            menu.con(menuService.getMenu('success_end_message_hw_registration', 'generic'))

        }
    }

});
menu.state('health_worker_claim.policy_no', {
    run: () => {
        menu.session.set('health_worker_policy_no', menu.val);
        menu.go('success_end_message')
    }

});

menu.state('provide_details_health_worker_hosp_claim_id', {
    run: () => {
        menu.con(menuService.getMenu('provide_details_health_worker_hosp_claim_id', '5'))
    },
    next: menuService.getMenuTags('provide_details_health_worker_hosp_claim_id', '5')

});

menu.state('provide_details_health_worker_hosp.national_id', {
    run: () => {
        let data = validateService.isValidIdOrPassportNo(menu.val)
        if (data.error) {
            menu.con('Invalid input\n' + menuService.getMenu('provide_details_health_worker_hosp_claim_id', '5'))
        } else {
            // console.log('==> HW HOSP ussd claim:')
            menu.session.set('health_worker_policy_no', menu.val)
            let claim_type = 1
            const resp = selfservice.API_Function(menu.args.phoneNumber, undefined, healthworker_request_category, source_channel, menu.val, undefined, 1, claim_type);
            menu.con(menuService.getMenu('success_end_message_hw_claim', 'generic'));

            // todo end session
        }
    },
    next: menuService.getMenuTags('provide_details_health_worker_hosp_claim_id', '5')

});

menu.state('provide_details_health_worker_last_claim_id', {
    run: () => {
        menu.con(menuService.getMenu('provide_details_health_worker_last_claim_id', '5'))
    },
    next: menuService.getMenuTags('provide_details_health_worker_last_claim_id', '5')

});

menu.state('provide_details_health_worker_last.national_id', {
    run: () => {
        let data = validateService.isValidIdOrPassportNo(menu.val)
        if (data.error) {
            menu.con('Invalid input\n' + menuService.getMenu('provide_details_health_worker_last_claim_id', '5'))
        } else {
            console.log('==> HW LAST ussd claim:')
            menu.session.set('health_worker_policy_no', menu.val)
            let claim_type = 2
            selfservice.API_Function(menu.args.phoneNumber, undefined, healthworker_request_category, source_channel, menu.val, undefined, 1, claim_type);
            menu.con(menuService.getMenu('success_end_message_hw_claim', 'generic'));
        }
    },
    next: menuService.getMenuTags('provide_details_health_worker_last_claim_id', '5')

});*/


// GENERIC STEPS

menu.state('response_options', {
    run: () => {
        menu.con(menuService.getMenu('response_options', 'generic'));
    },
    next: {
        // using regex to match user input to next state
        '*\\s+': 'provide_details.policy_no'
    }
});
menu.state('provide_details.policy_no', {
    run: () => {
        try {
            menu.session.set('policy_reg_no', menu.val);
            menu.go('success_end_message')
        } catch (e) {
            menu.go('error');
            console.log('provide_details: User Entry Error: ' + e)
        }
    }
});


menu.state('request_callback', {
    run: () => {
        //invoke api
        // menu.end(menuService.getMenu('success_end_message', 'generic'));
        menu.go('success_end_message')
    }
});
/*menu.state('covid_update_message', {
    run: () => {
        // todo call SMS API
        let i = level_string.charAt(level_string.length - 1)
        console.log('-----> TO SEND COVID...' + menu.args.phoneNumber + ' Option: ' + i);

        menu.con(menuService.getMenu('covid_menu', 'generic') + ' ' + menuService.getMenuByIndex('covid_menu', 'generic', i));
        const resp = selfservice.SMS_Function(menu.args.phoneNumber, i);
    }
});*/
menu.state('success_end_message', {
    run: () => {
        //invoke api
        try {
            let _line_of_business = '',
                _request_category = '',
                _policy_reg_no = '',
                _bank_subcategory = '';

            menu.session.get('line_of_business')
                .then(line_of_business => {
                    _line_of_business = line_of_business;
                });
            menu.session.get('request_category')
                .then(request_category => {
                    _request_category = request_category;
                });
            menu.session.get('policy_reg_no')
                .then(policy_reg_no => {
                    _policy_reg_no = policy_reg_no;
                });
            menu.session.get('bank_subcategory')
                .then(bank_subcategory => {
                    _bank_subcategory = bank_subcategory;
                    console.log('-----> TO SEND...' + menu.args.phoneNumber + ' =_line_of_business= ' + _line_of_business + ' =_request_category= ' + _request_category + ' =_policy_reg_no= ' + _policy_reg_no + ' =bank_subcategory= ' + _bank_subcategory)
                    const resp = selfservice.API_Function(menu.args.phoneNumber, _line_of_business, _request_category, 1, _policy_reg_no, bank_subcategory, 0);
                    menu.con(menuService.getMenu('success_end_message', 'generic'));

                })
        } catch (e) {
            menu.go('error');
            console.log('Path Error: ' + e)
        }


    }

});


menu.state('back_to_main', {
    run: () => {
        menu.goStart();
    }
});

menu.state('exit', {
    run: () => {
        menu.end();
    }
});

menu.state('error', {
    // handle errors
    run: () => {
        menu.end('An error occurred processing your request, please try again.')
    }
});

let sessions = {};
menu.sessionConfig({
    start: function (sessionId, key) {
        return new Promise((resolve, reject) => {
            if (!(sessionId in sessions)) sessions[sessionId] = {};
            resolve();
        });
    },
    set: (sessionId, key, value, callback) => {
        // store key-value pair in current session
        return new Promise((resolve, reject) => {
            sessions[sessionId][key] = value;
            callback();
        });
    },
    get: function (sessionId, key) {
        return new Promise((resolve, reject) => {
            let value = sessions[sessionId][key];
            resolve(value);
        });
    },
    end: function (sessionId, callback) {
        // clear current session
        // this is called by menu.end()
        return new Promise((resolve, reject) => {
            delete sessions[sessionId];
            callback();
        });
    }
});

/*function sendHealthWorkerDetails(phone_no) {

    let health_worker_name = '',
        health_worker_profession = '',
        health_worker_id_number = '',
        health_worker_license_number = '',
        health_worker_beneficiary_name = '',
        health_worker_facility = '',
        health_worker_beneficiary_phone_no = '';

    menu.session.get('health_worker_profession')
        .then(_health_worker_profession => {
            health_worker_profession = _health_worker_profession;
        });
    menu.session.get('health_worker_name')
        .then(_health_worker_name => {
            health_worker_name = _health_worker_name;
        });
    menu.session.get('health_worker_id_number')
        .then(_health_worker_id_number => {
            health_worker_id_number = _health_worker_id_number;
        });
    menu.session.get('health_worker_license_number')
        .then(_health_worker_license_number => {
            health_worker_license_number = _health_worker_license_number;
        });
    menu.session.get('health_worker_beneficiary_phone_no')
        .then(_health_worker_beneficiary_phone_no => {
            health_worker_beneficiary_phone_no = _health_worker_beneficiary_phone_no;
        });
    menu.session.get('health_worker_beneficiary_name')
        .then(_health_worker_beneficiary_name => {
            health_worker_beneficiary_name = _health_worker_beneficiary_name;
        });
    menu.session.get('health_worker_facility')
        .then(_health_worker_facility => {
            health_worker_facility = _health_worker_facility;


            const options = {
                'method': 'POST',
                'url': 'https://uapom.uapoldmutual.com/selfservice/apps/reghealthworkers',
                'headers': {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                form: {
                    'name': health_worker_name,
                    'phone_number': phone_no,
                    'id_number': health_worker_id_number,
                    'license_number': health_worker_license_number,
                    'profession': health_worker_profession,
                    'beneficiary_phone_number': health_worker_beneficiary_phone_no,
                    'email': '',
                    'beneficiary_name': health_worker_beneficiary_name,
                    'facility': health_worker_facility,
                    'source_channel': source_channel
                }
            };
            console.log('HW data: ' + JSON.stringify(options.form))
            /* request(options, function (error, response) {
                 if (error) throw new Error(error);
                 console.log("Our response body => " + response.body);

                 const json_response = JSON.parse(response.body);

                 console.log("Response => " + json_response);

                 menu.con(json_response);
                 console.log('Sent Health Worker details!')

             });*/

            //menu.con(menuService.getMenu('success_end_message', 'generic'));

       // })


//}

router.post('/', (req, res) => {

    // console.log("args: ------>");
    let args = {
        phoneNumber: req.body.phoneNumber,
        sessionId: req.body.sessionId,
        serviceCode: req.body.serviceCode,
        text: req.body.text
    };

    sessionService.updateSession(args)
    // level_string = req.body.text;
    console.log("args before: " + JSON.stringify(req.body));
    args.text = ussdRouter.ussdRouter(req.body.text)
    level_string = args.text;
    console.log("args: " + JSON.stringify(args) + ' level_string: ' + level_string);
    try {
        menu.run(args, resMsg => {
            // console.log("response: " + resMsg);
            res.send(resMsg);
        });
    } catch (e) {
        menu.go('error');
        console.log('Menu Error: ' + e)

    }

});

module.exports = router;
