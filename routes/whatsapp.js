var express = require('express');
var router = express.Router();
const util = require('util');
const jwt = require('jsonwebtoken');
const passportJWT = require('passport-jwt');

let newWhatsAppServiceUG = require('../services/ug/NewWhatsAppService');
let newWhatsAppServiceRW = require('../services/rw/NewWhatsAppService');
let newWhatsAppService = require('../services/NewWhatsAppService');

let menuService = require('../services/WhatsAppMenuService');
let mongoService = require('../services/MongoService');
var whatsappDBService = require('../services/WhatsappDBService');
var whatsappDBServiceUG = require('../services/ug/WhatsappDBService');
var whatsappDBServiceRW = require('../services/rw/WhatsappDBService');
var investAPIService = require('../services/ke/InvestAPIService');



var selfservice_api = require('../services/SelfService-API');

let whatsAppService = require('../services/WhatsAppService');
let mailService = require('../services/MailService');
let dateFormat = require('dateformat');
let auth = require('basic-auth')
var compare = require('tsscmp')


let jwtOptions = {};
let ExtractJwt = passportJWT.ExtractJwt;
jwtOptions.jwtFromRequest = ExtractJwt.fromAuthHeaderAsBearerToken();
jwtOptions.secretOrKey = 'jurVqG2DXc4UA522';

let whiteRwListNumbers = [
    "250785282685",
    "250782261782",
    "250788539201",
    "250784856700",
    "250788567977",
    "250788316021",
    "250785991560",
    "250786932913",
    "250785516057",
    "250788591106",
    "250784300589",
    "250788330781",
    "250782751001",
    "250728242242",
    "250784546653",
    "250788465903",
    "250788738002",
    "27727530288",
    "254721596767",
    "254722903377"
]
let whiteUgListNumber = ["256776179185",
    "256782881392",
    "256783552554",
    "256782643533",
    "256787925244",
    "256774009823",
    "256775192679",
    "256783173364",
    "256772191937",
    "256789229961",
    "256781587028",
    "256772835395",
    "256779280801",
    "256775915089",
    "256775090546",
    "256702543448",
    "256786125683",
    "256772388296",
    "256701445537",
    "256704865662",
    "256751438244",
    "256782028766",
    "256774258983",
    "256787142288",
    "256779132688",
    "256713932765",
    "256772565876",
    "256705422704",
    "256774292166",
    "256759526881",
    "256752620035",
    "256784508750",
    "256702225024",
    "254721596767",
    "254721596769"
]


/* GET users listing. */
router.get('/', function(req, res, next) {
    let now = new Date();
    res.json({
        error: false,
        msg: dateFormat(now, "isoDateTime"),
    })
});
/*router.get('/mail', function (req, res, next) {
    mailService.main()
});*/

router.get('/auth', function(req, res, next) {
    var credentials = auth(req)

    console.log('Cred: ' + credentials.name + ' ' + credentials.pass)
    if (!credentials || !check(credentials.name, credentials.pass)) {
        res.statusCode = 401
        res.setHeader('WWW-Authenticate', 'Basic realm="example"')
        res.end('Access denied')
    } else {
        console.log('JSnO ' + JSON.stringify(req.query))
        if ('ticket_no' in req.query)
            selfservice_api.ticket_media(req.query.ticket_no, function(err, resp) {
                if (err) {
                    console.log("Error => " + err);
                    res.end('Error getting ticket media details')
                } else {
                    res.json(resp);
                }

            });
        else
            res.end('Invalid request')
    }

});

// Basic function to validate credentials for example
function check(name, pass) {
    var valid = true

    // Simple method to prevent short-circut and use timing-safe compare
    valid = compare(name, 'john') && valid
    valid = compare(pass, 'secret') && valid

    return valid
}

/*router.post('/', function (req, res, next) {
    let content = req.body.results[0];

    console.log("*** WhatsApp message: " + content.message.text)
    if (content.message.text.toLowerCase() === 'menu') {
        whatsAppService.getMainMenu(content.from, content.message.text, function (err, resp) {
            if (err) {

            }
            else {
                let menu_resp = 'Hi ' + content.contact.name + '\n\n' + resp
                res.json(menu_resp)
                /!*whatsAppService.sendWhatsAppMessage(source_country,content.from, menu_resp ,function (err, resp) {
                    if (err) {

                    } else {
                        res.json(resp)
                    }
                })*!/

                // console.log('Message sent!' + menu_resp);
            }
        })

    } else
        whatsAppService.getSessionLevel(content.from, content.message.text, function (err, resp) {
            if (err) {

            } else {
                console.log('Message sent!');
                res.json(resp)
                 /!*whatsAppService.sendWhatsAppMessage(source_country,content.from, resp ,function (err, resp) {
                     if (err) {

                     } else {
                         res.json(resp)
                     }
                 })*!/
            }
        });


    /!*res.json({
        error: false,
        msg: req.body
    })*!/
})
;*/
router.post('/', function(req, res, next) {
    let content = req.body.results[0];
    let message_type = content.message.type;
    let _input = req.body.results[0].message.text
    req.body.results[0].message.text = _input.replace('ke ', '').toLowerCase()
    let params = {
        phone_no: content.from,
        message_id: content.messageId,
        wa_created_at: content.receivedAt,
        request_count: 1
    }

    let allow = true
    whatsappDBService.isRequestExisting(params, function(exists) {
        if (allow || !exists) {
            switch (message_type) {
                case 'TEXT':
                    {
                        newWhatsAppService.handleUserText(req, res);
                        break;
                    }

                case 'IMAGE':
                    newWhatsAppService.handleUserImages(req, res)
                    break;

                case 'VIDEO':
                    newWhatsAppService.handleUserVideos(req, res)
                    break;

                case 'DOCUMENT':
                    newWhatsAppService.handleUserDocuments(req, res)
                    break;

                case 'LOCATION':
                    break;

                case 'INTERACTIVE_BUTTON_REPLY':
                    newWhatsAppService.handleButtonEvent(req, res)
                    console.log('getting button event')
                    break;

            }
        } else
            res.json(exists)
    })

});
router.post('/:country_code', function(req, res, next) {
    let country = req.params.country_code
    console.log(`country ${country}`)
    // if (country == 'ug') {
        // whatsappDBService = whatsappDBServiceUG
        // newWhatsAppService = newWhatsAppServiceUG
    // } else if (country == 'rw') {
    //     whatsappDBService = whatsappDBServiceRW
    //     newWhatsAppService = newWhatsAppServiceRW
    // }
    let content = req.body.results[0];
    let message_type = content.message.type;
    // console.log('Message Type: ' + message_type);
    let params = {
        phone_no: content.from,
        message_id: content.messageId,
        wa_created_at: content.receivedAt,
        request_count: 1
    }
    let allow = false
        //if (whiteRwListNumbers.includes(content.from) || whiteUgListNumber.includes(content.from)) {
    whatsappDBService.isRequestExisting(params, function(exists) {
            console.log('Existr: ' + exists)
            console.log('Message type: ', message_type)
            if (allow || !exists)
                switch (message_type) {
                    case 'TEXT':
                        newWhatsAppServiceUG.handleUserText(req, res, req.params.country_code);
                        break;

                    case 'IMAGE':
                        newWhatsAppServiceUG.handleUserImages(req, res, req.params.country_code)
                        break;

                    case 'VIDEO':
                        newWhatsAppServiceUG.handleUserVideos(req, res, req.params.country_code)
                        break;

                    case 'DOCUMENT':
                        newWhatsAppServiceUG.handleUserDocuments(req, res, req.params.country_code)
                        break;

                    case 'LOCATION':
                        break;
                    case 'INTERACTIVE_BUTTON_REPLY':
                        newWhatsAppServiceUG.handleButtonEvent(req, res)
                        console.log('getting button event')
                        break;

                }
            else
                res.json(exists)
        })
        // } else {
        //     res.json(true)
        // }

});
router.post('/test', function(req, res, next) {
    whatsappDBService.updateUserSession(req.body, function(err, msg) {
        if (err)
            res.json('Error')
        else
            res.json(msg)
    })
});
router.post('/mail', function(req, res, next) {
    console.log('BOdy: ' + JSON.stringify(req.body))
    mailService.sendMail(req.body, function(err, msg) {
        if (err)
            res.json(msg)
        else
            res.json('Mail sent')
    })
});
/*router.post('/requests', function (req, res, next) {
    let content = req.body.results[0];
    let params = {
        phone_no: content.from,
        message_id: content.messageId,
        wa_created_at: content.receivedAt,
        request_count: 1
    }
    whatsappDBService.isRequestExisting(params, function (exists) {
        res.json(exists)
    })
});*/
router.post('/mongo', function(req, res, next) {
    console.log('Body ' + req.body.phone_no + ' - ' + req.body)
    mongoService.connection(function(err, resp) {
        if (err) {

        } else {
            res.json(resp)
        }
    })

});


router.get('/ticket_media', function(req, res) {

    selfservice_api.ticket_media(req.query.ticket_no, function(err, resp) {
        if (err) {
            console.log("Error => " + err);
            res.json("An error occurred getting ticket media " + err);
        } else {
            res.json(resp);
        }

    });

});


router.get('/registration_media', function(req, res) {
    selfservice_api.registration_media(req.query.phone_no, function(err, resp) {
        if (err) {
            console.log("Error => " + err);
            res.json("An error occurred getting ticket media " + err);
        } else {
            res.json(resp);
        }

    });

});


router.post('/receiver', function(req, res, next) {
    let content = req.body.results[0];
    console.log("*** WhatsApp message: " + content.message.text)
    if (content.message.text.toLowerCase() === 'menu') {
        whatsAppService.getMainMenu(content.from, content.message.text, function(err, resp) {
            if (err) {

            } else {
                let menu_resp = 'Hi ' + content.contact.name + '\n\n' + resp
                res.json(menu_resp)
                    /*whatsAppService.sendWhatsAppMessage(source_country,content.from, menu_resp ,function (err, resp) {
                        if (err) {

                        } else {
                            res.json(resp)
                        }
                    })*/

                // console.log('Message sent!' + menu_resp);
            }
        })

    } else
        whatsAppService.getSessionLevel(content.from, content.message.text, function(err, resp) {
            if (err) {

            } else {
                console.log('Message sent!');
                res.json(resp)
                    /*whatsAppService.sendWhatsAppMessage(source_country,content.from, resp ,function (err, resp) {
                        if (err) {

                        } else {
                            res.json(resp)
                        }
                    })*/
            }
        });


    /*res.json({
        error: false,
        msg: req.body
    })*/
});

router.get('/confirm_otp/:phonenumber', function(req, res) {

    var phonenumber = req.params.phonenumber;
    let payload = {
        phonenumber: phonenumber
    };
    const JWT_EXPIRY_SECS = 84600;

    var token = jwt.sign(payload, jwtOptions.secretOrKey, {
        expiresIn: JWT_EXPIRY_SECS // expires in 24 hours
    });
    res.render('confirm-otp', { url: req.baseUrl + '/confirm_otp_success', token: token, phonenumber: phonenumber });
});


router.post('/confirm_otp_success', async function(req, res) {
    let token = req.body.token;
    let otp = req.body.entered_otp;
    let pin = req.body.confirm_pin;
    let msisdn = req.body.phonenumber;

    let argsOTP = {
        onetime_pin: otp,
        msisdn: msisdn
    }

    let argsPin = {
        pin: pin,
        confirm_pin: pin,
        msisdn: msisdn
    }


    let message = '';
    jwt.verify(token, jwtOptions.secretOrKey, async function(err, decoded) {
        if (err) {
            message = 'Token invalid or expired';
        }
        let validateOneTimePin = await investAPIService.validateOneTimePin(argsOTP);
        message = validateOneTimePin['message'];
        //if(validateOneTimePin['success']){

        //let createPin=await investAPIService.createPin(argsPin);

        //if(createPin){
        message = validateOneTimePin['message'];
        //}
        //}

        res.render('confirm-otp-success', { message: message });
    });
});

router.get('/login', function(req, res) {
    res.render('login', { url: req.baseUrl + '/login_success', token: 'token', message: '' });
});

router.get('/login_success', function(req, res) {
    res.render('login-success', { message: 'url' });
});


function handleUGMenu(req, res) {
    console.log(`handleUGMenu`)
    let content = req.body.results[0];
    let message_type = content.message.type;
    let params = {
        phone_no: content.from,
        message_id: content.messageId,
        wa_created_at: content.receivedAt,
        request_count: 1
    }
    let allow = true

    whatsappDBService_ug.isRequestExisting(params, function(exists) {
        if (allow || !exists)
            switch (message_type) {
                case 'TEXT':
                    newWhatsAppService_ug.handleUserText(req, res, req.params.country_code);
                    break;

                case 'IMAGE':
                    newWhatsAppService_ug.handleUserImages(req, res, req.params.country_code)
                    break;

                case 'VIDEO':
                    newWhatsAppService_ug.handleUserVideos(req, res, req.params.country_code)
                    break;

                case 'DOCUMENT':
                    newWhatsAppService_ug.handleUserDocuments(req, res, req.params.country_code)
                    break;

                case 'LOCATION':
                    break;

            }
        else
            res.json(exists)
    })
}


function getUgandawhiteList() {


    return whiteListNumber

}

function getRwandaWhiteList() {
    let whiteListNumbers = [
        "250785282685",
        "250782261782",
        "250788539201",
        "250784856700",
        "250788567977",
        "250788316021",
        "250785991560",
        "250786932913",
        "250785516057",
        "250788591106",
        "250784300589",
        "250788330781",
        "250782751001",
        "250728242242",
        "250784546653",
        "250788465903",
        "250788738002"
    ]

    return whiteListNumbers
}
module.exports = router;
