"use strict";
var express = require('express');
const axios = require('axios');
const jwt = require('jsonwebtoken');
let bcrypt = require('bcrypt');
const passport = require('passport');
const passportJWT = require('passport-jwt');
const JWT_EXPIRY_SECS = 84600;
var menuService = require('../../services/ng/ussd/MenuPickerService.js');
var identityService = require('../../services/ng/IdentityService.js');
var utilsService = require('../../services/Utils.js');
var ProductSaleService = require('../../services/ng/ProductSaleService.js');
var SessionService = require('../../services/ng/SessionService.js');
var ScheduleService = require('../../services/ng/ScheduleService.js');
var DbService = require('../../services/ng/DbService.js');
const sendSMs = require('../../services/ng/ussd/NigSmsApi.js');

var FlutterWaveService = require('../../services/ng/FlutterWaveService');

const dotenv = require("dotenv");
dotenv.config();


const {FLUTTERWAVE_URL, FLUTTERWAVE_SECRET_KEY} = process.env;

let ExtractJwt = passportJWT.ExtractJwt;

var router = express.Router();
let level_string = '';
const UssdMenu = require('ussd-menu-builder');
let menu = new UssdMenu();
const ussdRouter = require('ussd-router');
let AccessUser = require('../../models/access_users.js');
let db = require('../../models');
let identity = require('../../middleware/ng/UserIdentityService');
const {response} = require('express');
var datapaymt = [];


let jwtOptions = {};
jwtOptions.jwtFromRequest = ExtractJwt.fromAuthHeaderAsBearerToken();
jwtOptions.secretOrKey = 'jurVqG2DXc4UA522';

menu.startState({
    run: () => {
        datapaymt = [];

        menu.session.set('ref', '');
        menu.session.set('firstname', '');
        menu.session.set('lastname', '');
        menu.session.set('fullname', '');
        menu.session.set('user_id', '');
        menu.session.set('session_id', '');
        menu.session.set('policy_reference_number', '');
        menu.session.set('product_option', '');
        menu.session.set('product_name', '');

        identityService.getUserDetails(menu.args.phoneNumber, function (err, userData) {

            console.log('Doesn\'t exist: ' + JSON.stringify(userData));
            if (err && userData.system_error) {
                menu.end('An error occurred executing your request, please try again.');
                console.log('System error: ' + userData.system_error)
            }

            else if (err && !userData.user_exists) {
                ScheduleService.initializeScheduledJob();
                menu.con(menuService.getMenu('user_new', '1'))
                SessionService.saveSession({
                    user_id: userData.user_id,
                    channel_id: 1,
                    path_taken: menu.args.serviceCode + '' + menu.args.text,
                    session_id: menu.args.sessionId
                }, function (err, session_response) {
                    if (err) {
                        console.log('Session error: ' + session_response.system_error)
                    } else {
                        menu.session.set('session_id', session_response.id);
                    }
                })
            }
            else {
                console.log('User found: ' + userData.full_name);
                menu.session.set('ref', '');
                menu.session.set('firstname', userData.first_name);
                menu.session.set('lastname', userData.last_name);
                menu.session.set('fullname', userData.full_name);
                menu.session.set('user_id', userData.user_id);
                menu.session.set('policy_reference_number', '');
                menu.session.set('product_option', '');
                menu.session.set('product_name', '');
                menu.con(utilsService.parameterizedString(menuService.getMenu('user_exists', '1'), userData.full_name))
                SessionService.saveSession({
                    user_id: userData.user_id,
                    channel_id: 1,
                    path_taken: menu.args.serviceCode + '' + menu.args.text,
                    session_id: menu.args.sessionId
                }, function (err, session_response) {
                    if (err) {
                        console.log('Session error: ' + session_response.system_error)
                    } else {
                        menu.session.set('session_id', session_response.id);
                    }
                })

            }
        })


    },
    next: {
        "*\\w+": function (callback) {
            identityService.getUserDetails(menu.args.phoneNumber, function (err, userData) {
                if (err && !userData.user_exists) {
                    callback('welcome.national_id')
                }
                else {
                    callback('entry.pin')
                }
            })
        }
    }


});


function getUserStatus() {
    // return menuService.getMenuTags('user_new', '1')
    let phone_no;
    try {
        phone_no = menu.args.phoneNumber
    } catch (e) {
        phone_no = '08000000000'
    }
    let user_status = identityService.getUserDetails(phone_no, function (err, userData) {

    });
    if (user_status) {
        let ss = menuService.getMenuTags('user_exists', '1');
        console.log('ss: ' + ss);
        return ss
    }
    else {
        let ss = menuService.getMenuTags('user_new', '1');
        console.log('sss: ' + ss);
        return ss;
    }

    /* if (err && userData.system_error) {
         return 'user_new'
     }

     else if (err && !userData.user_exists)
         return 'user_new'
     else {
         return 'user_exists'
     }*/
}

menu.state('welcome.national_id', {

    run: () => {
        menu.con(menuService.getMenu('first_name', '1'));
        menu.session.set('ref', menu.val)
        // getKYCDetails()
    },
    next: menuService.getMenuTags('first_name', '1')
    // next: menuService.getMenuTags('welcome', '2')
});
menu.state('entry.first_name', {

    run: () => {
        menu.con(menuService.getMenu('last_name', '1'));
        menu.session.set('firstname', menu.val)
    },
    next: menuService.getMenuTags('last_name', '1')
});
menu.state('entry.last_name', {

    run: () => {

        getKYCDetails(menu.val)

    },
    next: menuService.getMenuTags('welcome', '2')
});
/*menu.state('entry.pin', {
    run: () => {
        menu.con(menuService.getMenu('choose_service', '2'))
    },
    next: menuService.getMenuTags('choose_service', '2')
});*/
menu.state('entry.pin', {
    run: () => {
        menu.session.get('user_id').then(user_id => {
            identityService.loginUser(user_id, menu.val, function (err, resp) {
                if (err) {
                    menu.con(menuService.getMenu('wrong_pin', '2'))
                }
                else {
                    menu.con(menuService.getMenu('choose_service', '2'))
                }
            })
        })

    },
    next: menuService.getMenuTags('choose_service', '2')
});
menu.state('enter_pin.code', {
    run: () => {
        menu.session.set('pin', menu.val);
        menu.con(menuService.getMenu('re_enter_pin', '3'))
    },
    next: menuService.getMenuTags('re_enter_pin', '3')
});
menu.state('re_enter_pin.code', {
    run: () => {
        menu.session.get('pin').then(pin => {
            if (pin === menu.val) {
                menu.session.get('user_onboard_data').then(params => {
                    console.log('Onboard_data: ' + JSON.stringify(params));
                    //sending sms notification for account creation
                    let message = 'Congratulations! You have opened your MyOldMutual profile. To access the profile dial *1045#'
                    identityService.createUser(params, function (err, response) {
                        if (err) {
                            menu.end('An error occurred executing your request, please try again.');
                            console.log('createUser: ' + JSON.stringify(response));
                        }
                        else {
                            params.user_id = response.user_id;
                            console.log('params user: ' + JSON.stringify(params));
                            identityService.createUserPIN(params, pin, function (err, response) {
                                //sending sms notification
                                sendSMs.sendPaymentSms(menu.args.phoneNumber, message);
                                menu.con(utilsService.parameterizedString(menuService.getMenu('user_exists', '1'), params.full_name));
                            })
                        }


                    })
                })

            }
            else
                menu.con(menuService.getMenu('pin_mismatch', 'generic'))
        })
    },
    next: menuService.getMenuTags('user_exists', '1')
});
menu.state('car_licence_no', {
    run: () => {

        menu.con(menuService.getMenu('car_licence_no', '4'))
    },
    next: menuService.getMenuTags('car_licence_no', '4')
});
menu.state('entry.car_reg_no', {
    run: () => {
        menu.session.set('policy_reference_number', menu.val); //fetching car reg no
        menu.con(menuService.getMenu('vehicle_type', '4'))
    },
    next: menuService.getMenuTags('vehicle_type', '4')
});
/*menu.state('entry.car_reg_no', {
    run: () => {
        menu.session.set('car_reg_no', menu.val); //fetching car reg no
        menu.con(menuService.getMenu('vehicle_type', '4'));
        datapaymt.push(menu.val)
    },
    next: menuService.getMenuTags('vehicle_type', '4')
});*/
menu.state('entry.invest_beneficiary_name', {
    run: async () => {
        // menu.con(menuService.getMenu('invest_pay_option', '5'))

        menu.con(utilsService.parameterizedString(menuService.getMenu('invest_pay_option', '5'), await menu.session.get('product_option'), await menu.session.get('amount')))
        // menu.con(menuService.getMenu('confirm_travel_cover', '6'))
        menu.session.set('policy_details', 'Beneficiary: ' + menu.val);
    },
    next: menuService.getMenuTags('invest_pay_option', '5')
});
/*menu.state('welcome', {
    run: () => {

        menu.con(menuService.getMenu('confirm_details_yes', '3'))

    },
    next: menuService.getMenuTags('confirm_details_yes', '3')
});*/
menu.state('confirm_details_no', {
    run: () => {
        menu.con(menuService.getMenu('confirm_details_no', '3'))
    },
    next: menuService.getMenuTags('confirm_details_no', '3')
});
menu.state('confirm_details_yes', {
    run: () => {
        menu.con(menuService.getMenu('confirm_details_yes', '3'))
    },
    next: menuService.getMenuTags('confirm_details_yes', '3')
});
menu.state('insure', {
    run: () => {
        menu.con(menuService.getMenu('insure', '3'))
        menu.session.set('product_option', 'Insure');
    },
    next: menuService.getMenuTags('insure', '3')
});
menu.state('invest', {
    run: () => {
        menu.con(menuService.getMenu('invest', '3'))
        menu.session.set('product_option', 'Invest');
    },
    next: menuService.getMenuTags('invest', '3')
});
menu.state('my_portfolio', {
    run: () => {
        menu.con(menuService.getMenu('my_portfolio', '3'))
        menu.session.set('product_option', 'My Portfolio');
    },
    next: menuService.getMenuTags('my_portfolio', '3')
});
menu.state('third_party_cover', {
    run: async () => {
        menu.con(menuService.getMenu('third_party_cover', '4'))
        let menu_data = menuService.getMenuData('insure', '3')

        let product = menu_data.product_data[menu.val - 1].name
        menu.session.set('product_option', await menu.session.get('product_option') + ' - ' + product);
        console.log('third_party_cover Prdocut data: ' + await menu.session.get('product_option'))
    },
    next: menuService.getMenuTags('third_party_cover', '4')
});
menu.state('travel_sure', {
    run: async () => {
        menu.con(menuService.getMenu('travel_sure', '4'))
        let menu_data = menuService.getMenuData('insure', '3')

        let product = menu_data.product_data[menu.val - 1].name
        menu.session.set('product_option', await menu.session.get('product_option') + ' - ' + product);
        console.log('travel_sure Prdocut data: ' + await menu.session.get('product_option'))
    },
    next: menuService.getMenuTags('travel_sure', '4')
});
menu.state('travel_duration', {
    run: async () => {
        menu.con(menuService.getMenu('travel_duration', '5'))

        let menu_data = menuService.getMenuData('travel_sure', '4')

        let location = menu_data.product_data[menu.val - 1].name

        menu.session.set('product_option', await menu.session.get('product_option') + ' - ' + location);
        console.log('Prdocut data: ' + await menu.session.get('product_option'))

    },
    next: menuService.getMenuTags('travel_duration', '5')
});
menu.state('vehicle_type_selection', {
    run: async () => {
        // menu.con(menuService.getMenu('vehicle_type_selection', '5'))

        // menu.con(utilsService.parameterizedString(menuService.getMenu('vehicle_type_selection', '5'), await menu.session.get('product_option'), await menu.session.get('amount')))

        console.log('vehicle_type_selection: ' + menu.val)
        let menu_data = menuService.getMenuData('vehicle_type', '4')

        let amount = menu_data.product_data[menu.val - 1].amount
        let product = menu_data.product_data[menu.val - 1].name

        menu.con(utilsService.parameterizedString(menuService.getMenu('vehicle_type_selection', '5'), amount, await menu.session.get('policy_reference_number')))

        menu.session.set('amount', amount);
        menu.session.set('product_option', await menu.session.get('product_option') + ' - ' + product);

        console.log('Prdocut data: ' + await menu.session.get('product_option'))
    },
    next: menuService.getMenuTags('vehicle_type_selection', '5')
});
menu.state('travel_beneficiary_name', {
    run: async () => {
        let menu_data = await menuService.getMenuData('travel_duration', '5')
        let amount = await menu_data.product_data[menu.val - 1].amount
        let product = await menu_data.product_data[menu.val - 1].name
        menu.session.set('amount', amount);
        menu.session.set('product_option', await menu.session.get('product_option') + ' - ' + product);
        menu.con(menuService.getMenu('travel_beneficiary_name', '6'))

        console.log('Prdocut data travel_beneficiary_name: ' + await menu.session.get('product_option'))
        console.log('Prdocut data travel_beneficiary_amt: ' + await menu.session.get('amount'))

    },
    next: menuService.getMenuTags('travel_beneficiary_name', '6')
});
menu.state('entry.beneficiary_name', {
    run: async () => {

        menu.con(utilsService.parameterizedString(menuService.getMenu('confirm_travel_cover', '6'), await menu.session.get('product_option'), await menu.session.get('amount')))
        // menu.con(menuService.getMenu('confirm_travel_cover', '6'))
        menu.session.set('policy_details', 'Beneficiary: ' + menu.val);
    },
    next: menuService.getMenuTags('confirm_travel_cover', '6')
});
menu.state('make_payment', {
    run: () => {

        menuService.getMenuFromDb(function (err, response) {
            if (err) {
                menu.con(response.user_message)
            } else {
                menu.con(response)
            }

        })
    },
    next: menuService.getMenuTags('make_payment', 'generic')
});
menu.state('bank_account_selection', {
    run: () => {

        menu.con(menuService.getMenu('payment_confirmation', 'generic'))

    },
    next: menuService.getMenuTags('payment_confirmation', 'generic')
});
menu.state('entry.bank_option', {
    run: async () => {
        let bank_data = await DbService.getBanks({where: {id: menu.val}})

        console.log('USSD: Happy: ' + JSON.stringify(bank_data));
        // let menu_ = 'NGN' + await menu.session.get('amount') + ' for Third Party Cover.'
        let menu_ = ` NGN ${await menu.session.get('amount')} for ${await menu.session.get('product_option')}`

        // menu.con(menuService.getMenu('payment_confirmation', 'generic'));
        menu.con(utilsService.parameterizedString(menuService.getMenu('payment_confirmation', 'generic'), menu_))
        menu.session.set('bank_account_selection', bank_data[0].id); //fetching bankaccount
        menu.session.set('bank_account_code', bank_data[0].code); //fetching bankaccount

    },
    next: menuService.getMenuTags('payment_confirmation', 'generic')
});
menu.state('payment_confirmation', {
    run: () => {

        console.log('USSD: Happy');
        menu.con(menuService.getMenu('payment_confirmation', 'generic'));
        // menu.session.set('bank_account_selection', menu.val); //fetching bankaccount

    },

    next: menuService.getMenuTags('payment_confirmation', 'generic')

});
menu.state('payment_other_no', {
    run: () => {
        //entering another number for payment
        menu.con(menuService.getMenu('payment_other_no', '8'))

    },

    next: menuService.getMenuTags('complete_payment', '9')

});

menu.state('complete_payment', {
    run: () => {
        menu.session.set('payment_other_no', menu.val);
        //using the default phone number
        iniatiatePayment(function (err, dial_ussd) {
            if (err) {
                menu.end(dial_ussd)

            } else {
                console.log('USSD: ' + JSON.stringify(dial_ussd));

                menu.end('Your reference number is MC-' + Date.now() + '. To complete this transaction dial ' + dial_ussd + ' and follow the instructions')
            }
        })

    },

});


menu.state('payment_current_no', {
    run: () => {
        console.log('USSD: here');
        iniatiatePayment(function (err, dial_ussd) {
            console.log('USSD: ' + JSON.stringify(dial_ussd));
            menu.end('Your refernce number is MC-' + Date.now() + '. To complete this transaction dial ' + dial_ussd + ' and follow the instructions')
        })

    },

});
/*menu.state('payment_current_no', {
    run: () => {
        iniatiatePayment(function (dial_ussd) {
            console.log('USSD: ' + JSON.stringify(dial_ussd))
            menu.end('Please dial ' + dial_ussd + ' to complete the payment')
        })
        // Ussd_charge();//initiating payment

    },
    next: menuService.getMenuTags('payment_confirmation', 'generic')
});*/

menu.state('family_risk_plan', {
    run: async () => {
        menu.con(menuService.getMenu('family_risk_plan', '4'))
        let menu_data = menuService.getMenuData('invest', '3')

        let product = menu_data.product_data[menu.val - 1].name
        menu.session.set('product_option', await menu.session.get('product_option') + ' - ' + product);

        console.log('family_risk_plan Prdocut data: ' + await menu.session.get('product_option'))
    },
    next: menuService.getMenuTags('family_risk_plan', '4')
});
menu.state('invest_beneficiary_name', {
    run: async () => {
        menu.con(menuService.getMenu('invest_beneficiary_name', '5'))
        let menu_data = await menuService.getMenuData('family_risk_plan', '4')
        let amount = await menu_data.product_data[menu.val - 1].amount
        let product = await menu_data.product_data[menu.val - 1].name
        menu.session.set('amount', amount);
        menu.session.set('product_option', await menu.session.get('product_option') + ' - ' + product);

        console.log('Prdocut data invest_beneficiary_name: ' + await menu.session.get('product_option'))
        console.log('Prdocut data invest_beneficiary_name: ' + await menu.session.get('amount'))
    },
    next: menuService.getMenuTags('invest_beneficiary_name', '5')
});
menu.state('make_claim', {
    run: () => {
        menu.con(menuService.getMenu('make_claim', '4'))
    },
    next: menuService.getMenuTags('make_claim', '4')
});
menu.state('make_complaint', {
    run: () => {
        menu.con(menuService.getMenu('make_complaint', '4'))
    },
    next: menuService.getMenuTags('make_complaint', '4')
});
menu.state('submit_complaint', {
    run: () => {
        menu.con(menuService.getMenu('submit_complaint_success', 'generic'))
    },
    next: menuService.getMenuTags('submit_complaint_success', 'generic')
});
menu.state('submit_complaint_success', {
    run: () => {
        menu.con(menuService.getMenu('make_complaint', '4'))
    },
    next: menuService.getMenuTags('make_complaint', '4')
});
menu.state('cover_renewals', {
    run: () => {
        menu.con(menuService.getMenu('cover_renewals', '4'))
    },
    next: menuService.getMenuTags('cover_renewals', '4')
});
menu.state('enter_claim_policy_no', {
    run: () => {
        menu.con(menuService.getMenu('enter_claim_policy_no', '5'))
    },
    next: menuService.getMenuTags('enter_claim_policy_no', '5')
});
menu.state('enter_renewal_policy_no', {
    run: () => {
        menu.con(menuService.getMenu('enter_renewal_policy_no', '5'))
    },
    next: menuService.getMenuTags('enter_renewal_policy_no', '5')
});
menu.state('beneficiary_management', {
    run: () => {
        menu.con(menuService.getMenu('beneficiary_management', '4'))
    },
    next: menuService.getMenuTags('beneficiary_management', '4')
});
menu.state('beneficiary_action', {
    run: () => {
        menu.con(menuService.getMenu('beneficiary_action', '5'))
    },
    next: menuService.getMenuTags('beneficiary_action', '5')
});
menu.state('add_beneficiary', {
    run: () => {
        menu.con(menuService.getMenu('add_beneficiary', '6'))
    },
    next: menuService.getMenuTags('add_beneficiary', '6')
});
menu.state('remove_beneficiary', {
    run: () => {
        menu.con(menuService.getMenu('remove_beneficiary', '6'))
    },
    next: menuService.getMenuTags('remove_beneficiary', '6')
});
menu.state('request_beneficiary_removal', {
    run: () => {
        menu.con(menuService.getMenu('request_beneficiary_removal', '7'))
    },
    next: menuService.getMenuTags('request_beneficiary_removal', '7')
});
menu.state('confirm_beneficiary_removal', {
    run: () => {
        menu.con(menuService.getMenu('confirm_beneficiary_removal', 'generic'))
    },
    next: menuService.getMenuTags('confirm_beneficiary_removal', 'generic')
});
menu.state('entry.beneficiary_national_name', {
    run: () => {
        // todo pick the name
        menu.con(menuService.getMenu('beneficiary_national_id', '6'))
    },
    next: menuService.getMenuTags('beneficiary_national_id', '6')
});
menu.state('entry.beneficiary_national_id', {
    run: () => {
        // todo pick the name
        menu.con(menuService.getMenu('request_beneficiary_addition', '6'))
    },
    next: menuService.getMenuTags('request_beneficiary_addition', '6')
});
menu.state('confirm_beneficiary_addition', {
    run: () => {
        menu.con(menuService.getMenu('confirm_beneficiary_addition', 'generic'))
    },
    next: menuService.getMenuTags('confirm_beneficiary_addition', 'generic')
});
menu.state('cancel_beneficiary_addition', {
    run: () => {
        menu.con(menuService.getMenu('beneficiary_action', '5'))
    },
    next: menuService.getMenuTags('beneficiary_action', '5')
});
menu.state('cancel_beneficiary_removal', {
    run: () => {
        menu.con(menuService.getMenu('beneficiary_action', '5'))
    },
    next: menuService.getMenuTags('beneficiary_action', '5')
});
menu.state('entry.claim_policy_no', {
    run: () => {
        menu.con(menuService.getMenu('claim_policy_no', '5'))
    },
    next: menuService.getMenuTags('claim_policy_no', '5')
});
menu.state('entry.renewal_policy_no', {
    run: () => {
        menu.con(menuService.getMenu('renewal_policy_no', '5'))
    },
    next: menuService.getMenuTags('renewal_policy_no', '5')
});
menu.state('payment_success', {
    run: () => {
        menu.end(menuService.getMenu('payment_success', 'generic'))
    }
});
menu.state('complete_make_claim', {
    run: () => {
        menu.end(menuService.getMenu('complete_make_claim', 'generic'))
    }
});
menu.state('request_callback', {
    run: () => {
        menu.end(menuService.getMenu('request_callback', 'generic'))
    }
});

//fetching phonenumber for
// function fetchphoneNumber(phoneTrans_number,{

// })

//flutterwave Integration

async function iniatiatePayment(callback) {

    let _phone_number = '',
        _bank_account_selection = '';
    // _policy_reg_no = '',
    // _bank_subcategory = '';

    menu.session.get('payment_other_no')
        .then(payment_other_no => {
            _phone_number = payment_other_no; //fetching other phone number
        }).then(payment_other_no => { //fetching current phone number
        _phone_number = menu.args.phoneNumber;
    });

    menu.session.get('bank_account_selection')
        .then(bank_account_selection => {
            _bank_account_selection = bank_account_selection; //fetching bank_selected
        });

    let params = {
        "tx_ref": "MC-" + Date.now(),
        "account_bank": await menu.session.get('bank_account_code'),
        "amount": await menu.session.get('amount'),
        "currency": "NGN",
        "email": "<EMAIL>",
        "phone_number": _phone_number,
        "fullname": await menu.session.get('fullname')
    };

    try {

        console.log('USSD: here p' + JSON.stringify(params));
        let response = await axios.post(process.env.FLUTTERWAVE_URL, params, {
            headers: {
                'Authorization': `Bearer ${process.env.FLUTTERWAVE_SECRET_KEY}`,
                'content-type': 'application/json'
            },
        });
        console.log('payment initiated json: ' + response);
        console.log('payment initiated: ' + response.data.meta.authorization.note);
        console.log('phonenumber*****************' + _phone_number + '****');

        let db_params = {
            reference_no_internal: "MC-" + Date.now(),
            reference_no_external: null,
            amount: await menu.session.get('amount'),
            user_id: await menu.session.get('user_id'),
            currency_id: 1,
            channel_id: 1,
            bank_id: await menu.session.get('bank_account_selection'),
            payment_vendor_id: 1,
            product_id: 1,
            product_option: await menu.session.get('product_option'),
            policy_reference_number: await menu.session.get('policy_reference_number'),
            session_id: await menu.session.get('session_id'),
            payment_status_id: 1,
            paying_phone_number: menu.args.phoneNumber,
            payment_request_time: Date.now(),
            payment_payload_requested: JSON.stringify(params),
            pas_sync_status: 'PENDING',
        }

        console.log('saving trans data: ' + JSON.stringify(db_params));
        ProductSaleService.saveSaleInitialRequest(db_params, function (err, save_response) {
            //displaying the ussd code for the customer to complete payment

            if (err) {
                console.log('Error saving trans: ' + save_response.system_message);
                callback(true, save_response.user_message)
            } else {

                callback(null, response.data.meta.authorization.note);
                let message = 'Your reference number is ' + params.tx_ref + '. To complete this transaction dial ' + response.data.meta.authorization.note + ' and follow the instructions';
                sendSMs.sendPaymentSms(_phone_number, message);

            }
        })
        // callback(null, 'Saved')
    } catch (err) {
        console.log('Error: ' + JSON.stringify(err));
        console.log(err.response)
        // catches errors both in fetch and response.json alert(err); 
    }
}

function getKYCDetails(last_name) {
    menu.session.set('lastname', last_name);
    menu.session.get('ref').then(ref => {
        menu.session.get('firstname').then(first_name => {
            identity.getUser({
                ref: '10000000001',
                firstname: first_name,
                lastname: last_name

            }, function (err, response) {

                console.log('KYC ' + JSON.stringify(response.data.fieldMatches));
                if (response) {

                    let params = {
                        sur_name: response.data.lastname,
                        last_name: response.data.lastname,
                        first_name: response.data.firstname,
                        full_name: response.data.firstname + ' ' + response.data.lastname,
                        registration_document_type_id: 1,
                        registration_no: response.data.nin,
                        registration_status: 'COMPLETE',
                        registration_status_desc: JSON.stringify(response.data.fieldMatches),
                        gender: response.data.gender,
                        phone_no: menu.args.phoneNumber,
                        user_confirmed_details: 1,
                        existing_user: 0
                    };

                    menu.con(utilsService.parameterizedString(menuService.getMenu('welcome', '2'), params.full_name))
                    menu.session.set('user_onboard_data', params);
                    /*identityService.createUser(params, function (err, response) {
                        if (err){
                            console.log('createUser ' + JSON.stringify(response));
                            menu.end('An error occurred executing your request, please try again.')
                        }
                        else
                            menu.con(utilsService.parameterizedString(menuService.getMenu('welcome', '2'), params.full_name))

                    })*/
                } else {
                    menu.end(menuService.getMenu('update_kyc', '2'))
                }

            })
        })
    })
}

function rulesPassed(received_data) {
    return received_data.data.phone === menu.args.phoneNumber && received_data.data.fieldMatches.lastname
    /*  let resp = false;
      if(received_data.data.phone===menu.args.phoneNumber)
          resp =true
      if(received_data.data.fieldMatches.lastname)
          resp =true*/
}

router.post('/ussd/user', (req, res) => {
    processUssd(req, res)
    if (req.body.phoneNumber === '0700410593') // For test purposes
        processUssd(req, res);//2348068430299//2348099361568
    else {
        var token = req.headers['x-access-token'];
        // var tk = passport.authenticate('jwt', {session: false})
        // console.log('TK: ' + tk)
        if (!token) return res.status(401).send({
            auth: false,
            message: 'No token provided.'
        });

        jwt.verify(token, jwtOptions.secretOrKey, function (err, decoded) {
            if (err)
                return res.status(403).send({
                    auth: false,
                    message: 'Failed to authenticate token.'
                });

            console.log('decoded.id: ' + decoded.id);
            processUssd(req, res);

        });
    }

});
router.post('/ussd', (req, res) => {
    processUssd(req, res);
});

router.post('/callback', (req, res) => {
    FlutterWaveService.processFlutterWaveCallback(req,res)
   /* let params = {
        reference_no_internal: req.body.ref,
        pas_sync_status: req.body.status,
    }
    FlutterWaveService.receiveSaleData(params, function (err, resp) {
        if (err) {
            res.send(resp)
        } else {
            res.send(resp.user_message)
        }
    })*/


});
router.post('/callback/init', (req, res) => {
    console.log('Data to persist: '+JSON.stringify(req.body))
    res.send("Ok")
    FlutterWaveService.resendHooks();
   /* let params = {
        reference_no_internal: req.body.ref,
        pas_sync_status: req.body.status,
    }
    FlutterWaveService.receiveSaleData(params, function (err, resp) {
        if (err) {
            res.send(resp)
        } else {
            res.send(resp.user_message)
        }
    })*/


});


function processUssd(req, res) {

    // console.log("args: ------>");
    let args = {
        phoneNumber: req.body.phoneNumber,
        sessionId: req.body.sessionId,
        serviceCode: req.body.serviceCode,
        text: req.body.text
    };

    // console.log("ND USSD args before: " + JSON.stringify(req.body));
    args.text = ussdRouter.ussdRouter(req.body.text);
    level_string = args.text;
    console.log("args: level_string: " + level_string);
    // sessionService.updateSession(args)
    // level_string = req.body.text;
    try {
        menu.run(args, resMsg => {
            // console.log("response: " + resMsg);
            res.send(resMsg);
        });
    } catch (e) {
        menu.go('error');
        console.log('Menu Error: ' + e)

    }
}

/**
 * USSD aggregators getting access token to integrate with our endpoints
 * */
router.post('/get-token', (req, res) => {

    let user_name = req.body.username;
    let password = req.body.password;
    if (user_name && password) {
        db.access_users.findOne({
            where: {
                username: user_name
            }
        }).then(user => {
            if (!user) {
                return res.status(200).json({
                    status: false,
                    message: 'U1 Username or Password is incorrect',
                    errorCode: "20_1_1"
                })
            }
            var passwordIsValid = bcrypt.compareSync(req.body.password, user.password_hash);
            console.log('Ps: ' + passwordIsValid);
            if (passwordIsValid) {
                // from now on we'll identify the user by the id and the id is the
                // only personalized value that goes into our token
                /* let payload = {id: user.id};
                     var token = jwt.sign(payload, jwtOptions.secretOrKey, {
                         expiresIn: 10 // expires in 24 hours
                     });
                     res.status(200).send({auth: true, token: token})
         */
                let payload = {
                    id: user.id
                };
                var token = jwt.sign(payload, jwtOptions.secretOrKey, {
                    expiresIn: JWT_EXPIRY_SECS // expires in 24 hours
                });
                res.status(200).send({
                    status: true,
                    accessToken: token
                });

            } else {
                res.status(200).json({
                    status: false,
                    message: 'P1 Username or Password is incorrect',
                    errorCode: "20_1_2"
                })
            }
        }).catch(err => {
            res.status(200).send({
                status: false,
                message: 'There was a problem get the token',
                errorCode: "20_1_3"
            });
            console.log('There was a problem getting token. ' + err)

        })
    } else
        res.status(200).json({
            status: false,
            message: 'U Username or Password is incorrect',
            errorCode: "20_1_4"
        })

});

router.post('/ussd-test', (req, res) => {
    let identity = require('../../middleware/ng/UserIdentityService');
    identity.getUser(req.body, function (response, resp_msg) {
        if (response) {
            res.status(200).json(resp_msg)
        } else {
            res.status(200).json(resp_msg)
        }

    })
});
router.post('/update-user', (req, res) => {
    let user_name = req.body.username;
    let password = req.body.password;
    if (user_name && password) {
        db.access_users.findOne({
            where: {
                username: user_name
            }
        }).then(user => {
            if (!user) {
                return res.status(200).json({
                    status: false,
                    message: 'U Username or Password is incorrect',
                    errorCode: "20_2_1"
                })
            }

            bcrypt.hash(password, 8, function (err, hashed) {
                // console.log('Hash: ' + hashed +' user.id: '+user.id)
                let params = {
                    password_hash: hashed
                };

                // console.log('Hash: ' + params.password_hash +' user.id: '+user.id)
                db.access_users.update(params, {
                    where: {
                        id: user.id
                    }
                }).then(result => {
                    if (!result)
                        return res.status(200).send({
                            status: false,
                            message: 'There was a problem saving user.',
                            errorCode: "20_2_2"
                        });
                    // create a token

                    console.log('Hash: ' + hashed + ' user.id: ' + user.id);
                    let payload = {
                        id: user.id
                    };
                    var token = jwt.sign(payload, jwtOptions.secretOrKey, {
                        expiresIn: JWT_EXPIRY_SECS // expires in 24 hours
                    });
                    res.status(200).send({
                        status: true,
                        accessToken: token
                    })
                    // res.json({ user_name, msg: 'account created successfully' })
                }).catch(err => {
                    res.status(200).send({
                        status: false,
                        message: 'There was a problem saving user.',
                        errorCode: "20_2_3"
                    });
                    console.log('There was a problem registering the user. ' + err)
                });
            });

        });


    } else {

        res.status(200).json({
            status: false,
            message: 'U Username or Password is incorrect',
            errorCode: "20_2_4"
        })
    }
});


let sessions = {};
menu.sessionConfig({
    start: function (sessionId, key) {
        return new Promise((resolve, reject) => {
            if (!(sessionId in sessions)) sessions[sessionId] = {};
            resolve();
        });
    },
    set: (sessionId, key, value, callback) => {
        // store key-value pair in current session
        return new Promise((resolve, reject) => {
            sessions[sessionId][key] = value;
            callback();
        });
    },
    get: function (sessionId, key) {
        return new Promise((resolve, reject) => {
            let value = sessions[sessionId][key];
            resolve(value);
        });
    },
    end: function (sessionId, callback) {
        // clear current session
        // this is called by menu.end()
        return new Promise((resolve, reject) => {
            delete sessions[sessionId];
            callback();
        });
    }
});


const createUser = async ({
                              user_name,
                              password,
                              full_name
                          }) => {
    return await AccessUser.create({
        user_name,
        password,
        full_name
    });
};

const updateUser = async ({
                              user_name,
                              password
                          }) => {
    return await AccessUser.update({
        user_name,
        password
    });
};


const getUser = async obj => {
    return await AccessUser.findOne({
        where: obj,
    });
};

module.exports = router;