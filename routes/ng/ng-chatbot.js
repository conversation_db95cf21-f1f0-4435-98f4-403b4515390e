var express = require('express');
var router = express.Router();
let verificationService = require('../../services/ng/WebhookVerifyService.js')
let messageService = require('../../services/ng/WebhookMessageService.js')

/* GET users listing. */
router.get('/', function(req, res, next) {

    let resp = verificationService.webhookVerify(req,res)
    res.status(200).send(resp);

});

router.post('/', function(req, res, next) {
    messageService.webhookMessages(req,res);

});

module.exports = router;