const request = require('request');
const env = process.env.NODE_ENV || 'development';
const config = require(__dirname + '/../config/config.json');
var selfservice = require("/services/SelfService-API");


module.exports = {

    API_Function: API_Function,
    SMS_Function: SMS_Function


}


function API_Function(phone_number, line_of_business, request_category, source_channel, policy_reg_no, bank_subcategory) {


    var options = {
        'method': 'POST',
        'url': "https://uapom.uapoldmutual.com/selfservice/apps/tickets",
        'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': 'ci_session=s64e0n487l5k149g8bfr9v6mn5jfiibk'
        },
        form: form_builder(phone_number, line_of_business, request_category, source_channel, policy_reg_no, bank_subcategory)
    };


    process_request(options);


}


function SMS_Function(phone_number, option) {


    var options = {
        'method': 'POST',
        'url': "https://uapom.uapoldmutual.com/selfservice/apps/sms",
        'headers': {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        form: {
            'phone_number': phone_number,
            'option': option
        }
    };


    process_request(options);


}


function form_builder(phone_number, line_of_business, request_category, source_channel, policy_reg_no, bank_subcategory) {


    const form = {}; // or "const valueToPush = new Object();" which is the same
    form["phone_number"] = phone_number;
    form["request_category"] = request_category;
    form["source_channel"] = source_channel;


    if (line_of_business) {
        form["line_of_business"] = line_of_business;

    }


    if (bank_subcategory) {
        form["bank_subcategory"] = bank_subcategory;

    }


    if (policy_reg_no) {
        form["policy_reg_no"] = policy_reg_no;


    }

    console.log(form);

    return form;

}

function process_request(options) {
    try {
        request(options, function (error, response) {
            // console.log(response.body);
            if (error) {
                process.on('uncaughtException', handleErrors);

            }
            return response.body;

        });
    } catch (e) {
        console.log('Error', e);
    }

}


function handleErrors(e) {
    if (!e.errorCode) {
        console.log('Unknown exception occurred');
        process.exit(1)
    }

    console.log("ECODE-" + e.errorCode + ": " + e.message)
    process.exit(e.errorCode)
}
