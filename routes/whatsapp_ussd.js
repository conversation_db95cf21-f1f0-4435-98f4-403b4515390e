"use strict";
var express = require('express');
var menuService = require('../services/MenuService');
var sessionService = require('../services/SessionService');

var selfservice = require("../services/SelfService-API");
var router = express.Router();
let level_string = '';
const UssdMenu = require('ussd-menu-builder');
let menu = new UssdMenu();
const ussdRouter = require('ussd-router');

// Define ussd_menu.js states
menu.startState({

    run: () => {
        // use ussd_menu.js.con() to send response without terminating session
        menu.session.set('request_category', '')
        menu.session.set('line_of_business', '')
        menu.session.set('policy_reg_no', '')
        menu.session.set('bank_subcategory', '');
        menu.con(menuService.getMenu('start', 'first_level'))
    },
    next: menuService.getMenuTags('start', 'first_level')
});

/**
 * SECOND LEVEL MENUS
 * */

menu.state('register_claim', {
    run: () => {
        menu.session.set('request_category', 1);
        menu.con(menuService.getMenu('register_claim', 'second_level'))
    },
    next: menuService.getMenuTags('register_claim', 'second_level')
});
menu.state('policy_status', {
    run: () => {

        menu.session.set('request_category', 2);
        menu.con(menuService.getMenu('policy_status', 'second_level'))
    },
    next: menuService.getMenuTags('policy_status', 'second_level')
});
menu.state('buy_insurance', {
    run: () => {
        menu.session.set('request_category', 3);
        menu.con(menuService.getMenu('buy_insurance', 'second_level'))
    },
    next: menuService.getMenuTags('buy_insurance', 'second_level')
});
menu.state('make_insurance_payment', {
    run: () => {
        menu.session.set('request_category', 4);
        menu.con(menuService.getMenu('make_insurance_payment', 'second_level'))
    },
    next: menuService.getMenuTags('make_insurance_payment', 'second_level')
});
menu.state('invest_with_us', {
    run: () => {
        menu.session.set('request_category', 5);
        // menu.go('success_end_message');
        menu.con(menuService.getMenu('invest_with_us', 'second_level'))
        const resp = selfservice.API_Function(menu.args.phoneNumber, "", 5, 1, "", "");

    },
    next: menuService.getMenuTags('invest_with_us', 'second_level')
});
menu.state('bank_with_us', {
    run: () => {
        menu.session.set('request_category', 6);
        menu.con(menuService.getMenu('bank_with_us', 'second_level'))
    },
    next: menuService.getMenuTags('bank_with_us', 'second_level')
});
menu.state('covid', {
    run: () => {
        menu.session.set('request_category', 7);
        menu.con(menuService.getMenu('covid', 'second_level'))
    },
    next: menuService.getMenuTags('covid', 'second_level')
});
/**
 * ---END SECOND LEVEL MENUS
 * */

// THIRD LEVEL MENUS

menu.state('claim_options', {
    run: () => {
        menu.session.set('line_of_business', level_string.charAt(level_string.length - 1));
        menu.con(menuService.getMenu('claim_options', 'third_level'))
    },
    next: menuService.getMenuTags('claim_options', 'third_level')
});
menu.state('policy_status_options', {
    run: () => {
        menu.session.set('line_of_business', level_string.charAt(level_string.length - 1));
        menu.con(menuService.getMenu('policy_status_options', 'third_level'))
    },
    next: menuService.getMenuTags('policy_status_options', 'third_level')
});
menu.state('buy_insurance_options', {
    run: () => {
        menu.session.set('line_of_business', level_string.charAt(level_string.length - 1));
        menu.go('success_end_message')
    },
    next: menuService.getMenuTags('buy_insurance_options', 'third_level')
});
menu.state('make_insurance_payment_options', {
    run: () => {
        menu.session.set('line_of_business', level_string.charAt(level_string.length - 1));
        menu.con(menuService.getMenu('make_insurance_payment_options', 'third_level'))
    },
    next: menuService.getMenuTags('make_insurance_payment_options', 'third_level')
    /*next: {

        // using regex to match user input to next state
        // '*\\w+': 'make_insurance_payment_options.policy_no'
    }*/
});

menu.state('bank_with_us_option', {
    run: () => {
        menu.session.set('bank_subcategory', (menu.args.text).charAt(level_string.length - 1));
        menu.go('success_end_message');
    }
});
menu.state('provide_details', {
    run: () => {
        let i = level_string.charAt(level_string.length - 3)
        console.log('---> Details: '+ i)
        if (i == 1)
            menu.con(menuService.getMenu('provide_details_health', 'fourth_level'));
        else if (i == 2)
            menu.con(menuService.getMenu('provide_details_car', 'fourth_level'));
        else if (i == 3)
            menu.con(menuService.getMenu('provide_details_life', 'fourth_level'));
        else if (i == 4)
            menu.con(menuService.getMenu('provide_details_other', 'fourth_level'));

    },
    next: {
        // using regex to match user input to next state
        '*\\w+': 'provide_details.policy_no'
    }
});
menu.state('make_insurance_payment_options.policy_no', {
    run: () => {
        try {
            menu.session.set('bank_subcategory', menu.val);
            menu.go('success_end_message')
        }
        catch (e) {
            menu.go('error');
            console.log('make_insurance_payment_options: User Entry Error: ' + e)
        }
    }
});

// GENERIC STEPS

menu.state('response_options', {
    run: () => {
        menu.con(menuService.getMenu('response_options', 'generic'));
    },
    next: {
        // using regex to match user input to next state
        '*\\s+': 'provide_details.policy_no'
    }
});
menu.state('provide_details.policy_no', {
    run: () => {
        try {
            menu.session.set('policy_reg_no', menu.val);
            menu.go('success_end_message')
        } catch (e) {
            menu.go('error');
            console.log('provide_details: User Entry Error: ' + e)
        }
    }
});


menu.state('request_callback', {
    run: () => {
        //invoke api
        // menu.end(menuService.getMenu('success_end_message', 'generic'));
        menu.go('success_end_message')
    }
});
menu.state('covid_update_message', {
    run: () => {
        // todo call SMS API
        let i = level_string.charAt(level_string.length - 1)
        console.log('-----> TO SEND COVID...' + menu.args.phoneNumber + ' Option: ' + i);

        menu.con(menuService.getMenu('covid_menu', 'generic') + ' ' + menuService.getMenuByIndex('covid_menu', 'generic', i));
        const resp = selfservice.SMS_Function(menu.args.phoneNumber, i);
    }
});
menu.state('success_end_message', {
    run: () => {
        //invoke api
        try {
            let _line_of_business = '',
                _request_category = '',
                _policy_reg_no = '',
                _bank_subcategory = '';

            menu.session.get('line_of_business')
                .then(line_of_business => {
                    _line_of_business = line_of_business;
                });
            menu.session.get('request_category')
                .then(request_category => {
                    _request_category = request_category;
                });
            menu.session.get('policy_reg_no')
                .then(policy_reg_no => {
                    _policy_reg_no = policy_reg_no;
                });
            menu.session.get('bank_subcategory')
                .then(bank_subcategory => {
                    _bank_subcategory = bank_subcategory;
                    // console.log('-----> TO SEND...' + menu.args.phoneNumber + ' =_line_of_business= ' + _line_of_business + ' =_request_category= ' + _request_category + ' =_policy_reg_no= ' + _policy_reg_no + ' =bank_subcategory= ' + _bank_subcategory)
                    const resp = selfservice.API_Function(menu.args.phoneNumber, _line_of_business, _request_category, 1, _policy_reg_no, bank_subcategory,false);
                    menu.con(menuService.getMenu('success_end_message', 'generic'));

                })
        } catch (e) {
            menu.go('error');
            console.log('Path Error: ' + e)
        }


    }

});


menu.state('back_to_main', {
    run: () => {
        menu.goStart();
    }
});

menu.state('exit', {
    run: () => {
        menu.end();
    }
});

menu.state('error',  {
    // handle errors
    run: () => {
        menu.end('An error occurred processing your request, please try again.')
    }
});

let sessions = {};
menu.sessionConfig({
    start: function (sessionId, key) {
        return new Promise((resolve, reject) => {
            if (!(sessionId in sessions)) sessions[sessionId] = {};
            resolve();
        });
    },
    set: (sessionId, key, value, callback) => {
        // store key-value pair in current session
        return new Promise((resolve, reject) => {
            sessions[sessionId][key] = value;
            callback();
        });
    },
    get: function (sessionId, key) {
        return new Promise((resolve, reject) => {
            let value = sessions[sessionId][key];
            resolve(value);
        });
    },
    end: function (sessionId, callback) {
        // clear current session
        // this is called by menu.end()
        return new Promise((resolve, reject) => {
            delete sessions[sessionId];
            callback();
        });
    }
});

router.post('/', (req, res) => {

    // console.log("args: ------>");
    let args = {
        phoneNumber: req.body.phoneNumber,
        sessionId: req.body.phoneNumber,
        serviceCode: 'whatsapp',
        text: req.body.text
    };

    sessionService.updateSession(args)
    // level_string = req.body.text;
    console.log("args before: " + JSON.stringify(req.body));
    args.text = ussdRouter.ussdRouter(req.body.text)
    level_string = args.text;
    // console.log("args: " + JSON.stringify(args)+' level_string: '+level_string);
    try {
        menu.run(args, resMsg => {
            // console.log("response: " + resMsg);
            res.send(resMsg);
        });
    } catch (e) {
        menu.go('error');
        console.log('Menu Error: ' + e)

    }

});

module.exports = router;
