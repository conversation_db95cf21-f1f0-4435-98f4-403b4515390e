var express = require('express');
var router = express.Router();
let verificationService = require('../../services/sadc/WebhookVerifyService.js')
let messageService = require('../../services/sadc/WebhookMessageService.js')

/* GET users listing. */
router.get('/test', function(req, res, next) {
    let resp = verificationService.webhookVerify(req,res)
    res.status(200).send(resp);

});
router.get('/na', function(req, res, next) {
    let resp = verificationService.webhookVerify(req,res)
    res.status(200).send(resp);

});
router.get('/sz', function(req, res, next) {
    let resp = verificationService.webhookVerify(req,res)
    res.status(200).send(resp);

});
router.get('/bw', function(req, res, next) {
    let resp = verificationService.webhookVerify(req,res)
    res.status(200).send(resp);

});
router.get('/mw', function(req, res, next) {
    let resp = verificationService.webhookVerify(req,res)
    res.status(200).send(resp);

});



router.post('/test', function(req, res, next) {
    messageService.webhookMessages(req,res,'te');
});
router.post('/na', function(req, res, next) {
    messageService.webhookMessages(req,res,'na');
});
router.post('/sz', function(req, res, next) {
    messageService.webhookMessages(req,res,'sz');

});
router.post('/bw', function(req, res, next) {
    messageService.webhookMessages(req,res,'bw');

});
router.post('/mw', function(req, res, next) {
    messageService.webhookMessages(req,res,'mw');

});

module.exports = router;