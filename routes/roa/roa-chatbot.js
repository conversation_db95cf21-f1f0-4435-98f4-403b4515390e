var express = require('express');
var router = express.Router();
let verificationService = require('../../services/roa_messenger/WebhookVerifyService.js')
let messageService = require('../../services/roa_messenger/WebhookMessageService.js')

/* GET users listing. */
router.get('/', function(req, res, next) {

    let resp = verificationService.webhookVerify(req,res)
    res.status(200).send(resp);

});

router.post('/dialogflow', function(req, res, next) {
    console.log('Incoming: '+JSON.stringify(req.body))
    res.status(200).send(req.body);
});

router.get('/:country_code', function(req, res, next) {

    let resp = verificationService.webhookVerify(req,res)
    res.status(200).send(resp);

});

router.post('/:country_code', function(req, res, next) {
    messageService.webhookMessages(req,res,req.params.country_code);
});

// Test routes

router.get('/te/:country_code', function(req, res, next) {

    let resp = verificationService.webhookVerify(req,res)
    res.status(200).send(resp);

});

router.post('/te/:country_code', function(req, res, next) {
    messageService.webhookMessages(req,res,req.params.country_code);
});

module.exports = router;