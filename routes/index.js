var express = require('express');
var router = express.Router();

var selfservice_api = require('../services/SelfService-API');

/* GET home page. */
router.get('/', function (req, res, next) {
    res.render('index', {title: 'Express'});
});


router.post('/selfservice_api', function (req, res) {


    const phone_number = req.body.phone_number;
    const line_of_business = req.body.line_of_business;
    const request_category = req.body.request_category;
    const source_channel = req.body.source_channel;
    const policy_reg_no = req.body.policy_reg_no;
    const bank_subcategory = req.body.bank_subcategory;
    const resp = selfservice_api.API_Function(phone_number, line_of_business, request_category, source_channel, policy_reg_no, bank_subcategory);

    res.send(resp);

});

router.post('/sms_api', function (req, res) {


    const phone_number = req.body.phone_number;
    const option = req.body.option;
    const resp = selfservice_api.SMS_Function(phone_number, option);

    res.send(resp);

});


module.exports = router;
